/**
 * 请求接口
 * @param {Array} apis 所有接口配置
 * @param {function} request 请求服务
 */
export function _requestApi(apis, request) {
  function Request(options, data, headers) {
    return new Promise((resolve, reject) => {
      request({
        url: options.url,
        method: options.method,
        params: options.method === 'get' ? data : '',
        data: data,
        headers: {
          // 'Content-Type': 'application/x-www-form-urlencoded',
          ...options.headers,
          ...headers
        },
        responseType: options.responseType || '',
        requestOptions: options
      }).then(
        (res) => {
          resolve(res)
        },
        (error) => {
          reject(error)
        }
      )
    })
  }

  return function _({ name, data, headers }) {
    if (Object.keys(apis).indexOf(name) === -1) {
      // action不在reqConfig配置中
      throw new SyntaxError(`请在reqConfig文件注册接口:  ${name}`)
    }
    const options = apis[name]
    if (options.method === 'GET') options.method = 'get'
    options.method = options.method || 'get'
    return new Promise(async(resolve) => {
      const res = await Request(options, data, headers)
      resolve(res)
    })
  }
}
