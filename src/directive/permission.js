import store from '@/store'

/**
 * 检查是否有权限
 * @param value 权限值，可以是字符串、数组或对象
 * @returns {boolean}
 */
function checkPermission(value) {
  if (value && typeof value === 'object' && value.length > 0) {
    const roles = store.getters && store.getters.roles
    const permissions = store.getters && store.getters.permissions

    const permissionValues = value
    let hasPermission = false

    hasPermission = roles.some(role => {
      return permissionValues.includes(role)
    })
    if (hasPermission) {return true}
    hasPermission = permissions.some(permission => {
      return permissionValues.includes(permission)
    })
    if (hasPermission) {return true}
    return false
  } else if (value && typeof value === 'string') {
    const permissions = store.getters && store.getters.permissions
    return permissions.includes(value)
  } else {
    console.error(`权限参数错误！示例: v-permission="['admin','editor']" 或 v-permission="'user:read'"`)
    return false
  }
}

export default {
  inserted(el, binding, vnode) {
    const { value } = binding

    if (value) {
      const hasPermission = checkPermission(value)

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`权限参数是必需的! 示例: v-permission="['admin','editor']"`)
    }
  }
}
