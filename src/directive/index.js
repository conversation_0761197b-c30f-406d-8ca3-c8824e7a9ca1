import Vue from 'vue'
import permission from './permission'

const LIMIT_REGEXP = /[^0-9.-]/g; // 定义限制用户输入的正则表达式

Vue.directive('limitInput', {
  // inserted(el) {
  //   el.addEventListener('input', (e) => {
  //     let value = e.target.value;
  //     // 将输入值转换为数字类型
  //     let temp = Number(value);
  //     if (isNaN(temp)) {
  //       temp = '';
  //     } else {
  //       // 将数字转换为指定格式的字符串
  //       temp = temp.toString().replace(/^(-?\d*)\.?(\d{0,2}).*$/, '$1.$2');
  //     }
  //     e.target.value = temp;
  //     e.target.dispatchEvent(new Event('input')); // 更新v-model绑定的值
  //   });

  //   // 使用 onBlur 事件处理值为空的情况
  //   el.addEventListener('blur', (e) => {
  //     if (e.target.value === '-') {
  //       e.target.value = '';
  //     }
  //   });
  // }
});

// 注册权限指令
Vue.directive('permission', permission)

