// sidebar
@menuText: rgba(0,0,0,.7);
@menuActiveText: rgb(64 158 255);
@subMenuActiveText: rgb(64 158 255);
@menuActiveBg: #e6f7ff;

@menuBg: #ffffff;
@menuHover: #ffffff;

@subMenuBg: #ffffff;
@subMenuHover: #ffffff;


// @menuText: rgba(0,0,0,.7);
// @menuActiveText: #ffffff;
// @subMenuActiveText: rgba(0,0,0,.7);;
// @menuActiveBg: rgb(64 158 255);

// @menuBg: transparent;
// @menuHover: transparent;

// @subMenuBg: transparent;
// @subMenuHover: rgb(64 158 255);


@sideBarWidth: 210px;

@boxShadow: 2px 0 8px 0 rgba(29,35,41,.1);

@mainBg:#f0f2f5;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: @menuText;
  menuActiveText: @menuActiveText;
  subMenuActiveText: @subMenuActiveText;
  menuActiveBg: @menuActiveBg;
  menuBg: @menuBg;
  menuHover: @menuHover;
  subMenuBg: @subMenuBg;
  subMenuHover: @subMenuHover;
  sideBarWidth: @sideBarWidth;
  boxShadow: @boxShadow;
  mainBg: @mainBg;
}
