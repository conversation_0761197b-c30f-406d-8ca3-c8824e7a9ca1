@import "./variables.less";
@import "./mixin.less";
@import "./transition.less";
@import "./element-ui.less";
@import "./sidebar.less";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  font-size: 13px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
p {
  margin: 0;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.clearfix {
  zoom: 1; /*为了兼容IE*/
}

.clearfix:after {
  content: ""; /*设置内容为空*/
  height: 0; /*高度为0*/
  line-height: 0; /*行高为0*/
  display: block; /*将文本转为块级元素*/
  visibility: hidden; /*将元素隐藏*/
  clear: both; /*清除浮动*/
}

.text-center {
  text-align: center;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: rgb(32, 160, 255);
  }
}

.ignore {
  // line-height: 24px;
  overflow: hidden; /*超出的部分隐藏起来*/
  white-space: nowrap; /*不显示的地方用省略号...代替*/
  text-overflow: ellipsis; /* 支持 IE */
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}

//超出一行省略
.line-clamp {
  // width: 100px;
  overflow: hidden;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  white-space: nowrap; /* 规定文本是否折行 */
  word-break: break-all;
}

//超出一行以上省略（默认2）
.line-clamp-over {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
}

.flex-align-center {
  display: flex;
  align-items: center;
}
.flex-row-start {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;
}
.flex-row-center {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: center;
}
.flex-row-end {
  display: flex;
  flex-flow: row nowrap;
  align-items: flex-end;
  justify-content: center;
}

/*-----------------------------------------------------------------*/

// 默认页面
.page-width-limit {
  min-width: 700px !important;
  // max-width: 1200px !important;
}

// 统一搜索盒子样式
.top-container {
  padding: 10px;
  background-color: #ffffff;
  margin-bottom: 4px;
  border-bottom: 1px solid #EBEEF5;
  min-width: 650px;
  .el-form-item {
    display: inline-block;
    margin: 0 20px 0px 0;
  }
  .el-form-item__content {
    display: inline-block;
  }
  .el-form-item__label {
    font-size: 14px;
    padding: 0px 5px 0 0;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #07090d;
  }
  .el-input {
    width: 160px;
  }
  .el-input__inner {
    color: #07090d;
  }
  .el-date-editor {
    width: 330px;
  }
  button {
    padding: 6px 24px;
  }
  .el-radio-button__inner {
    padding: 9px 15px;
  }

  .is-plain {
    background: #fff !important;
    border: 1px solid #117af7;
    color: #117af7;
  }
  .is-plain:hover {
    color: #117af7 !important;
    background: rgba(17, 122, 247, 0.1) !important;
  }
  .el-picker-panel,
  .el-date-range-picker,
  .el-popper,
  .has-time {
    z-index: 50 !important;
  }
  .el-button--primary {
    background-color: #117af7 !important;
  }
  .el-button--primary.is-plain:focus {
    color: #3a8ee6 !important;
  }
}

.grecaptcha-badge {
  display: none;
}

// 页面容器
.container-bg {
  background-color: #fff;
  box-shadow: @boxShadow;
  margin-bottom: 10px;
  padding: 20px 20px 10px;
  overflow: hidden;
  min-height: calc(100vh - 114px);
  // overflow-x: scroll;
}

// 常规列表页面容器
.list-container-bg {
  background-color: #fff;
  box-shadow: @boxShadow;
  margin-bottom: 10px;
  // padding: 20px 20px 10px;
  overflow: hidden;
  min-height: calc(100vh - 114px);
  // overflow-x: scroll;
}

// 表格操作栏
.action-class {
  background: #fff !important;
}

// 页面tabs
.page-tab {
  // margin-top: 18px;
  background: #ffffff;
  padding: 0 10px;
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    border-bottom: 1px solid #eeeeee;
    li {
      float: left;
      color: #1b1e23;
      font-size: 14px;
      margin-right: 40px;
      height: 42px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      line-height: 42px;
      cursor: pointer;
    }
    .active {
      color: #117af7;
      border-bottom: 3px solid #117af7;
    }
  }
}

// 页面中间盒子
.page-middle {
  background: #fff;
  padding: 10px 10px 0px 10px;
}

// 按钮统一样式
.button-wrapper,
.button-full-wrapper {
  color: #117af7 !important;
  background: #ffffff !important;
  font-size: 14px !important;
  border-radius: 4px !important;
  padding: 0 12px !important;
  height: 32px !important;
  border: 1px solid #117af7;
  .iconfont {
    margin-right: 6px !important;
    // font-size: unset!important;
  }
  .button-txt {
    line-height: 20px;
  }
}
.button-wrapper {
  border-color: #117af7 !important;
}
.button-wrapper:hover {
  color: #117af7 !important;
  background: rgba(17, 122, 247, 0.1) !important;
}
.button-full-wrapper {
  color: #ffffff !important;
  background: #117af7 !important;
}
.button-wrapper > :first-child,
.button-full-wrapper > :first-child {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0 !important;
  bottom: 0 !important;
}

// 图片背景自适应展示
.img-bg {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

// 上传盒子公共样式
.upload-box {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    overflow: hidden;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .upload-box-add {
    color: #8c939d;
    text-align: center;
    .el-icon-plus {
      font-size: 24px;
      margin-bottom: 10px;
    }
  }
  .upload-img {
    width: 100% !important;
    height: 100% !important;
  }
}

.el-tooltip__popper {
  max-width: 400px !important;
}

.formTable {
  display: table;
  border-collapse: collapse;
  margin-bottom: 20px;
  width: 100%;
  .row {
    display: table-row;
  }
   
  .header .cell {
    font-weight: bold;
    background-color: #ccc;
    padding:10px 10px;
    border: 1px solid #d0ddeb;
  }
  .label {
    font-weight: bold;
  }
  .cell {
    display: table-cell;
    // text-align: center;
    padding:10px 10px;
    vertical-align: middle; /* 子元素默认垂直居中 */
    border: 1px solid #d0ddeb;
    .cell-box{
      display: flex;
      align-items: center;
      justify-content: center;
    }
  } 
}

.el-button-text-red {
  color: red !important;
  &.is-disabled {
    color: rgb(247, 171, 171) !important;
  }
}
.el-button-text-black {
  color:#07090d !important;
  &.is-disabled {
    color: #ccc !important;
  }
}

.el-button-text-green {
  color:#67c23a !important;
  &.is-disabled {
    color: #cbf3b6 !important;
  }
}
.el-button-text-orange {
  color: #e6a23c !important;
  &.is-disabled {
    color: #f8d196 !important;
  }
}
.el-button-text-grey {
  color:#ccc !important;
  &.is-disabled {
    color: #e7dbdb !important;
  }
}
