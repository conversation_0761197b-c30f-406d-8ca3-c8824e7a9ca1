// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// .dialog-footer {
//   .el-button {
//     width: 150px;
//     margin: 0 20px;
//   }
// }

.message-zindex {
  z-index: 3000 !important;
}

.el-table,
.el-form-item__label,
.el-form-item__content,
.el-drawer__body,
.el-tree-node__label,
.el-checkbox__label {
  font-size: 13px !important;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

// .el-table {
//   min-height: 50vh;
// }

.el-dialog {
  top: 51%;
  transform: translateY(-50%);
  margin-top: 0 !important;
  // margin-top: -10vh !important;
  // min-width: 400px;

  .el-dialog__header {
    padding: 20px 20px 0;
    .el-dialog__title {
      font-size: 16px;
    }
  }

  .dialog-footer .el-button {
    width: 70px;
  }

  .dialog-footer-big {
    text-align: center;
    .el-button {
      width: 100px;
    }
  }
}

.el-drawer__header {
  margin-bottom: 0;
  font-size: 16px;
  color: inherit;
  padding: 15px 20px 10px;
}

.el-drawer__body {
  padding: 20px;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #409EFF;
}
.el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner {
  border-color: #409EFF;
}
.el-input__inner {
  color:#606266 !important;
}
.el-drawer__header {
  font-weight: bold !important;
}