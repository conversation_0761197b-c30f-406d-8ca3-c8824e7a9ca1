<template>
  <div :class="classObj" class="app-wrapper">
    <div class="main-top clearfix">
      <div class="fl" style="margin-top:6px;">
        <!-- <img src="" alt="公司logo"> -->
        <b style="font-size:16px">乙禾素食系统</b>
      </div>

      <hamburger
          :is-active="sidebar.opened"
          class="hamburger-container"
          @toggleClick="toggleSideBar"
      />
      <div class="fr">
        <span v-if="userinfo.types === 4" style="position: relative;top: -12px;margin-right:20px;" v-show="out_time">
          <span v-if="isOutTimeExpired" :class="{'blink': isOutTimeExpired}" style="color: red; background-color: yellow; padding: 20px 30px; border-radius: 30px;"> 【 ！！！人脸识别已过期！！！ 】 </span>
          <i :class="[refresh ? 'el-icon-refresh refreshIcon' : 'el-icon-refresh']" @click="GetFaceTaskList(1)" style="cursor: pointer;"></i>
          开票有效期:{{out_time}}
        </span>
        <el-button v-if="userinfo.types === 4" v-show="out_time" type="success" style="position: relative;top: -12px;margin-right:20px;" @click="PostTaskInsert" size="mini">进行人脸识别</el-button>
        <img src="@/assets/images/my.png" class="user-avatar">
        <span
            style="
            font-size: 14px;
            position: relative;
            top: -12px;
            color: #07090d;
            margin-right: 16px;
          "
        >欢迎，{{ username }}</span>
        <span
            class="logout"
            @mouseover="hoverIcon = true"
            @mouseout="hoverIcon = false"
            @click="logout"
        >
          <svg-icon
              class="logout-svg"
              :icon-class="hoverIcon === false ? 'logout_icon' : 'logout_hover'"
          />
          <span>退出</span>
        </span>
      </div>
    </div>

    <!-- <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    /> -->
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
      </div>
      <tags-view />
      <app-main />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Navbar, Sidebar, AppMain, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import Hamburger from '@/components/Hamburger'
import { requestApi } from '@/utils/request'

export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TagsView,
    Hamburger
  },
  mixins: [ResizeMixin],
  data() {
    return {
      hoverIcon: false,
      out_time: '',
      refresh: false
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar']),
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    username() {
      return (
          this.$store.state.user.username || localStorage.getItem('username')
      )
    },
    userinfo() {
      return (
          this.$store.state.user.userInfo || localStorage.getItem('userInfo')
      )
    },
    isOutTimeExpired() {
      if (!this.out_time) return false;
      const currentTime = new Date().getTime();
      const outTime = new Date(this.out_time).getTime();
      return outTime < currentTime
    }
  },
  created() {
    // this.GetFaceTaskList()
  },
  methods: {
    PostTaskInsert() {
      requestApi({
        name: 'PostTaskInsert',
        data: {
          task_code: "5001",
          user_id: this.$store.state.user.userInfo.user_id || '',
          task_data: {
            auto_mark: "5001",
            out_time: this.out_time,
          }
        }
      }).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
        }
      })
    },
    GetFaceTaskList(type) {
      this.refresh = !this.refresh
      requestApi({
        name: 'GetFaceTaskList'
      }).then(res => {
        console.log(res)
        if (res.code === 200) {
          this.out_time = res.data.length && res.data[0].task_data.out_time
          if (type == 1) {
            this.refresh = !this.refresh
            this.$message.success('更新成功')
          }
        }
      })
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', {withoutAnimation: false})
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      // await this.$store.dispatch('user/resetToken')
      location.reload() // 为了重新实例化vue-router对象以避免bug
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    }
  }
}
</script>

<style lang="less" scoped>
.refreshIcon {
  transform: rotate(360deg);
  transition: all 1s; // 可配置
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.blink {
  animation: blink 1s infinite;
}

@import '~@/styles/mixin.less';
@import '~@/styles/variables.less';

.app-wrapper {
@include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - @sideBarWidth);
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}

.hamburger-container {
  line-height: 26px;
  height: 100%;
  float: left;
  cursor: pointer;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;
  // &:hover {
  //   background: rgba(0, 0, 0, 0.025);
  // }
}

.main-top {
  height: 50px;
  background: #fff;
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;
  padding: 10px 20px;
  box-shadow: 0px 2px 6px 0px rgba(190, 198, 209, 0.41);

  img {
    width: 100px;
  }

  .title {
    color: #07090d;
    font-weight: 600;
    font-size: 16px;
    border-left: 1px solid #e3e5e8;
    position: relative;
    top: -10px;
    text-shadow: 0px 2px 6px rgba(190, 198, 209, 0.41);
    margin-left: 16px;
    padding-left: 16px;
  }

  .user-avatar {
    width: 34px;
    height: 34px;
    margin-right: 12px;
    margin-top: -3px;
  }

  .logout {
    font-size: 14px;
    position: relative;
    top: -8px;
    margin-left: 8px;
    cursor: pointer;

    span {
      position: relative;
      top: -4px;
    }

    &-svg {
      color: #1b1e23;
      width: 16px;
      height: 16px;
      position: relative;
      top: -3px;
    }
  }

  .logout:hover {
    span {
      color: #409eff;
    }
  }

  .logout-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
  }
}
</style>