<template>
  <el-dialog :title="title" :visible.sync="visible" @close="handleClose">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="策略类型" prop="type">
            <el-select
              v-model="formData.type"
              placeholder="请选择策略类型"
              :disabled="formData.id !== undefined"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单类型" prop="order_type">
            <el-select
              v-model="formData.order_type"
              placeholder="请选择订单类型"
              :disabled="formData.id !== undefined"
            >
              <el-option
                v-for="item in orderTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="策略名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入策略名称" />
      </el-form-item>

      <el-form-item label="策略描述" prop="description">
        <el-input
          type="textarea"
          v-model="formData.description"
          rows="4"
          placeholder="请输入策略描述"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生效时间" prop="start_time">
            <el-date-picker
              v-model="formData.start_time"
              type="datetime"
              placeholder="选择生效时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时效时间" prop="end_time">
            <el-date-picker
              v-model="formData.end_time"
              type="datetime"
              placeholder="选择结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="策略状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-divider />
      <h3 class="section-title">触发条件</h3>
      <el-form-item label="订单金额" prop="order_amount">
        <el-input-number
          v-model="formData.order_amount"
          :precision="2"
          :min="0"
          :step="1"
        />
        <span class="form-tip"
          >当订单金额大于设置值触发赠送，当为0时该条件不生效</span
        >
      </el-form-item>
      <el-form-item label="订单产品" prop="order_products">
        <el-select
          v-model="formData.order_products"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="请输入产品名称"
          :remote-method="handleOrderProductsSearch"
          :loading="orderProductsLoading"
        >
          <el-option
            v-for="item in orderProductsOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-divider />
      <h3 class="section-title">触发动作</h3>
      <el-form-item label="赠送金额" prop="gift_amount">
        <el-input-number
          v-model="formData.gift_amount"
          :precision="2"
          :min="0"
          :step="1"
        />
        <span class="form-tip">触发赠送规则后将给对应用户赠送设置的金额</span>
      </el-form-item>
      <el-form-item label="赠送产品" prop="gift_products">
        <el-select
          v-model="formData.gift_products"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="请输入产品名称"
          :remote-method="handleGiftProductsSearch"
          :loading="giftProductsLoading"
        >
          <el-option
            v-for="item in giftProductsOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import moment from "moment";
import productApi from "@/api/product";
import { requestApi } from "@/utils/request";

export default {
  name: "GiftStrategyDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        id: undefined,
        name: "",
        type: "order_gift",
        order_type: "reservation",
        description: "",
        start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
        end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
        status: 1,
        order_amount: 0,
        gift_amount: 0,
        order_products: [],
        gift_products: [],
      },
      typeOptions: [{ label: "基于订单赠送", value: "order_gift" }],
      orderTypeOptions: [
        { label: "预订订单", value: "reservation" },
        { label: "充值订单", value: "recharge" },
        { label: "直销订单", value: "direct_sale" },
      ],
      statusOptions: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 },
      ],
      rules: {
        name: [{ required: true, message: "请输入策略名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择策略类型", trigger: "change" },
        ],
        order_type: [
          { required: true, message: "请选择订单类型", trigger: "change" },
        ],
        start_time: [
          { required: true, message: "请选择生效时间", trigger: "change" },
        ],
        end_time: [
          { required: true, message: "请选择结束时间", trigger: "change" },
        ],
      },
      orderProductsOptions: [],
      giftProductsOptions: [],
      orderProductsLoading: false,
      giftProductsLoading: false,
    };
  },
  watch: {
    detail: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          // 处理基本数据
          this.formData = { ...this.formData, ...val };

          // 处理订单产品
          if (val.order_product_rels && val.order_product_rels.length > 0) {
            this.formData.order_products = val.order_product_rels.map(
              (item) => item.order_product_id
            );
            this.orderProductsOptions = val.order_product_rels.map((item) => ({
              id: item.order_product_id,
              name: item.name,
            }));
          }

          // 处理赠送产品
          if (val.gift_product_rels && val.gift_product_rels.length > 0) {
            this.formData.gift_products = val.gift_product_rels.map(
              (item) => item.gift_product_id
            );
            this.giftProductsOptions = val.gift_product_rels.map((item) => ({
              id: item.gift_product_id,
              name: item.name,
            }));
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    async handleOrderProductsSearch(query) {
      if (query) {
        this.orderProductsLoading = true;
        try {
          const res = await requestApi({
            name: "getProductsByName",
            data: { name: query },
          });
          this.orderProductsOptions = res.data || [];
        } catch (error) {
          console.error("搜索订单产品失败:", error);
        } finally {
          this.orderProductsLoading = false;
        }
      } else {
        this.orderProductsOptions = [];
      }
    },
    async handleGiftProductsSearch(query) {
      if (query) {
        this.giftProductsLoading = true;
        try {
          const res = await requestApi({
            name: "getProductsByName",
            data: { name: query },
          });
          this.giftProductsOptions = res.data || [];
        } catch (error) {
          console.error("搜索赠送产品失败:", error);
        } finally {
          this.giftProductsLoading = false;
        }
      } else {
        this.giftProductsOptions = [];
      }
    },
    resetForm() {
      this.formData = {
        id: undefined,
        name: "",
        type: "order_gift",
        order_type: "reservation",
        description: "",
        start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
        end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
        status: 1,
        order_amount: 0,
        gift_amount: 0,
        order_products: [],
        gift_products: [],
      };
      this.orderProductsOptions = [];
      this.giftProductsOptions = [];
    },
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.formData);
        }
      });
    },
    handleCancel() {
      this.$emit("update:visible", false);
    },
    handleClose() {
      this.$emit("update:visible", false);
      // 重置表单
      this.$refs.dataForm.resetFields();
      this.resetForm();
    },
  },
};
</script>

<style lang="less" scoped>
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.section-title {
  font-size: 16px;
  color: #303133;
  margin: 20px 0 15px 0;
  padding-left: 120px; /* 与表单项标签对齐 */
}
</style>
