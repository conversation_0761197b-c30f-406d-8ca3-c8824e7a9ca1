<template>
  <div class="product-list">
    <h1>策略列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.name"
            placeholder="策略名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.type"
            placeholder="策略类型"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd"
          >新增策略
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="序号" width="65" align="center" />
      <el-table-column prop="name" label="名称" align="center">
        <template slot-scope="scope">
          <div class="package-info">
            <!--            <el-avatar-->
            <!--                shape="square"-->
            <!--                size="medium"-->
            <!--                :src="row.imageUrl || defaultImage"-->
            <!--            ></el-avatar>-->
            <span class="ml-10">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120" align="center">
        <template slot-scope="scope">
          {{ $options.filters.typeFilter(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="scope" label="范围" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.scope === "product" ? "商品" : "订单" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="validity"
        label="有效期"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.start_time | formatDate }} 至
          {{ scope.row.end_time | formatDate }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)"
            >编辑</el-button
          >

          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>

          <el-button type="text" size="mini" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑策略弹窗 -->
    <gift-strategy-detail
      :visible.sync="dialogFormVisible"
      :title="dialogTitle"
      :detail="currentDetail"
      @submit="handleDetailSubmit"
    />
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";
import GiftStrategyDetail from "./gift-strategy-detail.vue";

export default {
  name: "GiftStrategyList",
  components: {
    GiftStrategyDetail,
  },
  filters: {
    typeFilter(type) {
      const typeMap = {
        pricing: "普通价格",
        discount: "折扣",
        full_reduction: "满减",
        member_price: "会员价",
        time_limited: "限时特价",
      };
      return typeMap[type] || "";
    },
    formatDate(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD");
    },
  },
  data() {
    return {
      searchForm: {
        name: "",
        type: "",
      },
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      dialogFormVisible: false,
      dialogTitle: "",
      currentDetail: {},
      typeOptions: [
        { label: "普通价格", value: "pricing" },
        { label: "折扣", value: "discount" },
        { label: "满减", value: "full_reduction" },
        { label: "会员价", value: "member_price" },
        { label: "限时特价", value: "time_limited" },
      ],
      defaultImage:
        "https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",
    };
  },
  created() {
    this.getOrderGiftStrategyList();
  },
  methods: {
    // 获取策略列表
    getOrderGiftStrategyList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      if (this.searchForm.type) {
        params.type = this.searchForm.type;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getOrderGiftStrategiesBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("API响应:", response.data.list);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取价格策略列表失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取价格策略列表失败", error);
          this.$message.error("获取价格策略列表失败");
        });
    },

    handleSearch() {
      this.pagination.page = 1;
      this.getOrderGiftStrategyList();
    },

    resetSearch() {
      this.searchForm = {
        name: "",
        type: "",
      };
      this.getOrderGiftStrategyList();
    },

    handleAdd() {
      this.dialogTitle = "新增赠送策略";
      this.currentDetail = {};
      this.dialogFormVisible = true;
    },

    handleEdit(row) {
      this.dialogTitle = "编辑价格策略";

      // 获取策略详情
      this.listLoading = true;
      requestApi({
        name: "getOrderGiftStrategy",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          this.listLoading = false;
          if (response && response.code === 200) {
            this.currentDetail = response.data;
            this.dialogFormVisible = true;
          } else {
            this.$message.error(response.message || "获取价格策略详情失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取价格策略详情失败", error);
          this.$message.error("获取价格策略详情失败");
        });
    },

    handleDelete(row) {
      this.$confirm("确认删除该价格策略?删除规则将连同赠送记录一并被删除，请慎重操作！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "deleteOrderGiftStrategy",
            data: {
              id: row.id,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getOrderGiftStrategyList(); // 刷新列表
              } else {
                this.$message.error(response.message || "删除价格策略失败");
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error("删除价格策略失败", error);
              this.$message.error("删除价格策略失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    toggleStatus(row) {
      // 切换状态: active <-> inactive
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${statusText}该价格策略?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "updateOrderGiftStrategyStatus",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: `价格策略已${statusText}!`,
                });
                row.status = newStatus; // 更新本地状态
                this.getOrderGiftStrategyList(); // 刷新列表
              } else {
                this.$message.error(
                  response.message || `${statusText}价格策略失败`
                );
              }
            })
            .catch((error) => {
              console.error(`${statusText}价格策略失败`, error);
              this.$message.error(`${statusText}价格策略失败`);
            })
            .finally(() => {
              this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    handleDetailSubmit(formData) {
      this.listLoading = true;

      // 处理提交数据
      const submitData = {
        ...formData,
        order_products: formData.order_products,
        gift_products: formData.gift_products,
      };

      if (formData.id !== undefined) {
        // 编辑策略
        requestApi({
          name: "updateOrderGiftStrategy",
          data: {
            id: formData.id,
            ...submitData,
          },
        })
          .then((response) => {
            this.listLoading = false;
            if (response && response.code === 200) {
              this.$message({
                type: "success",
                message: "赠送策略已更新!",
              });
              this.dialogFormVisible = false;
              this.getOrderGiftStrategyList(); // 刷新列表
            } else {
              this.$message.error(response.message || "更新赠送策略失败");
            }
          })
          .catch((error) => {
            this.listLoading = false;
            console.error("更新赠送策略失败", error);
            this.$message.error("更新赠送策略失败");
          });
      } else {
        // 新增策略
        requestApi({
          name: "addOrderGiftStrategy",
          data: submitData,
        })
          .then((response) => {
            this.listLoading = false;
            if (response && response.code === 200) {
              this.$message({
                type: "success",
                message: "新增赠送策略成功!",
              });
              this.dialogFormVisible = false;
              this.getOrderGiftStrategyList(); // 刷新列表
            } else {
              this.$message.error(response.message || "新增赠送策略失败");
            }
          })
          .catch((error) => {
            this.listLoading = false;
            console.error("新增赠送策略失败", error);
            this.$message.error("新增赠送策略失败");
          });
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getOrderGiftStrategyList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getOrderGiftStrategyList();
    },
  },
};
</script>

<style lang="less" scoped>
.product-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .package-info {
    display: flex;
    align-items: center;

    .ml-10 {
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .avatar-uploader {
    display: block;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;

    &:hover {
      border-color: #409eff;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }
  }

  .form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
