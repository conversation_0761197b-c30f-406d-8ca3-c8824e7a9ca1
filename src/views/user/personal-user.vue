<template>
  <div class="personal-account">
    <h1>个人用户列表</h1>

    <!-- 头部菜单 -->
    <div class="menu-type-container">
      <div class="search-container">
        <el-input
          v-model="searchForm.name"
          placeholder="用户姓名"
          size="mini"
          class="search-input"
        ></el-input>
        <el-input
          v-model="searchForm.phone"
          placeholder="手机号码"
          size="mini"
          class="search-input"
        ></el-input>
        <el-select
          v-model="searchForm.status"
          placeholder="用户状态"
          size="mini"
          class="search-input"
        >
          <el-option label="开启" :value="1"></el-option>
          <el-option label="禁用" :value="0"></el-option>
        </el-select>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询</el-button
        >
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置</el-button
        >
      </div>
      <div class="left-buttons">
        <el-button
          v-permission="['user:personal:create']"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增用户
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 个人列表 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="60" align="center">
      </el-table-column>
      <el-table-column prop="id" label="用户ID" width="130"></el-table-column>
      <el-table-column prop="real_name" label="用户姓名" width="100">
      </el-table-column>
      <el-table-column prop="phone" label="手机号码" width="120">
      </el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
      <el-table-column prop="status" label="用户状态" width="100">
        <template slot-scope="scope">
          <span
            :class="
              scope.row.status === 1 ? 'status-normal' : 'status-disabled'
            "
          >
            {{ scope.row.status === 1 ? "开启" : "禁用" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="150">
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="company"-->
      <!--        label="已绑定公司">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-tag v-if="scope.row.company" size="small" type="info">-->
      <!--            {{ scope.row.company }}-->
      <!--          </el-tag>-->
      <!--          <span v-else>-</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <el-button
            v-permission="['user:personal:update']"
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
            >编辑
          </el-button>
          <el-button
            v-permission="['user:enterprise:relate']"
            type="text"
            size="small"
            @click="handleJoinEnterprise(scope.row)"
            >关联企业
          </el-button>
          <el-button
            v-permission="['user:personal:status']"
            type="text"
            size="small"
            :class="scope.row.status === 1 ? 'text-danger' : 'text-success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 个人列表结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增编辑用户信息对话框 -->
    <el-dialog
      :title="dialogType === 'edit' ? '编辑个人信息' : '新增个人信息'"
      :visible.sync="editDialogVisible"
      width="500px"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="userForm"
        label-width="100px"
      >
        <el-form-item label="用户姓名：" prop="real_name">
          <el-input v-model="formData.real_name"></el-input>
        </el-form-item>
        <el-form-item label="手机号码：" prop="phone">
          <el-input v-model="formData.phone"></el-input>
        </el-form-item>
        <el-form-item label="邮箱：" prop="email">
          <el-input v-model="formData.email"></el-input>
        </el-form-item>
        <el-form-item label="用户状态：" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--        <el-form-item label="已绑定公司：" prop="company">-->
        <!--          <div class="company-selector">-->
        <!--            <el-checkbox-group v-model="selectedCompanies">-->
        <!--              <el-checkbox label="广州市优厨食品有限公司"></el-checkbox>-->
        <!--              <el-checkbox label="深圳市美食配送有限公司"></el-checkbox>-->
        <!--            </el-checkbox-group>-->
        <!--          </div>-->
        <!--        </el-form-item>-->
      </el-form>
      <span slot="footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </span>
    </el-dialog>
    <!-- 新增编辑用户信息对话框结束 -->

    <!-- 关联企业对话框 -->
    <el-dialog
      title="关联企业"
      :visible.sync="enterpriseDialogVisible"
      width="800px"
    >
      <div class="enterprise-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="enterpriseSearchForm.name"
              placeholder="企业名称"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="enterpriseSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              @click="handleEnterpriseSearch"
              >查询</el-button
            >
            <el-button type="primary" size="mini" @click="resetEnterpriseSearch"
              >重置</el-button
            >
          </div>
          <div class="enterprise-buttons">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddEnterprise"
              >加入企业
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="enterpriseLoading"
          :data="enterpriseData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column
            prop="company_name"
            label="企业名称"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleRemoveEnterprise(scope.row)"
                >退出企业
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handleEnterpriseCurrentChange"
            @size-change="handleEnterpriseSizeChange"
            :current-page.sync="enterprisePagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="enterprisePagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="enterpriseTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 关联企业对话框结束 -->

    <!-- 加入企业对话框 -->
    <el-dialog
      title="加入企业"
      :visible.sync="addEnterpriseDialogVisible"
      width="800px"
    >
      <div class="enterprise-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="enterpriseListSearchForm.company_name"
              placeholder="企业名称"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="enterpriseListSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              @click="handleEnterpriseListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="resetEnterpriseListSearch"
              >重置</el-button
            >
          </div>
        </div>

        <el-table
          v-loading="enterpriseListLoading"
          :data="enterpriseListData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column
            prop="company_name"
            label="企业名称"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleJoinEnterpriseAction(scope.row)"
                >加入
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handleEnterpriseListCurrentChange"
            @size-change="handleEnterpriseListSizeChange"
            :current-page.sync="enterpriseListPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="enterpriseListPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="enterpriseListTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 加入企业对话框结束 -->
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import { checkPermission, checkRole } from "@/utils/permission";

export default {
  name: "PersonalAccount",
  data() {
    return {
      searchForm: {
        name: "",
        phone: "",
        status: "",
      },
      loading: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      viewDialogVisible: false,
      editDialogVisible: false,
      currentUser: {},
      formData: {
        username: "",
        real_name: "",
        phone: "",
        email: "",
        status: "开启",
        // company: ''
      },
      selectedCompanies: [],
      dialogType: "add", // 'add' 或 'edit'
      rules: {
        real_name: [
          { required: true, message: "请输入用户姓名", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        email: [
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
        ],
      },
      // 企业相关数据
      enterpriseDialogVisible: false,
      addEnterpriseDialogVisible: false,
      enterpriseLoading: false,
      currentPersonalId: null,
      enterpriseData: [],
      enterprisePagination: {
        page: 1,
        pageSize: 10,
      },
      enterpriseTotal: 0,
      enterpriseSearchForm: {
        name: "",
        status: "",
      },
      // 企业列表相关数据
      enterpriseListData: [],
      enterpriseListLoading: false,
      enterpriseListPagination: {
        page: 1,
        pageSize: 10,
      },
      enterpriseListTotal: 0,
      enterpriseListSearchForm: {
        company_name: "",
        status: "",
      },
      // 关联企业相关数据
      enterpriseFormData: {
        personal_id: null,
        enterprise_name: "",
        status: 1,
      },
      enterpriseRules: {
        enterprise_name: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 获取用户数据
    fetchData() {
      this.loading = true;
      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }
      if (this.searchForm.phone) {
        params.phone = this.searchForm.phone;
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status;
      }

      requestApi({
        name: "getPersonalListBySearch",
        data: params,
      })
        .then((response) => {
          this.loading = false;
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取用户列表失败");
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error("获取用户列表失败", error);
          this.$message.error("获取用户列表失败");
        });
    },

    // 搜索用户
    handleSearch() {
      this.pagination.page = 1;
      this.fetchData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        name: "",
        phone: "",
        status: "",
      };
      this.handleSearch();
    },

    // 改变每页显示条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchData();
    },

    // 跳转到指定页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchData();
    },

    // 编辑用户
    handleEdit(row) {
      this.dialogType = "edit";
      requestApi({
        name: "getPersonalDetail",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.formData = response.data || {};
            this.selectedCompanies = this.formData.company
              ? [this.formData.company]
              : [];
            this.editDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取用户详情失败");
          }
        })
        .catch((error) => {
          console.error("获取用户详情失败", error);
          this.$message.error("获取用户详情失败");
        });
    },

    // 新增用户
    handleAdd() {
      this.dialogType = "add";
      this.formData = {
        username: "",
        real_name: "",
        phone: "",
        email: "",
        status: 1,
        // company: ''
      };
      this.selectedCompanies = [];
      this.editDialogVisible = true;
    },

    // 提交表单
    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          // 处理公司信息
          if (this.selectedCompanies && this.selectedCompanies.length > 0) {
            this.formData.company = this.selectedCompanies.join(", ");
          }

          // 确定使用的API方法
          const methodName =
            this.dialogType === "edit" ? "updatePersonal" : "addPersonal";

          // 保存原始表单数据，防止失败后数据丢失
          const originalForm = { ...this.formData };

          // 标记保存是否成功的变量
          let saveSuccess = false;

          // 必须使用电话作为用户名
          this.formData.username = this.formData.phone;

          requestApi({
            name: methodName,
            data: this.formData,
          })
            .then((response) => {
              if (
                response &&
                (response.code === 200 || response.code === 201)
              ) {
                this.$message.success(
                  this.dialogType === "add" ? "添加成功" : "编辑成功"
                );
                saveSuccess = true;
                // 更新表单数据
                if (methodName === "updatePersonal") {
                  this.formData = { ...response.data, id: originalForm.id };
                } else {
                  this.formData = response.data || {};
                }
                this.fetchData();
              } else {
                this.$message.error(
                  response.message ||
                    (this.dialogType === "add" ? "添加失败" : "编辑失败")
                );
                // 保留原始表单数据
                this.formData = originalForm;
              }
            })
            .catch((error) => {
              console.error(
                this.dialogType === "add" ? "添加失败" : "编辑失败",
                error
              );
              this.$message.error(
                this.dialogType === "add" ? "添加失败" : "编辑失败"
              );
              // 保留原始表单数据
              this.formData = originalForm;
            })
            .finally(() => {
              // 只在保存成功时关闭对话框
              if (saveSuccess) {
                this.editDialogVisible = false;
              }
              // 刷新列表
              this.fetchData();
            });
        }
      });
    },

    // 处理加入企业
    handleJoinEnterprise(row) {
      this.currentPersonalId = row.id;
      this.enterpriseFormData.personal_id = row.id;
      this.enterpriseDialogVisible = true;
      this.enterprisePagination.page = 1;
      this.fetchEnterpriseData();
    },

    // 获取企业数据
    fetchEnterpriseData() {
      this.enterpriseLoading = true;
      const params = {
        id: this.currentPersonalId,
        page: this.enterprisePagination.page,
        pageSize: this.enterprisePagination.pageSize,
      };

      // 添加搜索参数
      if (this.enterpriseSearchForm.name) {
        params.name = this.enterpriseSearchForm.name;
      }
      if (this.enterpriseSearchForm.status !== "") {
        params.status = this.enterpriseSearchForm.status;
      }

      requestApi({
        name: "getEnterpriseListByPersonalId",
        data: params,
      })
        .then((response) => {
          this.enterpriseLoading = false;
          if (response && response.code === 200) {
            this.enterpriseData = response.data.list || [];
            this.enterpriseTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取企业列表失败");
          }
        })
        .catch((error) => {
          this.enterpriseLoading = false;
          console.error("获取企业列表失败", error);
          this.$message.error("获取企业列表失败");
        });
    },

    // 搜索企业
    handleEnterpriseSearch() {
      this.enterprisePagination.page = 1;
      this.fetchEnterpriseData();
    },

    // 重置企业搜索
    resetEnterpriseSearch() {
      this.enterpriseSearchForm = {
        name: "",
        status: "",
      };
      this.handleEnterpriseSearch();
    },

    // 改变企业列表每页显示条数
    handleEnterpriseSizeChange(val) {
      this.enterprisePagination.pageSize = val;
      this.fetchEnterpriseData();
    },

    // 跳转到企业列表指定页
    handleEnterpriseCurrentChange(val) {
      this.enterprisePagination.page = val;
      this.fetchEnterpriseData();
    },

    // 加入企业
    handleAddEnterprise() {
      this.enterpriseListPagination.page = 1;
      this.enterpriseListSearchForm = {
        company_name: "",
        status: "",
      };
      this.addEnterpriseDialogVisible = true;
      this.fetchEnterpriseListData();
    },

    // 获取企业列表数据
    fetchEnterpriseListData() {
      this.enterpriseListLoading = true;
      const params = {
        page: this.enterpriseListPagination.page,
        pageSize: this.enterpriseListPagination.pageSize,
      };

      // 添加搜索参数
      if (this.enterpriseListSearchForm.company_name) {
        params.company_name = this.enterpriseListSearchForm.company_name;
      }
      if (this.enterpriseListSearchForm.status !== "") {
        params.status = this.enterpriseListSearchForm.status;
      }

      requestApi({
        name: "getEnterpriseListBySearch",
        data: params,
      })
        .then((response) => {
          this.enterpriseListLoading = false;
          if (response && response.code === 200) {
            this.enterpriseListData = response.data.list || [];
            this.enterpriseListTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取企业列表失败");
          }
        })
        .catch((error) => {
          this.enterpriseListLoading = false;
          console.error("获取企业列表失败", error);
          this.$message.error("获取企业列表失败");
        });
    },

    // 搜索企业列表
    handleEnterpriseListSearch() {
      this.enterpriseListPagination.page = 1;
      this.fetchEnterpriseListData();
    },

    // 重置企业列表搜索
    resetEnterpriseListSearch() {
      this.enterpriseListSearchForm = {
        company_name: "",
        status: "",
      };
      this.handleEnterpriseListSearch();
    },

    // 改变企业列表每页显示条数
    handleEnterpriseListSizeChange(val) {
      this.enterpriseListPagination.pageSize = val;
      this.fetchEnterpriseListData();
    },

    // 跳转到企业列表指定页
    handleEnterpriseListCurrentChange(val) {
      this.enterpriseListPagination.page = val;
      this.fetchEnterpriseListData();
    },

    // 加入企业操作
    handleJoinEnterpriseAction(row) {
      requestApi({
        name: "addEnterprisePersonalRel",
        data: {
          enterprise_id: row.id,
          personl_id: this.currentPersonalId,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("加入企业成功");
            this.addEnterpriseDialogVisible = false;
            this.fetchEnterpriseData(); // 刷新已关联的企业列表
          } else {
            this.$message.error(response.message || "加入企业失败");
          }
        })
        .catch((error) => {
          console.error("加入企业失败", error);
          this.$message.error("加入企业失败");
        });
    },

    // 退出企业
    handleRemoveEnterprise(row) {
      this.$confirm("确认删除该企业关联?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用删除企业API
          requestApi({
            name: "removeEnterprisePersonalRel",
            data: {
              enterprise_id: row.id,
              personl_id: this.currentPersonalId,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchEnterpriseData();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("退出企业失败", error);
              this.$message.error("退出企业失败");
            });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 提交企业表单
    submitEnterpriseForm() {
      this.$refs.enterpriseForm.validate((valid) => {
        if (valid) {
          // 这里应调用加入企业API，暂时模拟
          this.$message.success("加入企业成功");
          this.addEnterpriseDialogVisible = false;
          this.fetchEnterpriseData();
        }
      });
    },

    // 处理状态变更
    handleStatusChange(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${statusText}该用户?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用更新状态API
          requestApi({
            name: "updatePersonal",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success(`${statusText}成功`);
                // 刷新列表
                this.fetchData();
              } else {
                this.$message.error(response.message || `${statusText}失败`);
              }
            })
            .catch((error) => {
              console.error(`${statusText}失败`, error);
              this.$message.error(`${statusText}失败`);
            });
        })
        .catch(() => {
          this.$message.info(`已取消${statusText}`);
        });
    },

    // 权限检查方法
    hasPermission(permission) {
      return checkPermission(permission);
    },

    hasRole(role) {
      return checkRole(role);
    },
  },
};
</script>

<style lang="less" scoped>
.personal-account {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .menu-type-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;
  }

  .left-buttons {
    display: flex;
    gap: 10px;
  }

  .search-container {
    display: flex;
    gap: 10px;
  }

  .search-input {
    width: 180px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .status-normal {
    color: #67c23a;
  }

  .status-disabled {
    color: #f56c6c;
  }

  .user-info-form {
    .form-item {
      margin-bottom: 15px;
      display: flex;

      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
        color: #606266;
      }

      .company-tags {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }

  .company-selector {
    display: flex;
    flex-direction: column;

    .el-checkbox-group {
      display: flex;
      flex-direction: column;

      .el-checkbox {
        margin-left: 0;
        margin-bottom: 10px;
      }
    }
  }

  .enterprise-dialog-content {
    .search-header {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 20px;
      gap: 15px;
    }

    .enterprise-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .text-danger {
    color: #f56c6c;
  }

  .text-success {
    color: #67c23a;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
