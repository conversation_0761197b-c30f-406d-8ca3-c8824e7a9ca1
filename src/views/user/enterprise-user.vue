<template>
  <div class="enterprise-account">
    <h1>企业列表</h1>

    <!-- 头部菜单 -->
    <div class="menu-type-container">
      <div class="search-container">
        <el-input
          v-model="searchForm.company_name"
          placeholder="企业名称"
          size="mini"
          class="search-input"
        ></el-input>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询</el-button
        >
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置</el-button
        >
      </div>
      <div class="left-buttons">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增企业
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 列表 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="企业ID" width="100"> </el-table-column>
      <el-table-column prop="company_name" label="企业名称"> </el-table-column>
      <el-table-column prop="phone" label="联系电话" width="120">
      </el-table-column>
      <el-table-column prop="status" label="企业状态" width="80">
        <template slot-scope="scope">
          <span
            :class="
              scope.row.status === 1 ? 'status-normal' : 'status-disabled'
            "
          >
            <!-- 修改显示内容 -->
            {{ scope.row.status === 1 ? "开启" : "禁用" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="150">
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleJoinPersonal(scope.row)"
            >管理用户
          </el-button>
          <el-button
            type="text"
            size="small"
            :class="scope.row.status === 1 ? 'text-danger' : 'text-success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 列表结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 编辑企业信息对话框 -->
    <el-dialog
      :title="dialogType === 'edit' ? '编辑企业信息' : '新增企业信息'"
      :visible.sync="editDialogVisible"
      width="500px"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="companyForm"
        label-width="130px"
      >
        <!--        <el-form-item label="企业ID：" prop="id">-->
        <!--          <el-input-->
        <!--            v-model="formData.id"-->
        <!--            :disabled="dialogType === 'edit'"-->
        <!--          ></el-input>-->
        <!--        </el-form-item>-->
        <el-form-item label="企业名称：" prop="company_name">
          <el-input v-model="formData.company_name"></el-input>
        </el-form-item>
        <el-form-item label="联系电话：" prop="phone">
          <el-input v-model="formData.phone"></el-input>
        </el-form-item>
        <el-form-item label="邮箱地址：" prop="email">
          <el-input v-model="formData.email"></el-input>
        </el-form-item>
        <el-form-item label="企业地址：" prop="address">
          <el-input v-model="formData.address"></el-input>
        </el-form-item>
        <el-form-item label="企业状态：" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--        <el-form-item label="是否是素食企业：" prop="isVegan">-->
        <!--          <el-radio-group v-model="formData.isVegan">-->
        <!--            <el-radio label="是">是</el-radio>-->
        <!--            <el-radio label="否">否</el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="是否允许代售商品：" prop="allowAgentSale">-->
        <!--          <el-radio-group v-model="formData.allowAgentSale">-->
        <!--            <el-radio label="是">是</el-radio>-->
        <!--            <el-radio label="否">否</el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="企业规模：" prop="companySize">-->
        <!--          <el-select v-model="formData.companySize" placeholder="请选择">-->
        <!--            <el-option label="小型" value="小型"></el-option>-->
        <!--            <el-option label="中型" value="中型"></el-option>-->
        <!--            <el-option label="大型" value="大型"></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
      </el-form>
      <span slot="footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </span>
    </el-dialog>
    <!-- 编辑企业信息对话框结束 -->

    <!-- 关联个人用户对话框 -->
    <el-dialog
      title="关联个人用户"
      :visible.sync="personalDialogVisible"
      width="800px"
    >
      <div class="personal-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="personalSearchForm.name"
              placeholder="用户姓名"
              size="mini"
              class="search-input"
            ></el-input>
            <el-button type="primary" size="mini" @click="handlePersonalSearch"
              >查询</el-button
            >
            <el-button type="primary" size="mini" @click="resetPersonalSearch"
              >重置</el-button
            >
          </div>
          <div class="personal-buttons">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddPersonal"
              >添加用户
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="personalLoading"
          :data="personalData"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="real_name" label="用户姓名"></el-table-column>
          <el-table-column
            prop="phone"
            label="手机号码"
            width="120"
          ></el-table-column>
          <el-table-column prop="is_admin" label="管理员" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.is_admin ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.is_admin ? "是" : "否" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="relation_status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.relation_status === 1
                    ? 'status-normal'
                    : 'status-disabled'
                "
              >
                {{ scope.row.relation_status === 1 ? "启用" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleToggleAdmin(scope.row)"
                :class="scope.row.is_admin ? 'text-warning' : 'text-success'"
              >
                {{ scope.row.is_admin ? "取消管理员" : "设为管理员" }}
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleToggleRelationStatus(scope.row)"
                :class="
                  scope.row.relation_status === 1
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ scope.row.relation_status === 1 ? "禁用" : "启用" }}
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleRemovePersonal(scope.row)"
                class="text-danger"
                >移除用户
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handlePersonalCurrentChange"
            @size-change="handlePersonalSizeChange"
            :current-page.sync="personalPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="personalPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="personalTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 关联个人用户对话框结束 -->

    <!-- 添加个人用户对话框 -->
    <el-dialog
      title="添加个人用户"
      :visible.sync="addPersonalDialogVisible"
      width="800px"
    >
      <div class="personal-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="personalListSearchForm.name"
              placeholder="用户姓名"
              size="mini"
              class="search-input"
            ></el-input>
            <el-input
              v-model="personalListSearchForm.phone"
              placeholder="手机号码"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="personalListSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              @click="handlePersonalListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="resetPersonalListSearch"
              >重置</el-button
            >
          </div>
        </div>

        <el-table
          v-loading="personalListLoading"
          :data="personalListData"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="个人ID" width="80"></el-table-column>
          <el-table-column prop="real_name" label="用户姓名"></el-table-column>
          <el-table-column
            prop="phone"
            label="手机号码"
            width="120"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleJoinPersonalAction(scope.row)"
                >添加
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handlePersonalListCurrentChange"
            @size-change="handlePersonalListSizeChange"
            :current-page.sync="personalListPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="personalListPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="personalListTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 添加个人用户对话框结束 -->
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "EnterpriseAccount",
  data() {
    return {
      searchForm: {
        id: "",
        company_name: "",
      },
      loading: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      viewDialogVisible: false,
      editDialogVisible: false,
      currentCompany: {},
      formData: {
        id: "",
        username: "",
        company_name: "",
        contactPerson: "",
        phone: "",
        email: "",
        address: "",
        status: "开启",
        // isVegan: "是",
        // allowAgentSale: "是",
        // companySize: "小型",
      },
      dialogType: "add", // 'add' 或 'edit'
      rules: {
        // id: [
        //   { required: true, message: "请输入企业ID", trigger: "blur" },
        // ],
        company_name: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
        contactPerson: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        email: [
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
        ],
        status: [
          { required: true, message: "请选择企业状态", trigger: "change" },
        ],
        // isVegan: [
        //   {
        //     required: true,
        //     message: "请选择是否是素食企业",
        //     trigger: "change",
        //   },
        // ],
        // allowAgentSale: [
        //   {
        //     required: true,
        //     message: "请选择是否允许代售商品",
        //     trigger: "change",
        //   },
        // ],
      },
      personalDialogVisible: false,
      personalSearchForm: {
        name: "",
        status: "",
      },
      personalLoading: false,
      personalData: [],
      personalPagination: {
        page: 1,
        pageSize: 10,
      },
      personalTotal: 0,
      addPersonalDialogVisible: false,
      personalListData: [],
      personalListLoading: false,
      personalListPagination: {
        page: 1,
        pageSize: 10,
      },
      personalListTotal: 0,
      personalListSearchForm: {
        name: "",
        phone: "",
        status: "",
      },
      currentEnterpriseId: null,
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 获取企业数据
    fetchData() {
      this.loading = true;
      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.id) {
        params.id = this.searchForm.id;
      }
      if (this.searchForm.company_name) {
        params.company_name = this.searchForm.company_name;
      }

      console.log("parmas:");
      console.log(params);

      requestApi({
        name: "getEnterpriseListBySearch",
        data: params,
      })
        .then((response) => {
          this.loading = false;
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取企业列表失败");
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error("获取企业列表失败", error);
          this.$message.error("获取企业列表失败");
        });
    },

    // 搜索企业
    handleSearch() {
      this.pagination.page = 1;
      this.fetchData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        id: "",
        company_name: "",
      };
      this.handleSearch();
    },

    // 改变每页显示条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchData();
    },

    // 跳转到指定页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchData();
    },

    // 编辑企业
    handleEdit(row) {
      this.dialogType = "edit";
      requestApi({
        name: "getEnterpriseDetail",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.formData = response.data || {};
            this.editDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取企业详情失败");
          }
        })
        .catch((error) => {
          console.error("获取企业详情失败", error);
          this.$message.error("获取企业详情失败");
        });
    },

    // 新增企业
    handleAdd() {
      this.dialogType = "add";
      this.formData = {
        // id: "",
        username: "",
        company_name: "",
        contactPerson: "",
        phone: "",
        email: "",
        address: "",
        status: 1,
        // isVegan: "是",
        // allowAgentSale: "是",
        // companySize: "小型",
      };
      this.editDialogVisible = true;
    },

    // 提交表单
    submitForm() {
      this.$refs.companyForm.validate((valid) => {
        if (valid) {
          // 确定使用的API方法
          const methodName =
            this.dialogType === "edit" ? "updateEnterprise" : "addEnterprise";

          if (methodName === "addEnterprise") {
            this.formData.username = this.formData.company_name;
          }

          // 保存原始表单数据，防止失败后数据丢失
          const originalForm = { ...this.formData };

          // 标记保存是否成功的变量
          let saveSuccess = false;

          requestApi({
            name: methodName,
            data: this.formData,
          })
            .then((response) => {
              if (
                response &&
                (response.code === 200 || response.code === 201)
              ) {
                this.$message.success(
                  this.dialogType === "add" ? "添加成功" : "编辑成功"
                );
                saveSuccess = true;
                // 更新表单数据
                if (methodName === "updateEnterprise") {
                  this.formData = {
                    ...response.data,
                    id: originalForm.id,
                  };
                } else {
                  this.formData = response.data || {};
                }
                this.fetchData();
              } else {
                this.$message.error(
                  response.message ||
                    (this.dialogType === "add" ? "添加失败" : "编辑失败")
                );
                // 保留原始表单数据
                this.formData = originalForm;
              }
            })
            .catch((error) => {
              console.error(
                this.dialogType === "add" ? "添加失败" : "编辑失败",
                error
              );
              this.$message.error(
                this.dialogType === "add" ? "添加失败" : "编辑失败"
              );
              // 保留原始表单数据
              this.formData = originalForm;
            })
            .finally(() => {
              // 只在保存成功时关闭对话框
              if (saveSuccess) {
                this.editDialogVisible = false;
              }
            });
        }
      });
    },

    // 关联个人用户
    handleJoinPersonal(row) {
      this.currentEnterpriseId = row.id;
      this.personalDialogVisible = true;
      this.personalPagination.page = 1;
      this.fetchPersonalData();
    },

    // 获取个人用户数据
    fetchPersonalData() {
      this.personalLoading = true;
      const params = {
        id: this.currentEnterpriseId,
        page: this.personalPagination.page,
        pageSize: this.personalPagination.pageSize,
      };

      // 添加搜索参数
      if (this.personalSearchForm.name) {
        params.name = this.personalSearchForm.name;
      }
      if (this.personalSearchForm.status !== "") {
        params.status = this.personalSearchForm.status;
      }

      requestApi({
        name: "getPersonalListByEnterpriseId",
        data: params,
      })
        .then((response) => {
          this.personalLoading = false;
          if (response && response.code === 200) {
            this.personalData = response.data.list || [];
            this.personalTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取用户列表失败");
          }
        })
        .catch((error) => {
          this.personalLoading = false;
          console.error("获取用户列表失败", error);
          this.$message.error("获取用户列表失败");
        });
    },

    // 搜索个人用户
    handlePersonalSearch() {
      this.personalPagination.page = 1;
      this.fetchPersonalData();
    },

    // 重置个人用户搜索
    resetPersonalSearch() {
      this.personalSearchForm = {
        name: "",
        status: "",
      };
      this.handlePersonalSearch();
    },

    // 改变个人用户每页显示条数
    handlePersonalSizeChange(val) {
      this.personalPagination.pageSize = val;
      this.fetchPersonalData();
    },

    // 跳转到个人用户指定页
    handlePersonalCurrentChange(val) {
      this.personalPagination.page = val;
      this.fetchPersonalData();
    },

    // 添加个人用户
    handleAddPersonal() {
      this.personalListPagination.page = 1;
      this.personalListSearchForm = {
        name: "",
        phone: "",
        status: "",
      };
      this.addPersonalDialogVisible = true;
      this.fetchPersonalListData();
    },

    // 移除个人用户
    handleRemovePersonal(row) {
      this.$confirm("确认移除该用户关联?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用移除用户API
          requestApi({
            name: "removeEnterprisePersonalRel",
            data: {
              enterprise_id: this.currentEnterpriseId,
              personl_id: row.id,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("移除成功");
                this.fetchPersonalData();
              } else {
                this.$message.error(response.message || "移除失败");
              }
            })
            .catch((error) => {
              console.error("移除用户失败", error);
              this.$message.error("移除用户失败");
            });
        })
        .catch(() => {
          this.$message.info("已取消移除");
        });
    },

    // 获取个人用户列表
    fetchPersonalListData() {
      this.personalListLoading = true;
      const params = {
        page: this.personalListPagination.page,
        pageSize: this.personalListPagination.pageSize,
      };

      // 添加搜索参数
      if (this.personalListSearchForm.name) {
        params.name = this.personalListSearchForm.name;
      }
      if (this.personalListSearchForm.phone) {
        params.phone = this.personalListSearchForm.phone;
      }
      if (this.personalListSearchForm.status) {
        params.status = this.personalListSearchForm.status;
      }

      console.log("personal list parmas:");
      console.log(params);

      requestApi({
        name: "getPersonalListBySearch",
        data: params,
      })
        .then((response) => {
          this.personalListLoading = false;
          if (response && response.code === 200) {
            this.personalListData = response.data.list || [];
            this.personalListTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取个人用户列表失败");
          }
        })
        .catch((error) => {
          this.personalListLoading = false;
          console.error("获取个人用户列表失败", error);
          this.$message.error("获取个人用户列表失败");
        });
    },

    // 搜索个人用户列表
    handlePersonalListSearch() {
      this.personalListPagination.page = 1;
      this.fetchPersonalListData();
    },

    // 重置个人用户列表搜索
    resetPersonalListSearch() {
      this.personalListSearchForm = {
        name: "",
        phone: "",
        status: "",
      };
      this.handlePersonalListSearch();
    },

    // 改变个人用户列表每页显示条数
    handlePersonalListSizeChange(val) {
      this.personalListPagination.pageSize = val;
      this.fetchPersonalListData();
    },

    // 跳转到个人用户列表指定页
    handlePersonalListCurrentChange(val) {
      this.personalListPagination.page = val;
      this.fetchPersonalListData();
    },

    // 添加个人用户到企业
    handleJoinPersonalAction(row) {
      requestApi({
        name: "addEnterprisePersonalRel",
        data: {
          enterprise_id: this.currentEnterpriseId,
          personl_id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("添加用户成功");
            this.addPersonalDialogVisible = false;
            this.fetchPersonalData(); // 刷新已关联的用户列表
          } else {
            this.$message.error(response.message || "添加用户失败");
          }
        })
        .catch((error) => {
          console.error("添加用户失败", error);
          this.$message.error("添加用户失败");
        });
    },

    // 处理状态变更
    handleStatusChange(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${statusText}该企业?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用更新状态API
          requestApi({
            name: "updateEnterprise",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success(`${statusText}成功`);
                // 刷新列表
                this.fetchData();
              } else {
                this.$message.error(response.message || `${statusText}失败`);
              }
            })
            .catch((error) => {
              console.error(`${statusText}失败`, error);
              this.$message.error(`${statusText}失败`);
            });
        })
        .catch(() => {
          this.$message.info(`已取消${statusText}`);
        });
    },

    // 切换管理员状态
    handleToggleAdmin(row) {
      const newAdminStatus = !row.is_admin;
      const actionText = newAdminStatus ? "设为管理员" : "取消管理员";

      this.$confirm(`确认${actionText}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "updateEnterprisePersonalRel",
            data: {
              enterprise_id: this.currentEnterpriseId,
              user_id: row.id,
              is_admin: newAdminStatus,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success(`${actionText}成功`);
                this.fetchPersonalData(); // 刷新关联用户列表
              } else {
                this.$message.error(response.message || `${actionText}失败`);
              }
            })
            .catch((error) => {
              console.error(`${actionText}失败`, error);
              this.$message.error(`${actionText}失败`);
            });
        })
        .catch(() => {
          this.$message.info(`已取消${actionText}`);
        });
    },

    // 切换关联状态
    handleToggleRelationStatus(row) {
      const newRelationStatus = row.relation_status === 1 ? 0 : 1;
      const actionText = newRelationStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${actionText}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "updateEnterprisePersonalRel",
            data: {
              enterprise_id: this.currentEnterpriseId,
              user_id: row.id,
              relation_status: newRelationStatus,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success(`${actionText}成功`);
                this.fetchPersonalData(); // 刷新关联用户列表
              } else {
                this.$message.error(response.message || `${actionText}失败`);
              }
            })
            .catch((error) => {
              console.error(`${actionText}失败`, error);
              this.$message.error(`${actionText}失败`);
            });
        })
        .catch(() => {
          this.$message.info(`已取消${actionText}`);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise-account {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .menu-type-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;
  }

  .left-buttons {
    display: flex;
    gap: 10px;
  }

  .search-container {
    display: flex;
    gap: 10px;
  }

  .search-input {
    width: 180px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .company-info-form {
    .form-item {
      margin-bottom: 15px;
      display: flex;

      .label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
        color: #606266;
      }
    }
  }

  .status-normal {
    color: #67c23a;
  }

  .status-disabled {
    color: #f56c6c;
  }

  .personal-dialog-content {
    .search-header {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 20px;
      gap: 15px;
    }

    .personal-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .text-danger {
    color: #f56c6c;
  }

  .text-success {
    color: #67c23a;
  }

  .text-warning {
    color: #e6a23c;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
