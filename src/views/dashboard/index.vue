<template>
  <div class="page-dashboard">
    <template v-if="role != 3 && role != 4">
      <span @click="go(item.url)" v-for="(item, index) in list" :key="index" style="height:120px;cursor: pointer;">
        <div class="item">
          <img :src="item.icon" alt="" />
          <p>{{ item.name }}</p>
        </div>
      </span>
    </template>
    <template v-else>
      <span @click="go('/user/user-info')"  style="height:120px;cursor: pointer;">
        <div class="item">
          <img :src="require('../../assets/images/102.png')" alt="" />
          <p>账户中心</p>
        </div>
      </span>
    </template>
  </div>
</template>
<script>
export default {
  name: 'Dashboard',
  components: {},
  data() {
    return {
      role: this.$store.state.user.token,
      list: [{
      // list: [{
      //   name: '开票列表',
      //   icon: require('../../assets/images/122.png'),
      //   url: '/invoicing/invoicing-management',
      // }, {
      //   name: '普通发票',
      //   icon: require('../../assets/images/62.png'),
      //   url: '/invoicing/add',
      // }, {
      //   name: '开票申请',
      //   icon: require('../../assets/images/79.png'),
      //   url: '/invoicing-application/application-list',
      // }, {
        name: '个人信息',
        icon: require('../../assets/images/102.png'),
        url: '/user/user-info',
      // }, {
      //   name: '购买方列表',
      //   icon: require('../../assets/images/18.png'),
      //   url: '/buyer/buyer-management',
      // }, {
      //   name: '客户联系人',
      //   icon: require('../../assets/images/9.png'),
      //   url: '/customer/customer-management',
      // }, {
      //   name: '销售方列表',
      //   icon: require('../../assets/images/33.png'),
      //   url: '/enterprise/enterprise-management',
      // }, {
      //   name: '任务列表',
      //   icon: require('../../assets/images/38.png'),
      //   url: '/task/task-list',
      // }, {
      //   name: '草稿',
      //   icon: require('../../assets/images/91.png'),
      //   url: '/invoicing/draft',
      // }, {
      //   name: '批量开票任务',
      //   icon: require('../../assets/images/79.png'),
      //   url: '/batch/tasks',
      }]
    }
  },
  mounted() {
  },
  methods: {
    go(url) {
      if (url == '/invoicing/add') {
        let view = {
          path: "/invoicing/add",
          name: 'InvoicingDetailAdd'
        }
        this.$store.dispatch('delVisitedViews', view).then((views) => {
          localStorage.setItem("invoice_type", 2);
          setTimeout(() => {
            this.$router.push(url + '?addoredit=1')
          }, 300)
        })
      } else {
        this.$router.push(url)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.page-dashboard {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  background: #fff;
  min-height: 400px;

  .item {
    background: #fff;
    text-align: center;
    width: 120px;
    padding: 20px;
    margin: 0 20px;
  }
}
</style>
