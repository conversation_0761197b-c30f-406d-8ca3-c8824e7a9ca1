<template>
  <div class="errPage-container">
    <el-button
      icon="arrow-left"
      class="pan-back-btn"
      @click="back"
    >返回</el-button>
    <div>
      <h1 class="text-jumbo text-ginormous">Oops!</h1>
      <h2>你没有权限去该页面</h2>
      <h4>如有需要请联系管理员</h4>
      <ul class="list-unstyled">
        <li>或者你可以去:</li>
        <li class="link-type">
          <router-link to="/dashboard">回首页</router-link>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Page401',
  data() {
    return {}
  },
  methods: {
    back() {
      if (this.$route.query.noGoBack) {
        this.$router.push({ path: '/dashboard' })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.errPage-container {
  text-align: center;
  width: 800px;
  margin: 100px auto;
  .pan-back-btn {
    background: #008489;
    color: #fff;
  }
  .pan-gif {
    margin: 0 auto;
    display: block;
  }
  .pan-img {
    display: block;
    margin: 0 auto;
    width: 100%;
  }
  .text-jumbo {
    font-size: 60px;
    font-weight: 700;
    color: #484848;
  }
  .list-unstyled {
    padding: 0;
    li {
      padding-bottom: 5px;
      list-style: none;
    }
    a {
      color: #008489;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
