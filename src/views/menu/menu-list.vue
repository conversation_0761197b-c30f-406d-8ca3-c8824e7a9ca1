<template>
  <div class="menu-list">
    <h1>菜单列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
              v-model="searchForm.name"
              placeholder="菜单名称"
              size="mini"
              class="filter-item"
          />
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
        >查询
        </el-button>
        <el-button size="mini" @click="resetSearch">重置</el-button>
      </div>
      <div class="left-buttons">
        <el-button
            type="primary"
            size="mini"
            @click="handleAdd"
            icon="el-icon-plus"
        >新增菜单
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
        v-loading="listLoading"
        :data="tableData"
        border
        fit
        highlight-current-row
        style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column prop="id" label="序号" width="65" align="center"/>
      <el-table-column prop="name" label="名称" align="center">
        <template slot-scope="scope">
          <div class="package-info">
            <span class="ml-10">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          prop="description"
          label="描述"
          width="120"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="product_name"
          label="餐食"
          width="120"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="rule_name"
          label="时段"
          width="120"
          align="center"
      ></el-table-column>

      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "已生效" : "已失效" }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="350">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
          >{{ scope.row.status === 1 ? "失效" : "生效" }}
          </el-button>
          <el-button type="text" size="mini" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page.sync="pagination.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑菜单弹窗 -->
    <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogFormVisible"
        width="85%"
    >
      <el-form
          ref="dataForm"
          :rules="menu_rules"
          :model="formData"
          label-position="right"
          label-width="120px"
      >
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入菜单名称"/>
        </el-form-item>
        <el-form-item label="餐型选择" prop="product_id">
          <el-select
              v-model="formData.product_id"
              filterable
              remote
              placeholder="请选择餐型"
              :remote-method="searchProducts"
              :loading="productLoading"
              @change="handleProductChange"
          >
            <el-option
                v-for="item in productOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            >
              <span style="float: left">{{ item.name }}</span>
              <!--              <span style="float: right; color: #8492a6; font-size: 13px"-->
              <!--                >¥{{ item.price }}</span-->
              <!--              >-->
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="时段选择" prop="rule_id">
          <el-select
              v-model="formData.rule_id"
              filterable
              placeholder="请选择时段"
              :loading="ruleLoading"
              :disabled="!formData.product_id || diningRules.length === 0"
          >
            <el-option
                v-for="item in diningRules"
                :key="item.rule_id"
                :label="item.rule_name"
                :value="item.rule_id"
            >
              <span>{{ item.rule_name }}</span>
              <span v-if="item.alias"> ({{ item.alias }})</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
                label="生效开始时间"
                prop="available_start_time_cron_str"
            >
              <div class="time-input-wrapper">
                <CronTime v-model="formData.available_start_time_cron_str"/>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
                label="生效结束时间"
                prop="available_end_time_cron_str"
            >
              <div class="time-input-wrapper">
                <CronTime v-model="formData.available_end_time_cron_str"/>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">生效</el-radio>
            <el-radio :label="0">失效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单描述">
          <el-input
              type="textarea"
              v-model="formData.description"
              rows="4"
              placeholder="请输入菜单描述"
          />
        </el-form-item>
      </el-form>

      <!-- 菜品列表 -->
      <div class="content-section">
        <div class="content-header">
          <h3>菜品列表</h3>
          <div class="content-actions">
            <el-button
                type="primary"
                size="mini"
                @click="handleOpenContentDialog"
            >添加菜品
            </el-button
            >
            <el-button
                type="danger"
                size="mini"
                @click="handleRemoveContents"
                :disabled="!selectedContents.length"
            >移除菜品
            </el-button
            >
          </div>
        </div>

        <el-table
            v-loading="contentLoading"
            :data="contentList"
            border
            fit
            highlight-current-row
            @selection-change="handleContentSelectionChange"
            style="width: 100%; margin-top: 10px"
        >
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column prop="id" label="ID" width="65" align="center"/>
          <el-table-column prop="name" label="菜品名称" align="center"/>
          <el-table-column label="菜品图片" width="100" align="center">
            <template slot-scope="scope">
              <div class="menu-image">
                <img :src="scope.row.thumbnail" alt="菜品图片"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="status"
              label="状态"
              width="100"
              align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? "可用" : "不可用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <el-button
                  type="text"
                  size="mini"
                  @click="handleRemoveContent(scope.row)"
              >移除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增/编辑菜单弹窗结束 -->

    <!-- 添加菜品对话框 -->
    <el-dialog
        title="添加菜品"
        :visible.sync="contentDialogVisible"
        width="80%"
    >
      <div class="search-content-form">
        <el-form :inline="true" :model="contentSearchForm" class="form-inline">
          <el-form-item label="菜品名">
            <el-input
                v-model="contentSearchForm.name"
                placeholder="菜品名"
                size="mini"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
                v-model="contentSearchForm.status"
                placeholder="状态"
                size="mini"
                clearable
            >
              <el-option label="可用" :value="1"/>
              <el-option label="不可用" :value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="searchContents"
            >查询
            </el-button
            >
            <el-button size="mini" @click="resetContentSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
          v-loading="contentListLoading"
          :data="contentListData"
          border
          fit
          highlight-current-row
          @selection-change="handleContentListSelectionChange"
          style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column prop="id" label="ID" width="65" align="center"/>
        <el-table-column label="菜品图片" width="100" align="center">
          <template slot-scope="scope">
            <div class="menu-image">
              <img :src="scope.row.thumbnail" alt="菜品图片"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="菜品名称" align="center"/>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? "可用" : "不可用" }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
            @current-change="handleContentListCurrentChange"
            @size-change="handleContentListSizeChange"
            :current-page.sync="contentListPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="contentListPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="contentListTotal"
        >
        </el-pagination>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="contentDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addSelectedContents">确定</el-button>
      </div>
    </el-dialog>
    <!-- 添加菜品对话框结束 -->
  </div>
</template>

<script>
import {requestApi} from "@/utils/request";
import CronTime from "@/components/CronTime/index.vue";

export default {
  name: "MenuList",
  components: {
    CronTime,
  },
  filters: {},
  data() {
    return {
      searchForm: {
        name: "",
      },
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 3,
      dialogFormVisible: false,
      dialogTitle: "",
      formData: {
        id: undefined,
        name: "",
        type: "",
        price: 0,
        stock: 0,
        status: 1,
        description: "",
        reservation_fee: 0,
        max_reservations: 1,
        reservation_deadline: null,
        cancellation_deadline: null,
        is_approval_required: false,
        product_id: undefined,
        rule_id: undefined,
        available_start_time_cron_str: "0 0 * * * 0 *",
        available_end_time_cron_str: "0 0 * * * 0 *",
        content_ids: [],
      },
      menu_rules: {
        name: [{required: true, message: "请输入菜单名称", trigger: "blur"}],
        type: [
          {required: true, message: "请选择菜单类型", trigger: "change"},
        ],
        available_start_time_cron_str: [
          {required: true, message: "请选择生效开始时间", trigger: "change"},
        ],
        available_end_time_cron_str: [
          {required: true, message: "请选择生效截止时间", trigger: "change"},
        ],
      },

      // 餐型相关
      productLoading: false,
      productOptions: [],
      // 时段相关
      ruleLoading: false,
      ruleOptions: [],
      // 菜品相关
      contentDialogVisible: false,
      contentSearchForm: {
        name: "",
        status: "",
      },
      contentLoading: false,
      contentList: [],
      selectedContents: [],
      contentPagination: {
        page: 1,
        pageSize: 10,
      },
      contentTotal: 0,
      contentListSearchForm: {
        name: "",
        status: "",
      },
      contentListLoading: false,
      contentListData: [],
      contentListPagination: {
        page: 1,
        pageSize: 10,
      },
      contentListTotal: 0,
      selectedContentList: [],
      currentProductId: null,

      diningRules: [],
    };
  },
  created() {
    this.getMenuList();
  },
  methods: {
    // 获取菜单列表
    getMenuList() {
      this.loading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getMenusBySearch",
        data: params,
      })
          .then((response) => {
            this.loading = false;
            if (response && response.code === 200) {
              this.tableData = response.data.list || [];
              this.total = response.data.total || 0;
            } else {
              this.$message.error(response.message || "获取菜单列表失败");
            }
          })
          .catch((error) => {
            this.loading = false;
            console.error("获取菜单列表失败", error);
            this.$message.error("获取菜单列表失败");
          });
    },

    // 餐型时段相关方法
    searchProducts(query) {
      if (query !== "") {
        this.productLoading = true;
        requestApi({
          name: "getProductWithDiningRules",
          data: {
            product_name: query,
          },
        })
            .then((response) => {
              this.productLoading = false;
              if (response && response.code === 200) {
                // 转换API返回的数据格式为组件需要的格式
                this.productOptions = (response.data.products || []).map(
                    (product) => ({
                      id: product.product_id,
                      name: product.product_name,
                      price: product.product_price,
                      description: product.product_description,
                      status: product.product_status,
                      dining_rules: product.dining_rules || [],
                    })
                );
              } else {
                this.$message.error(response.message || "搜索餐型失败");
              }
            })
            .catch((error) => {
              console.error("搜索餐型失败", error);
              this.productLoading = false;
              this.$message.error("搜索餐型失败");
            });
      } else {
        this.productOptions = [];
      }
    },

    handleProductChange(productId) {
      this.formData.rule_id = undefined; // 清空已选择的规则
      this.diningRules = [];
      this.currentProductId = productId;

      if (!productId) return;

      // 查找当前选中的产品
      const selectedProduct = this.productOptions.find(
          (item) => item.id === productId
      );
      if (selectedProduct && selectedProduct.dining_rules) {
        this.diningRules = selectedProduct.dining_rules;
        // 如果只有一条规则，自动选中
        if (this.diningRules.length === 1) {
          this.formData.rule_id = this.diningRules[0].rule_id;
        }
      } else {
        this.fetchDiningRules();
      }
    },

    fetchDiningRules() {
      if (!this.currentProductId) return;

      this.ruleLoading = true;
      requestApi({
        name: "getProductWithDiningRules",
        data: {
          product_id: this.currentProductId,
        },
      })
          .then((response) => {
            this.ruleLoading = false;
            if (response && response.code === 200) {
              const product =
                  response.data.products && response.data.products.length > 0
                      ? response.data.products[0]
                      : null;

              if (product && product.dining_rules) {
                this.diningRules = product.dining_rules;
                // 如果只有一条规则，自动选中
                if (this.diningRules.length === 1) {
                  this.formData.rule_id = this.diningRules[0].rule_id;
                }
              }
            } else {
              this.$message.error(response.message || "获取时段失败");
            }
          })
          .catch((error) => {
            this.ruleLoading = false;
            console.error("获取时段失败", error);
            this.$message.error("获取时段失败");
          });
    },

    // 菜品相关方法
    handleOpenContentDialog() {
      this.contentDialogVisible = true;
      this.contentListSearchForm = {
        title: "",
        status: "",
      };
      this.searchContents();
    },

    searchContents() {
      this.contentListLoading = true;
      const params = {
        page: this.contentListPagination.page,
        pageSize: this.contentListPagination.pageSize,
        ...this.contentListSearchForm,
      };

      requestApi({
        name: "getDishListBySearch", // 使用替代API，实际应该根据项目情况调整
        data: params,
      })
          .then((response) => {
            this.contentListLoading = false;
            if (response && response.code === 200) {
              this.contentListData = response.data.list || [];
              this.contentListTotal = response.data.total || 0;
            } else {
              this.$message.error(response.message || "获取菜品列表失败");
            }
          })
          .catch((error) => {
            this.contentListLoading = false;
            console.error("获取菜品列表失败", error);
            this.$message.error("获取菜品列表失败");
          });
    },

    resetContentSearch() {
      this.contentListSearchForm = {
        title: "",
        status: "",
      };
      this.searchContents();
    },

    handleContentListSelectionChange(selection) {
      this.selectedContentList = selection;
    },

    handleContentSelectionChange(selection) {
      this.selectedContents = selection;
    },

    handleContentListCurrentChange(val) {
      this.contentListPagination.page = val;
      this.searchContents();
    },

    handleContentListSizeChange(val) {
      this.contentListPagination.pageSize = val;
      this.contentListPagination.page = 1;
      this.searchContents();
    },

    addSelectedContents() {
      if (this.selectedContentList.length === 0) {
        this.$message.warning("请至少选择一个菜品");
        return;
      }

      // 将选中的菜品添加到菜单的菜品列表中
      const newContents = this.selectedContentList.filter(
          (item) => !this.contentList.some((existing) => existing.id === item.id)
      );

      this.contentList = [...this.contentList, ...newContents];
      this.contentDialogVisible = false;
      this.$message.success(`已添加 ${newContents.length} 个菜品`);
    },

    handleRemoveContent(content) {
      const index = this.contentList.findIndex(
          (item) => item.id === content.id
      );
      if (index !== -1) {
        this.contentList.splice(index, 1);
      }
    },

    handleRemoveContents() {
      if (this.selectedContents.length === 0) {
        this.$message.warning("请选择要移除的菜品");
        return;
      }

      this.selectedContents.forEach((content) => {
        const index = this.contentList.findIndex(
            (item) => item.id === content.id
        );
        if (index !== -1) {
          this.contentList.splice(index, 1);
        }
      });

      this.$message.success(`已移除 ${this.selectedContents.length} 个菜品`);
      this.selectedContents = [];
    },

    // 菜单相关方法
    handleSearch() {
      this.pagination.page = 1;
      this.getMenuList();
    },

    resetSearch() {
      this.searchForm = {
        name: "",
      };
      this.getMenuList();
    },

    // 查看菜单明细
    handleAdd() {
      this.dialogTitle = "新增菜单";
      this.formData = {
        id: undefined,
        name: "",
        // type: "direct_sale",
        price: 0,
        stock: 0,
        status: 1,
        description: "",
        reservation_fee: 0,
        max_reservations: 1,
        reservation_deadline: null,
        cancellation_deadline: null,
        is_approval_required: false,
        product_id: undefined,
        rule_id: undefined,
        available_start_time_cron_str: "0 0 * * * 0 *",
        available_end_time_cron_str: "0 0 * * * 0 *",
      };
      this.contentList = [];
      this.selectedContents = [];
      this.dialogFormVisible = true;
    },

    handleEdit(row) {
      this.dialogTitle = "编辑菜单";

      // 获取菜单详情
      this.listLoading = true;
      requestApi({
        name: "getMenuWithContents",
        data: {
          Menu_id: row.id,
        },
      })
          .then((response) => {
            this.listLoading = false;
            if (response && response.code === 200) {
              // 将API返回的数据映射到表单
              const menuData = response.data;
              this.formData = {
                id: menuData.id || "",
                name: menuData.name || "",
                type: menuData.type || "direct_sale",
                price: menuData.price || 0,
                stock: menuData.stock || 0,
                status: menuData.status || 0,
                description: menuData.description || "",
                reservation_fee: menuData.reservation_fee || 0,
                max_reservations: menuData.max_reservations || 1,
                reservation_deadline: menuData.reservation_deadline || "",
                cancellation_deadline: menuData.cancellation_deadline || "",
                is_approval_required: menuData.is_approval_required || false,
                product_id: menuData.product_id,
                rule_id: menuData.rule_id,
                available_start_time_cron_str:
                    menuData.available_start_time_cron_str || "0 0 * * * 0 *",
                available_end_time_cron_str:
                    menuData.available_end_time_cron_str || "0 0 * * * 0 *",
              };
              this.contentList = menuData.contents || [];
              this.dialogFormVisible = true;
            } else {
              this.$message.error(response.message || "获取菜单详情失败");
            }
          })
          .catch((error) => {
            this.listLoading = false;
            console.error("获取菜单详情失败", error);
            this.$message.error("获取菜单详情失败");
          });
    },

    handleDelete(row) {
      this.$confirm("确认删除该菜单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            this.listLoading = true;
            requestApi({
              name: "deleteMenu",
              data: {
                id: row.id,
              },
            })
                .then((response) => {
                  this.listLoading = false;
                  if (response && response.code === 200) {
                    this.$message({
                      type: "success",
                      message: "删除成功!",
                    });
                    this.getMenuList(); // 刷新列表
                  }
                })
                .catch((error) => {
                  console.error("删除菜单失败", error);
                  this.$message.error("删除菜单失败");
                })
                .finally(() => {
                  this.listLoading = false;
                });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
    },

    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "生效" : "失效";

      this.$confirm(`确认${statusText}该菜单?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            this.listLoading = true;
            requestApi({
              name: "updateMenuStatus",
              data: {
                id: row.id,
                status: newStatus,
              },
            })
                .then((response) => {
                  this.listLoading = false;
                  if (response && response.code === 200) {
                    this.$message({
                      type: "success",
                      message: `菜单已${statusText}!`,
                    });
                    row.status = newStatus; // 更新本地状态
                    this.getMenuList(); // 刷新列表
                  } else {
                    this.$message.error(
                        response.message || `${statusText}菜单失败`
                    );
                  }
                })
                .catch((error) => {
                  this.listLoading = false;
                  console.error(`${statusText}菜单失败`, error);
                  this.$message.error(`${statusText}菜单失败`);
                });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消操作",
            });
          });
    },

    submitForm() {
      this.$refs.dataForm.validate((valid) => {
        let content_ids = []
        if (valid) {
          this.contentList.forEach(item => {
            content_ids.push(item.id);
          })
          // 准备要提交的数据
          const menuData = {
            name: this.formData.name,
            type: this.formData.type,
            price: this.formData.price,
            stock: this.formData.stock,
            status: this.formData.status,
            description: this.formData.description,
            product_id: this.formData.product_id,
            rule_id: this.formData.rule_id,
            available_start_time_cron_str:
            this.formData.available_start_time_cron_str,
            available_end_time_cron_str:
            this.formData.available_end_time_cron_str,
            content_ids: content_ids
          };

          // 如果是预订菜单，添加预订相关字段
          if (this.formData.type === "reservation") {
            menuData.reservation_fee = this.formData.reservation_fee;
            menuData.max_reservations = this.formData.max_reservations;
            menuData.reservation_deadline = this.formData.reservation_deadline;
            menuData.cancellation_deadline =
                this.formData.cancellation_deadline;
            menuData.is_approval_required = this.formData.is_approval_required;
          }

          if (this.formData.id !== undefined) {
            // 编辑菜单
            menuData.id = this.formData.id;

            // 处理空值
            if (
                menuData.reservation_deadline === null ||
                menuData.reservation_deadline === ""
            ) {
              delete menuData.reservation_deadline;
            }
            if (
                menuData.cancellation_deadline === null ||
                menuData.cancellation_deadline === ""
            ) {
              delete menuData.cancellation_deadline;
            }

            this.listLoading = true;
            requestApi({
              name: "updateMenu",
              data: menuData,
            })
                .then((response) => {
                  if (response && response.code === 200) {
                    // 处理菜品列表的更新
                    this.updateMenuContents(this.formData.id)
                        .then(() => {
                          this.listLoading = false;
                          this.$message({
                            type: "success",
                            message: "菜单信息已更新!",
                          });
                          this.dialogFormVisible = false;
                          this.getMenuList(); // 刷新列表
                        })
                        .catch((error) => {
                          this.listLoading = false;
                          console.error("更新菜单菜品失败", error);
                          this.$message.error("菜单信息已更新，但菜品更新失败");
                        });
                  } else {
                    this.listLoading = false;
                    this.$message.error(response.message || "更新菜单失败");
                  }
                })
                .catch((error) => {
                  this.listLoading = false;
                  console.error("更新菜单失败", error);
                  this.$message.error("更新菜单失败");
                });
          } else {
            // 新增菜单
            this.listLoading = true;
            requestApi({
              name: "addMenu",
              data: menuData,
            })
                .then((response) => {
                  if (response && response.code === 200) {
                    // 如果有菜品列表，添加菜品关联
                    if (this.contentList.length > 0 && response.data.id) {
                      this.updateMenuContents(response.data.id)
                          .then(() => {
                            this.listLoading = false;
                            this.$message({
                              type: "success",
                              message: "新增菜单成功!",
                            });
                            this.dialogFormVisible = false;
                            this.getMenuList(); // 刷新列表
                          })
                          .catch((error) => {
                            this.listLoading = false;
                            console.error("添加菜单菜品失败", error);
                            this.$message.error("菜单创建成功，但菜品添加失败");
                          });
                    } else {
                      this.listLoading = false;
                      this.$message({
                        type: "success",
                        message: "新增菜单成功!",
                      });
                      this.dialogFormVisible = false;
                      this.getMenuList(); // 刷新列表
                    }
                  } else {
                    this.listLoading = false;
                    this.$message.error(response.message || "新增菜单失败");
                  }
                })
                .catch((error) => {
                  this.listLoading = false;
                  console.error("新增菜单失败", error);
                  this.$message.error("新增菜单失败");
                });
          }
        }
      });
    },

    // 更新菜单菜品
    updateMenuContents(menuId) {
      return new Promise((resolve, reject) => {
        const contentIds = this.contentList.map((item) => item.id);
        requestApi({
          name: "addProductContent", // 使用正确的API
          data: {
            product_id: menuId,
            content_ids: contentIds,
          },
        })
            .then((response) => {
              if (response && response.code === 200) {
                resolve();
              } else {
                reject(new Error(response.message || "更新菜品失败"));
              }
            })
            .catch((error) => {
              reject(error);
            });
      });
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getMenuList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getMenuList();
    },
  },
};
</script>

<style lang="less" scoped>
.menu-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .package-info {
    display: flex;
    align-items: center;

    .ml-10 {
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .avatar-uploader {
    display: block;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;

    &:hover {
      border-color: #409eff;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.content-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.content-actions {
  display: flex;
  gap: 10px;
}

.time-input-wrapper {
  width: 100%;
}

.search-content-form {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.status-normal {
  color: #67c23a;
}

.status-disabled {
  color: #f56c6c;
}

.pricing-dialog-content {
  padding: 20px;

  .search-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .search-container {
      display: flex;
      gap: 10px;

      .search-input {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .pricing-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}

.rule-dialog-content {
  padding: 20px;

  .search-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .search-container {
      display: flex;
      gap: 10px;

      .search-input {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .rule-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .menu-image img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}
</style>
