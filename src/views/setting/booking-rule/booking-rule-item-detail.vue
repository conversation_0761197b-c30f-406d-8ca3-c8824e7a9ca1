<template>
  <el-dialog :title="title" :visible="visible" width="85%" @close="handleClose">
    <el-form
      ref="ruleItemForm"
      :rules="ruleItemRules"
      :model="formData"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="规则项名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入规则项名称"
          style="width: 300px"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="start_time_cron_str">
            <div class="time-input-wrapper">
              <el-input
                v-model="startTimeDescription"
                placeholder="请设置开始时间"
                :readonly="true"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                type="primary"
                size="small"
                @click="openStartTimeDialog"
                style="flex-shrink: 0"
              >
                设置
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="end_time_cron_str">
            <div class="time-input-wrapper">
              <el-input
                v-model="endTimeDescription"
                placeholder="请设置结束时间"
                :readonly="true"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                type="primary"
                size="small"
                @click="openEndTimeDialog"
                style="flex-shrink: 0"
              >
                设置
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="预订截止" prop="order_deadline">
            <div style="display: flex; align-items: center">
              <el-input-number
                v-model="orderDeadlineValues.month"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateOrderDeadline"
              />
              <span style="margin-right: 10px">月</span>
              <el-input-number
                v-model="orderDeadlineValues.day"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateOrderDeadline"
              />
              <span style="margin-right: 10px">天</span>
              <el-input-number
                v-model="orderDeadlineValues.hour"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateOrderDeadline"
              />
              <span style="margin-right: 10px">时</span>
              <el-input-number
                v-model="orderDeadlineValues.minute"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateOrderDeadline"
              />
              <span style="margin-right: 10px">分</span>
              <span style="margin-left: 10px; color: #606266">
                {{ $options.filters.deadlineFilter(formData.order_deadline) }}
              </span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="取消截止" prop="cancellation_deadline">
            <div style="display: flex; align-items: center">
              <el-input-number
                v-model="cancellationDeadlineValues.month"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateCancellationDeadline"
              />
              <span style="margin-right: 10px">月</span>
              <el-input-number
                v-model="cancellationDeadlineValues.day"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateCancellationDeadline"
              />
              <span style="margin-right: 10px">天</span>
              <el-input-number
                v-model="cancellationDeadlineValues.hour"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateCancellationDeadline"
              />
              <span style="margin-right: 10px">时</span>
              <el-input-number
                v-model="cancellationDeadlineValues.minute"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 80px; margin-right: 5px"
                @change="calculateCancellationDeadline"
              />
              <span style="margin-right: 10px">分</span>
              <span style="margin-left: 10px; color: #606266">
                {{
                  $options.filters.deadlineFilter(
                    formData.cancellation_deadline
                  )
                }}
              </span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="最大可预订数" prop="quantity">
            <el-input-number
              v-model="formData.quantity"
              :min="0"
              controls-position="right"
              placeholder="请输入最大可预订数"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="开放预订时段" prop="generated_count">
            <el-input-number
              v-model="formData.generated_count"
              :min="0"
              controls-position="right"
              placeholder="请输入开放预订时段数"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>

    <!-- 开始时间设置对话框 -->
    <el-dialog
      title="设置开始时间"
      :visible.sync="showStartTimeDialog"
      width="60%"
      :append-to-body="true"
    >
      <CronTime v-model="tempStartTimeCron" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showStartTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmStartTime">确定</el-button>
      </div>
    </el-dialog>

    <!-- 结束时间设置对话框 -->
    <el-dialog
      title="设置结束时间"
      :visible.sync="showEndTimeDialog"
      width="60%"
      :append-to-body="true"
    >
      <CronTime v-model="tempEndTimeCron" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showEndTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmEndTime">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import CronTime from "@/components/CronTime/index.vue";
import { parseCronExpression } from "@/utils/cron";

export default {
  name: "BookingRuleItemDetail",
  components: {
    CronTime,
  },
  filters: {
    deadlineFilter(minutes) {
      if (!minutes || isNaN(parseInt(minutes))) return minutes;

      minutes = parseInt(minutes);
      if (minutes <= 0) return "0分钟前";

      const year = Math.floor(minutes / (60 * 24 * 365));
      minutes %= 60 * 24 * 365;

      const month = Math.floor(minutes / (60 * 24 * 30));
      minutes %= 60 * 24 * 30;

      const day = Math.floor(minutes / (60 * 24));
      minutes %= 60 * 24;

      const hour = Math.floor(minutes / 60);
      minutes %= 60;

      let result = "";
      if (year > 0) result += `${year}年`;
      if (month > 0) result += `${month}月`;
      if (day > 0) result += `${day}天`;
      if (hour > 0) result += `${hour}小时`;
      if (minutes > 0) result += `${minutes}分钟`;

      return result + "前";
    },
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "规则项详情",
    },
    data: {
      type: Object,
      default: () => ({
        id: undefined,
        rule_id: undefined,
        name: "",
        start_time_cron_str: "",
        end_time_cron_str: "",
        start_time: null,
        end_time: null,
        quantity: 0,
        order_deadline: 0,
        cancellation_deadline: 0,
        generated_count: 0,
      }),
    },
  },
  data() {
    return {
      formData: {
        id: undefined,
        rule_id: undefined,
        name: "",
        start_time_cron_str: "",
        end_time_cron_str: "",
        start_time: null,
        end_time: null,
        quantity: 0,
        order_deadline: 0,
        cancellation_deadline: 0,
        generated_count: 0,
      },
      ruleItemRules: {
        name: [
          { required: true, message: "请输入规则项名称", trigger: "blur" },
        ],
        start_time_cron_str: [
          { required: true, message: "请设置开始时间", trigger: "blur" },
        ],
        end_time_cron_str: [
          { required: true, message: "请设置结束时间", trigger: "blur" },
        ],
        quantity: [
          { required: true, message: "请设置最大可预订数", trigger: "blur" },
        ],
        generated_count: [
          { required: true, message: "请输入开放预订时段数", trigger: "blur" },
        ],
      },
      orderDeadlineValues: {
        month: 0,
        day: 0,
        hour: 0,
        minute: 0,
      },
      cancellationDeadlineValues: {
        month: 0,
        day: 0,
        hour: 0,
        minute: 0,
      },
      // 新增的数据属性
      showStartTimeDialog: false,
      showEndTimeDialog: false,
      tempStartTimeCron: "",
      tempEndTimeCron: "",
    };
  },
  computed: {
    startTimeDescription() {
      return this.formData.start_time_cron_str
        ? parseCronExpression(this.formData.start_time_cron_str) ||
            this.formData.start_time_cron_str
        : "请设置开始时间";
    },
    endTimeDescription() {
      return this.formData.end_time_cron_str
        ? parseCronExpression(this.formData.end_time_cron_str) ||
            this.formData.end_time_cron_str
        : "请设置结束时间";
    },
  },
  watch: {
    data: {
      handler(newData) {
        this.formData = { ...newData };
        this.parseDeadlineValues(newData.order_deadline, "order");
        this.parseDeadlineValues(newData.cancellation_deadline, "cancellation");
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleSubmit() {
      this.$refs.ruleItemForm.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.formData);
        }
      });
    },
    openStartTimeDialog() {
      this.tempStartTimeCron = this.formData.start_time_cron_str;
      this.showStartTimeDialog = true;
    },
    openEndTimeDialog() {
      this.tempEndTimeCron = this.formData.end_time_cron_str;
      this.showEndTimeDialog = true;
    },
    confirmStartTime() {
      this.formData.start_time_cron_str = this.tempStartTimeCron;
      this.showStartTimeDialog = false;
    },
    confirmEndTime() {
      this.formData.end_time_cron_str = this.tempEndTimeCron;
      this.showEndTimeDialog = false;
    },
    calculateOrderDeadline() {
      const { month, day, hour, minute } = this.orderDeadlineValues;
      const orderDeadline = (month * 30 + day) * 24 * 60 + hour * 60 + minute;
      this.formData.order_deadline = orderDeadline;
    },
    calculateCancellationDeadline() {
      const { month, day, hour, minute } = this.cancellationDeadlineValues;
      const cancellationDeadline =
        (month * 30 + day) * 24 * 60 + hour * 60 + minute;
      this.formData.cancellation_deadline = cancellationDeadline;
    },
    parseDeadlineValues(minutes, type) {
      if (!minutes || isNaN(parseInt(minutes))) {
        if (type === "order") {
          this.orderDeadlineValues = { month: 0, day: 0, hour: 0, minute: 0 };
        } else {
          this.cancellationDeadlineValues = {
            month: 0,
            day: 0,
            hour: 0,
            minute: 0,
          };
        }
        return;
      }

      minutes = parseInt(minutes);
      const values = { month: 0, day: 0, hour: 0, minute: 0 };

      // 计算月份 (按30天/月)
      values.month = Math.floor(minutes / (60 * 24 * 30));
      minutes %= 60 * 24 * 30;

      // 计算天数
      values.day = Math.floor(minutes / (60 * 24));
      minutes %= 60 * 24;

      // 计算小时
      values.hour = Math.floor(minutes / 60);
      minutes %= 60;

      // 剩余的分钟
      values.minute = minutes;

      // 设置对应类型的值
      if (type === "order") {
        this.orderDeadlineValues = values;
      } else {
        this.cancellationDeadlineValues = values;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.time-input-wrapper {
  display: flex;
  align-items: center;

  .el-input {
    flex: 1;
    margin-right: 10px;
  }

  .el-button {
    flex-shrink: 0;
  }
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
