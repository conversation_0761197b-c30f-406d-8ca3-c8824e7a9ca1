<template>
  <div class="rule-list">
    <h1>规则列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.name"
            placeholder="规则名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd"
          >新增规则
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="序号" width="65" align="center" />
      <el-table-column prop="name" label="名称" align="center">
        <template slot-scope="scope">
          <div class="rule-info">
            <span>{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120" align="center">
        <template slot-scope="scope">
          {{ $options.filters.typeFilter(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="scope" label="适用范围" width="100" align="center">
        <template slot-scope="scope">
          {{ $options.filters.scopeFilter(scope.row.scope) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="order_type"
        label="订单类型"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          {{ $options.filters.orderTypeFilter(scope.row.order_type) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)"
            >编辑
          </el-button>

          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>

          <el-button type="text" size="mini" @click="handleDelete(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑规则弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogFormVisible"
      width="90%"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="规则类型" prop="type">
          <el-select
            v-model="formData.type"
            :disabled="dialogTitle === '编辑规则'"
            placeholder="请选择规则类型"
            @change="handleTypeChange"
          >
            <el-option label="预订规则" value="reservation" />
            <el-option label="餐厅预订规则" value="dining_reservation" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="适用范围" prop="scope">
          <el-select v-model="formData.scope" placeholder="请选择适用范围">
            <el-option label="产品" value="product" />
            <el-option label="订单" value="order" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单类型" prop="order_type">
          <el-select v-model="formData.order_type" placeholder="请选择订单类型">
            <el-option label="无" value="none" />
            <el-option label="预订" value="reservation" />
            <el-option label="直接购买" value="direct" />
          </el-select>
        </el-form-item>

        <!-- 餐厅预订规则特有字段 -->
        <template v-if="formData.type === 'dining_reservation'">
          <el-form-item label="餐厅别名" prop="alias">
            <el-input
              v-model="formData.alias"
              placeholder="请输入餐厅别名，如：早餐、午餐、晚餐"
            />
          </el-form-item>
          <el-form-item label="用餐开始时间" prop="dining_start_time_cron_str">
            <div class="time-input-wrapper">
              <el-input
                v-model="diningStartTimeDescription"
                placeholder="请设置用餐开始时间"
                :readonly="true"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                type="primary"
                size="small"
                @click="openDiningStartTimeDialog"
                style="flex-shrink: 0"
              >
                设置
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="用餐结束时间" prop="dining_end_time_cron_str">
            <div class="time-input-wrapper">
              <el-input
                v-model="diningEndTimeDescription"
                placeholder="请设置用餐结束时间"
                :readonly="true"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                type="primary"
                size="small"
                @click="openDiningEndTimeDialog"
                style="flex-shrink: 0"
              >
                设置
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="核销开始时间" prop="verify_start_time_cron_str">
            <div class="time-input-wrapper">
              <el-input
                v-model="verifyStartTimeDescription"
                placeholder="请设置核销开始时间"
                :readonly="true"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                type="primary"
                size="small"
                @click="openVerifyStartTimeDialog"
                style="flex-shrink: 0"
              >
                设置
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="核销结束时间" prop="verify_end_time_cron_str">
            <div class="time-input-wrapper">
              <el-input
                v-model="verifyEndTimeDescription"
                placeholder="请设置核销结束时间"
                :readonly="true"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                type="primary"
                size="small"
                @click="openVerifyEndTimeDialog"
                style="flex-shrink: 0"
              >
                设置
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="预订截止时间(分钟)" prop="order_deadline">
            <el-input-number
              v-model="formData.order_deadline"
              :min="0"
              :step="1"
              placeholder="预订截止前多少分钟"
            />
            <span class="input-tip">提前多少分钟截止预订</span>
          </el-form-item>
          <el-form-item label="取消截止时间(分钟)" prop="cancellation_deadline">
            <el-input-number
              v-model="formData.cancellation_deadline"
              :min="0"
              :step="1"
              placeholder="取消截止前多少分钟"
            />
            <span class="input-tip">提前多少分钟截止取消</span>
          </el-form-item>
          <el-form-item label="是否自动核销" prop="is_auto_verify">
            <el-switch
              v-model="formData.is_auto_verify"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
          <el-form-item label="生成数量" prop="generated_count">
            <el-input-number
              v-model="formData.generated_count"
              :min="1"
              :step="1"
              placeholder="生成预订时段数量"
            />
            <span class="input-tip">生成多少个预订时段</span>
          </el-form-item>
          <el-form-item label="最大预订数量" prop="quantity">
            <el-input-number
              v-model="formData.quantity"
              :min="1"
              :step="1"
              placeholder="每个时段最大预订数量"
            />
          </el-form-item>
        </template>
      </el-form>

      <!-- 规则项列表 - 仅对一般预订规则显示 -->
      <div class="rule-items-section" v-if="formData.type === 'reservation'">
        <div class="rule-items-header">
          <h3>规则项列表</h3>
          <div class="rule-items-actions">
            <el-button type="primary" size="mini" @click="handleAddRuleItem"
              >新增规则项
            </el-button>
            <el-button
              type="danger"
              size="mini"
              @click="handleDeleteRuleItems"
              :disabled="!selectedRuleItems.length"
              >删除规则项
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="ruleItemsLoading"
          :data="ruleItems"
          border
          fit
          highlight-current-row
          @selection-change="handleRuleItemSelectionChange"
          style="width: 100%; margin-top: 10px"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!--          <el-table-column prop="id" label="序号" width="65" align="center" />-->
          <el-table-column
            prop="order"
            label="序号"
            width="55"
            align="center"
          />
          <el-table-column prop="name" label="规则项名称" align="center" />
          <el-table-column
            prop="start_time_cron_str"
            label="开始时间"
            align="center"
          >
            <template slot-scope="scope">
              {{
                $options.filters.cronStartTimeFilter(
                  scope.row.start_time_cron_str
                )
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="end_time_cron_str"
            label="结束时间"
            align="center"
          >
            <template slot-scope="scope">
              {{
                $options.filters.cronEndTimeFilter(scope.row.end_time_cron_str)
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="order_deadline"
            label="预订截止"
            align="center"
          >
            <template slot-scope="scope">
              {{ $options.filters.deadlineFilter(scope.row.order_deadline) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="cancellation_deadline"
            label="取消截止"
            align="center"
          >
            <template slot-scope="scope">
              {{
                $options.filters.deadlineFilter(scope.row.cancellation_deadline)
              }}
            </template>
          </el-table-column>
          />
          <el-table-column
            prop="generated_count"
            label="开放预订时段"
            width="65"
            align="center"
          />
          <el-table-column
            prop="quantity"
            label="最大可预订数"
            width="65"
            align="center"
          />
          <el-table-column label="操作" align="center" width="160">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="handleEditRuleItem(scope.row)"
                >编辑
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="handleDeleteRuleItem(scope.row)"
                >删除
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="handlePreviewTime(scope.row)"
                >预览时间
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="!formData.id" type="primary" @click="saveRule"
          >保存</el-button
        >
        <el-button v-else type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增/编辑规则弹窗结束 -->

    <!-- 新增/编辑规则项对话框 -->
    <BookingRuleItemDetail
      :visible="ruleItemDialogVisible"
      :title="ruleItemDialogTitle"
      :data="ruleItemFormData"
      @close="ruleItemDialogVisible = false"
      @submit="submitRuleItemForm"
    />
    <!-- 新增/编辑规则项对话框结束 -->

    <!-- 预览时间对话框 -->
    <el-dialog
      title="预览时间"
      :visible.sync="timePreviewDialogVisible"
      width="80%"
    >
      <div class="time-preview-content">
        <div
          v-if="
            previewData.executionTimes && previewData.executionTimes.length > 0
          "
          class="execution-times-section"
        >
          <h3>
            开放预订时段列表 (共{{ previewData.executionTimes.length }}个)
          </h3>
          <el-table
            :data="previewData.executionTimes"
            border
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="序号"
              width="80"
            ></el-table-column>
            <el-table-column label="开始时间">
              <template slot-scope="scope">
                {{
                  scope.row.startTime
                    ? scope.row.startTime.toLocaleString("zh-CN")
                    : "无法计算"
                }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间">
              <template slot-scope="scope">
                {{
                  scope.row.endTime
                    ? scope.row.endTime.toLocaleString("zh-CN")
                    : "无法计算"
                }}
              </template>
            </el-table-column>
            <el-table-column label="预订截止时间">
              <template slot-scope="scope">
                {{
                  scope.row.orderDeadline
                    ? scope.row.orderDeadline.toLocaleString("zh-CN")
                    : "无法计算"
                }}
              </template>
            </el-table-column>
            <el-table-column label="取消截止时间">
              <template slot-scope="scope">
                {{
                  scope.row.cancellationDeadline
                    ? scope.row.cancellationDeadline.toLocaleString("zh-CN")
                    : "无法计算"
                }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="timePreviewDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 预览时间对话框结束 -->

    <!-- 用餐开始时间设置对话框 -->
    <el-dialog
      title="设置用餐开始时间"
      :visible.sync="showDiningStartTimeDialog"
      width="60%"
      :append-to-body="true"
    >
      <CronTime v-model="tempDiningStartTimeCron" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDiningStartTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmDiningStartTime"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 用餐结束时间设置对话框 -->
    <el-dialog
      title="设置用餐结束时间"
      :visible.sync="showDiningEndTimeDialog"
      width="60%"
      :append-to-body="true"
    >
      <CronTime v-model="tempDiningEndTimeCron" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDiningEndTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmDiningEndTime">确定</el-button>
      </div>
    </el-dialog>

    <!-- 核销开始时间设置对话框 -->
    <el-dialog
      title="设置核销开始时间"
      :visible.sync="showVerifyStartTimeDialog"
      width="60%"
      :append-to-body="true"
    >
      <CronTime v-model="tempVerifyStartTimeCron" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showVerifyStartTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmVerifyStartTime"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 核销结束时间设置对话框 -->
    <el-dialog
      title="设置核销结束时间"
      :visible.sync="showVerifyEndTimeDialog"
      width="60%"
      :append-to-body="true"
    >
      <CronTime v-model="tempVerifyEndTimeCron" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showVerifyEndTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmVerifyEndTime">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import {
  parseCronExpression,
  getNextCronTime,
  calculateDeadlineTime,
  generateMultipleExecutionTimes,
} from "@/utils/cron";
import CronTime from "@/components/CronTime/index.vue";
import BookingRuleItemDetail from "./booking-rule-item-detail.vue";

export default {
  name: "RuleList",
  components: {
    CronTime,
    BookingRuleItemDetail,
  },
  filters: {
    typeFilter(type) {
      const typeMap = {
        reservation: "预订规则",
        dining_reservation: "餐厅预订规则",
      };
      return typeMap[type] || "";
    },
    scopeFilter(scope) {
      const scopeMap = {
        product: "产品",
        order: "订单",
      };
      return scopeMap[scope] || "";
    },
    orderTypeFilter(orderType) {
      const orderTypeMap = {
        none: "无",
        reservation: "预订",
        direct: "直接购买",
      };
      return orderTypeMap[orderType] || "";
    },
    cronStartTimeFilter(cronStr) {
      return parseCronExpression(cronStr) || cronStr;
    },
    cronEndTimeFilter(cronStr) {
      return parseCronExpression(cronStr) || cronStr;
    },
    deadlineFilter(minutes) {
      if (!minutes || isNaN(parseInt(minutes))) return minutes;

      minutes = parseInt(minutes);
      if (minutes <= 0) return "0分钟前";

      const year = Math.floor(minutes / (60 * 24 * 365));
      minutes %= 60 * 24 * 365;

      const month = Math.floor(minutes / (60 * 24 * 30));
      minutes %= 60 * 24 * 30;

      const day = Math.floor(minutes / (60 * 24));
      minutes %= 60 * 24;

      const hour = Math.floor(minutes / 60);
      minutes %= 60;

      let result = "";
      if (year > 0) result += `${year}年`;
      if (month > 0) result += `${month}月`;
      if (day > 0) result += `${day}天`;
      if (hour > 0) result += `${hour}小时`;
      if (minutes > 0) result += `${minutes}分钟`;

      return result + "前";
    },
  },
  data() {
    return {
      searchForm: {
        name: "",
      },
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      dialogFormVisible: false,
      dialogTitle: "",
      formData: {
        id: undefined,
        name: "",
        type: "reservation",
        status: 1,
        scope: "product",
        order_type: "none",
        // 餐厅预订规则特有字段
        alias: "",
        dining_start_time_cron_str: "",
        dining_end_time_cron_str: "",
        verify_start_time_cron_str: "",
        verify_end_time_cron_str: "",
        order_deadline: 120,
        cancellation_deadline: 60,
        is_auto_verify: false,
        generated_count: 1,
        quantity: 50,
      },
      rules: {
        name: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择规则类型", trigger: "change" },
        ],
        scope: [
          { required: true, message: "请选择适用范围", trigger: "change" },
        ],
        order_type: [
          { required: true, message: "请选择订单类型", trigger: "change" },
        ],
        alias: [
          { required: false, message: "请输入餐厅别名", trigger: "blur" },
        ],
        dining_start_time_cron_str: [
          { required: false, message: "请选择用餐开始时间", trigger: "change" },
        ],
        dining_end_time_cron_str: [
          { required: false, message: "请选择用餐结束时间", trigger: "change" },
        ],
        verify_start_time_cron_str: [
          { required: false, message: "请选择核销开始时间", trigger: "change" },
        ],
        verify_end_time_cron_str: [
          { required: false, message: "请选择核销结束时间", trigger: "change" },
        ],
        order_deadline: [
          { required: false, message: "请输入预订截止时间", trigger: "blur" },
        ],
        cancellation_deadline: [
          { required: false, message: "请输入取消截止时间", trigger: "blur" },
        ],
        generated_count: [
          { required: false, message: "请输入生成数量", trigger: "blur" },
        ],
        quantity: [
          { required: false, message: "请输入最大预订数量", trigger: "blur" },
        ],
      },
      ruleItems: [],
      selectedRuleItems: [],
      ruleItemsLoading: false,
      ruleItemDialogVisible: false,
      ruleItemDialogTitle: "",
      ruleItemFormData: {
        id: undefined,
        rule_id: undefined,
        name: "",
        start_time_cron_str: "",
        end_time_cron_str: "",
        start_time: null,
        end_time: null,
        quantity: 0,
        order_deadline: 0,
        cancellation_deadline: 0,
        generated_count: 0,
      },

      timePreviewDialogVisible: false,
      previewData: {
        startTime: null,
        endTime: null,
        orderDeadline: null,
        cancellationDeadline: null,
        executionTimes: [],
      },

      // 时间设置对话框相关
      showDiningStartTimeDialog: false,
      showDiningEndTimeDialog: false,
      showVerifyStartTimeDialog: false,
      showVerifyEndTimeDialog: false,
      tempDiningStartTimeCron: "",
      tempDiningEndTimeCron: "",
      tempVerifyStartTimeCron: "",
      tempVerifyEndTimeCron: "",
    };
  },
  computed: {
    diningStartTimeDescription() {
      return this.formData.dining_start_time_cron_str
        ? parseCronExpression(this.formData.dining_start_time_cron_str) ||
            this.formData.dining_start_time_cron_str
        : "请设置用餐开始时间";
    },
    diningEndTimeDescription() {
      return this.formData.dining_end_time_cron_str
        ? parseCronExpression(this.formData.dining_end_time_cron_str) ||
            this.formData.dining_end_time_cron_str
        : "请设置用餐结束时间";
    },
    verifyStartTimeDescription() {
      return this.formData.verify_start_time_cron_str
        ? parseCronExpression(this.formData.verify_start_time_cron_str) ||
            this.formData.verify_start_time_cron_str
        : "请设置核销开始时间";
    },
    verifyEndTimeDescription() {
      return this.formData.verify_end_time_cron_str
        ? parseCronExpression(this.formData.verify_end_time_cron_str) ||
            this.formData.verify_end_time_cron_str
        : "请设置核销结束时间";
    },
  },
  created() {
    console.log("RuleList组件已创建");
    this.getRuleList();
  },
  methods: {
    // 获取规则列表
    getRuleList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getRulesBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("API响应:", response.data.list);
          console.log(response);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          }
        })
        .catch((error) => {
          console.error("获取规则列表失败", error);
          this.$message.error("获取规则列表失败");
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSearch() {
      this.pagination.page = 1;
      this.getRuleList();
    },
    resetSearch() {
      this.searchForm = {
        name: "",
      };
      this.getRuleList();
    },

    handleAdd() {
      this.dialogTitle = "新增规则";
      this.formData = {
        id: undefined,
        name: "",
        type: "reservation",
        status: 1,
        scope: "product",
        order_type: "none",
        // 餐厅预订规则特有字段
        alias: "",
        dining_start_time_cron_str: "",
        dining_end_time_cron_str: "",
        verify_start_time_cron_str: "",
        verify_end_time_cron_str: "",
        order_deadline: 120,
        cancellation_deadline: 60,
        is_auto_verify: false,
        generated_count: 1,
        quantity: 50,
      };
      this.ruleItems = [];
      this.dialogFormVisible = true;
    },

    // 处理规则类型变化
    handleTypeChange(type) {
      // 当切换规则类型时，更新表单验证规则
      this.$nextTick(() => {
        if (type === "dining_reservation") {
          // 为餐厅预订规则添加必填验证
          this.rules.alias[0].required = true;
          this.rules.dining_start_time_cron_str[0].required = true;
          this.rules.dining_end_time_cron_str[0].required = true;
          this.rules.verify_start_time_cron_str[0].required = true;
          this.rules.verify_end_time_cron_str[0].required = true;
          this.rules.order_deadline[0].required = true;
          this.rules.cancellation_deadline[0].required = true;
          this.rules.generated_count[0].required = true;
          this.rules.quantity[0].required = true;
        } else {
          // 普通预订规则时，移除餐厅预订字段的必填验证
          this.rules.alias[0].required = false;
          this.rules.dining_start_time_cron_str[0].required = false;
          this.rules.dining_end_time_cron_str[0].required = false;
          this.rules.verify_start_time_cron_str[0].required = false;
          this.rules.verify_end_time_cron_str[0].required = false;
          this.rules.order_deadline[0].required = false;
          this.rules.cancellation_deadline[0].required = false;
          this.rules.generated_count[0].required = false;
          this.rules.quantity[0].required = false;
        }
      });
    },
    handleEdit(row) {
      this.dialogTitle = "编辑规则";

      // 获取规则详情
      this.listLoading = true;
      requestApi({
        name: "getRule",
        data: {
          rule_id: row.id,
        },
      })
        .then((response) => {
          this.listLoading = false;
          if (response && response.code === 200) {
            // 将API返回的数据映射到表单
            this.formData = {
              id: response.data.id || "",
              name: response.data.name || "",
              type: response.data.type || "reservation",
              status: response.data.status || 1,
              scope: response.data.scope || "product",
              order_type: response.data.order_type || "none",
              // 餐厅预订规则特有字段
              alias: response.data.alias || "",
              dining_start_time_cron_str:
                response.data.dining_start_time_cron_str || "",
              dining_end_time_cron_str:
                response.data.dining_end_time_cron_str || "",
              verify_start_time_cron_str:
                response.data.verify_start_time_cron_str || "",
              verify_end_time_cron_str:
                response.data.verify_end_time_cron_str || "",
              order_deadline: response.data.order_deadline || 120,
              cancellation_deadline: response.data.cancellation_deadline || 60,
              is_auto_verify: response.data.is_auto_verify || false,
              generated_count: response.data.generated_count || 1,
              quantity: response.data.quantity || 50,
            };
            this.dialogFormVisible = true;
            this.ruleItems = response.data.rule_items || [];
            // 获取规则项列表
            // this.getRuleItems(row.id);
          } else {
            this.$message.error(response.message || "获取规则详情失败1");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取规则详情失败2", error);
          this.$message.error("获取规则详情失败3");
        });
    },
    handleDelete(row) {
      this.$confirm("确认删除该规则?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "deleteRule",
            data: {
              id: row.id,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getRuleList(); // 刷新列表
              } else {
                this.$message.error(response.message || "删除规则失败");
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error("删除规则失败", error);
              this.$message.error("删除规则失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${statusText}该规则?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "updateRuleStatus",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: `规则已${statusText}!`,
                });
                row.status = newStatus; // 更新本地状态
                this.getRuleList(); // 刷新列表
              } else {
                this.$message.error(
                  response.message || `${statusText}规则失败`
                );
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error(`${statusText}规则失败`, error);
              this.$message.error(`${statusText}规则失败`);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    saveRule() {
      console.log("保存规则逻辑");
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 保存规则逻辑
          console.log("保存规则:", this.formData);
          const ruleData = {
            id: this.formData.id,
            name: this.formData.name,
            type: this.formData.type,
            status: this.formData.status,
            scope: this.formData.scope,
            order_type: this.formData.order_type,
          };

          // 如果是餐厅预订规则，添加特有字段
          if (this.formData.type === "dining_reservation") {
            ruleData.alias = this.formData.alias;
            ruleData.dining_start_time_cron_str =
              this.formData.dining_start_time_cron_str;
            ruleData.dining_end_time_cron_str =
              this.formData.dining_end_time_cron_str;
            ruleData.verify_start_time_cron_str =
              this.formData.verify_start_time_cron_str;
            ruleData.verify_end_time_cron_str =
              this.formData.verify_end_time_cron_str;
            ruleData.order_deadline = this.formData.order_deadline;
            ruleData.cancellation_deadline =
              this.formData.cancellation_deadline;
            ruleData.is_auto_verify = this.formData.is_auto_verify;
            ruleData.generated_count = this.formData.generated_count;
            ruleData.quantity = this.formData.quantity;
          }

          this.listLoading = true;
          requestApi({
            name: "addRule",
            data: ruleData,
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "规则已保存!",
                });
                this.formData.id = response.data.id; // 更新本地IDz
              } else {
                this.$message.error(response.message || "保存规则失败");
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error("保存规则失败", error);
              this.$message.error("保存规则失败");
            });
        }
      });
    },
    submitForm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (this.formData.id !== undefined) {
            // 编辑规则
            const ruleData = {
              id: this.formData.id,
              name: this.formData.name,
              type: this.formData.type,
              status: this.formData.status,
              scope: this.formData.scope,
              order_type: this.formData.order_type,
              rule_items: this.ruleItems, // 添加规则项列表
            };

            // 如果是餐厅预订规则，添加特有字段
            if (this.formData.type === "dining_reservation") {
              ruleData.alias = this.formData.alias;
              ruleData.dining_start_time_cron_str =
                this.formData.dining_start_time_cron_str;
              ruleData.dining_end_time_cron_str =
                this.formData.dining_end_time_cron_str;
              ruleData.verify_start_time_cron_str =
                this.formData.verify_start_time_cron_str;
              ruleData.verify_end_time_cron_str =
                this.formData.verify_end_time_cron_str;
              ruleData.order_deadline = this.formData.order_deadline;
              ruleData.cancellation_deadline =
                this.formData.cancellation_deadline;
              ruleData.is_auto_verify = this.formData.is_auto_verify;
              ruleData.generated_count = this.formData.generated_count;
              ruleData.quantity = this.formData.quantity;
            }

            this.listLoading = true;
            requestApi({
              name: "updateRule",
              data: ruleData,
            })
              .then((response) => {
                this.listLoading = false;
                if (response && response.code === 200) {
                  this.$message({
                    type: "success",
                    message: "规则信息已更新!",
                  });
                  this.dialogFormVisible = false;
                  this.getRuleList(); // 刷新列表
                } else {
                  this.$message.error(response.message || "更新规则失败");
                }
              })
              .catch((error) => {
                this.listLoading = false;
                console.error("更新规则失败", error);
                this.$message.error("更新规则失败");
              });
          } else {
            // 新增规则
            const ruleData = {
              name: this.formData.name,
              type: this.formData.type,
              status: this.formData.status,
              scope: this.formData.scope,
              order_type: this.formData.order_type,
              rule_items: this.ruleItems, // 添加规则项列表
            };

            // 如果是餐厅预订规则，添加特有字段
            if (this.formData.type === "dining_reservation") {
              ruleData.alias = this.formData.alias;
              ruleData.dining_start_time_cron_str =
                this.formData.dining_start_time_cron_str;
              ruleData.dining_end_time_cron_str =
                this.formData.dining_end_time_cron_str;
              ruleData.verify_start_time_cron_str =
                this.formData.verify_start_time_cron_str;
              ruleData.verify_end_time_cron_str =
                this.formData.verify_end_time_cron_str;
              ruleData.order_deadline = this.formData.order_deadline;
              ruleData.cancellation_deadline =
                this.formData.cancellation_deadline;
              ruleData.is_auto_verify = this.formData.is_auto_verify;
              ruleData.generated_count = this.formData.generated_count;
              ruleData.quantity = this.formData.quantity;
            }

            this.listLoading = true;
            requestApi({
              name: "addRule",
              data: ruleData,
            })
              .then((response) => {
                this.listLoading = false;
                if (response && response.code === 200) {
                  this.$message({
                    type: "success",
                    message: "新增规则成功!",
                  });
                  this.dialogFormVisible = false;
                  this.getRuleList(); // 刷新列表
                } else {
                  this.$message.error(response.message || "新增规则失败");
                }
              })
              .catch((error) => {
                this.listLoading = false;
                console.error("新增规则失败", error);
                this.$message.error("新增规则失败");
              });
          }
        }
      });
    },
    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getRuleList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getRuleList();
    },

    handleAddRuleItem() {
      if (!this.formData.id) {
        this.$message.warning("请先保存规则后再添加规则项");
        return;
      }

      if (this.formData.type !== "reservation") {
        this.$message.warning("只有一般预订规则支持添加规则项");
        return;
      }

      this.ruleItemDialogTitle = "新增规则项";
      this.ruleItemFormData = {
        id: undefined,
        rule_id: this.formData.id,
        name: "",
        start_time_cron_str: "",
        end_time_cron_str: "",
        start_time: null,
        end_time: null,
        quantity: 0,
        order_deadline: 0,
        cancellation_deadline: 0,
        generated_count: 0,
      };

      this.ruleItemDialogVisible = true;
    },

    handleDeleteRuleItems() {
      if (!this.selectedRuleItems.length) {
        this.$message.warning("请至少选择一个规则项");
        return;
      }

      this.$confirm(
        `确认删除已选择的 ${this.selectedRuleItems.length} 个规则项?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.ruleItemsLoading = true;
          // 获取选中项的ID列表
          const selectedIds = this.selectedRuleItems.map((item) => item.id);

          // 创建删除规则项的请求队列
          const deletePromises = selectedIds.map((id) => {
            return requestApi({
              name: "delteRuleItem",
              data: { id },
            });
          });

          // 并行处理所有删除请求
          Promise.all(deletePromises)
            .then(() => {
              // 过滤掉被选中的项，保留未选中的项
              this.ruleItems = this.ruleItems.filter(
                (item) => !selectedIds.includes(item.id)
              );

              this.$message.success("规则项批量删除成功");
              this.selectedRuleItems = [];
              this.ruleItemsLoading = false;
            })
            .catch((error) => {
              console.error("批量删除规则项失败", error);
              this.$message.error("批量删除规则项失败");
              this.ruleItemsLoading = false;
            });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    handleRuleItemSelectionChange(selectedItems) {
      this.selectedRuleItems = selectedItems;
    },

    handleEditRuleItem(row) {
      this.ruleItemDialogTitle = "编辑规则项";
      this.ruleItemFormData = {
        id: row.id,
        rule_id: this.formData.id,
        name: row.name,
        start_time_cron_str: row.start_time_cron_str,
        end_time_cron_str: row.end_time_cron_str,
        quantity: row.quantity,
        order_deadline: row.order_deadline,
        cancellation_deadline: row.cancellation_deadline,
        generated_count: row.generated_count,
      };

      // 将cron表达式转换为天期对象
      this.ruleItemFormData.start_time = this.cronToDate(
        row.start_time_cron_str
      );
      this.ruleItemFormData.end_time = this.cronToDate(row.end_time_cron_str);

      this.ruleItemDialogVisible = true;
    },

    handleDeleteRuleItem(row) {
      this.$confirm("确认删除该规则项?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.ruleItemsLoading = true;

          // 调用API删除规则项
          requestApi({
            name: "delteRuleItem",
            data: {
              id: row.id,
            },
          })
            .then((response) => {
              this.ruleItemsLoading = false;
              if (response && response.code === 200) {
                // 从本地规则项列表中删除
                const index = this.ruleItems.findIndex(
                  (item) => item.id === row.id
                );
                if (index !== -1) {
                  this.ruleItems.splice(index, 1);
                }
                this.$message.success("规则项删除成功");
              } else {
                this.$message.error(response.message || "删除规则项失败");
              }
            })
            .catch((error) => {
              this.ruleItemsLoading = false;
              console.error("删除规则项失败", error);
              this.$message.error("删除规则项失败");
            });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 获取规则项列表
    getRuleItems(ruleId) {
      if (!ruleId) return;

      this.ruleItemsLoading = true;
      requestApi({
        name: "getRuleItems",
        data: {
          rule_id: ruleId,
        },
      })
        .then((response) => {
          this.ruleItemsLoading = false;
          if (response && response.code === 200) {
            this.ruleItems = response.data || [];
          } else {
            this.$message.error(response.message || "获取规则项列表失败");
          }
        })
        .catch((error) => {
          this.ruleItemsLoading = false;
          console.error("获取规则项列表失败", error);
          this.$message.error("获取规则项列表失败");
        });
    },

    // 提交规则项表单
    submitRuleItemForm(formData) {
      this.ruleItemsLoading = true;

      // 准备要提交的数据
      const submitData = {
        id: formData.id,
        rule_id: this.formData.id,
        name: formData.name,
        start_time_cron_str: formData.start_time_cron_str,
        end_time_cron_str: formData.end_time_cron_str,
        quantity: formData.quantity,
        order_deadline: formData.order_deadline,
        cancellation_deadline: formData.cancellation_deadline,
        generated_count: formData.generated_count,
      };

      if (formData.id) {
        // 编辑规则项 - 调用更新API
        requestApi({
          name: "updateRuleItem",
          data: {
            ...submitData,
            id: formData.id,
          },
        })
          .then((response) => {
            this.ruleItemsLoading = false;
            if (response && response.code === 200) {
              // 更新本地数据
              const index = this.ruleItems.findIndex(
                (item) => item.id === formData.id
              );
              if (index !== -1) {
                this.$set(this.ruleItems, index, {
                  ...this.ruleItems[index],
                  ...submitData,
                });
              }
              this.$message.success("规则项更新成功");
              this.ruleItemDialogVisible = false;
            } else {
              this.$message.error(response.message || "更新规则项失败");
            }
          })
          .catch((error) => {
            this.ruleItemsLoading = false;
            console.error("更新规则项失败", error);
            this.$message.error("更新规则项失败");
          });
      } else {
        // 新增规则项 - 调用创建API
        requestApi({
          name: "addRuleItem",
          data: submitData,
        })
          .then((response) => {
            this.ruleItemsLoading = false;
            if (response && response.code === 200) {
              // 添加到规则项列表，使用返回的ID
              submitData.id = response.data.id || null;
              this.ruleItems.push(submitData);
              this.$message.success("规则项添加成功");
              this.ruleItemDialogVisible = false;
            } else {
              this.$message.error(response.message || "添加规则项失败");
            }
          })
          .catch((error) => {
            this.ruleItemsLoading = false;
            console.error("添加规则项失败", error);
            this.$message.error("添加规则项失败");
          });
      }
    },

    // 将cron表达式转换为Date对象
    cronToDate(cronStr) {
      if (!cronStr) return null;

      // 解析cron表达式，格式为：分 时 * * *
      const parts = cronStr.split(" ");
      if (parts.length < 2) return null;

      const minutes = parseInt(parts[0]);
      const hours = parseInt(parts[1]);

      if (isNaN(minutes) || isNaN(hours)) return null;

      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      date.setSeconds(0);

      return date;
    },

    handlePreviewTime(row) {
      // 获取下一次开始时间
      const nextStartTime = getNextCronTime(row.start_time_cron_str);
      const nextEndTime = getNextCronTime(row.end_time_cron_str);

      // 根据开始时间计算预订截止和取消截止时间
      let orderDeadline = null;
      let cancellationDeadline = null;

      if (nextStartTime && row.order_deadline) {
        orderDeadline = calculateDeadlineTime(
          nextStartTime,
          row.order_deadline
        );
      }

      if (nextStartTime && row.cancellation_deadline) {
        cancellationDeadline = calculateDeadlineTime(
          nextStartTime,
          row.cancellation_deadline
        );
      }

      // 生成多个预览时间
      const executionCount = row.generated_count
        ? parseInt(row.generated_count)
        : 0;
      let executionTimes = [];

      if (executionCount > 0) {
        // 获取多个开始时间
        const startTimes = generateMultipleExecutionTimes(
          row.start_time_cron_str,
          executionCount
        );
        const endTimes = generateMultipleExecutionTimes(
          row.end_time_cron_str,
          executionCount
        );

        // 组合成时间段对象数组
        executionTimes = startTimes.map((startTime, index) => {
          const endTime = endTimes[index] || null;

          // 计算每个时间段的预订截止和取消截止时间
          let itemOrderDeadline = null;
          let itemCancellationDeadline = null;

          if (startTime && row.order_deadline) {
            itemOrderDeadline = calculateDeadlineTime(
              startTime,
              row.order_deadline
            );
          }

          if (startTime && row.cancellation_deadline) {
            itemCancellationDeadline = calculateDeadlineTime(
              startTime,
              row.cancellation_deadline
            );
          }

          return {
            startTime,
            endTime,
            orderDeadline: itemOrderDeadline,
            cancellationDeadline: itemCancellationDeadline,
          };
        });
      }

      this.previewData = {
        startTime: nextStartTime,
        endTime: nextEndTime,
        orderDeadline: orderDeadline,
        cancellationDeadline: cancellationDeadline,
        executionTimes: executionTimes,
      };

      this.timePreviewDialogVisible = true;
    },

    // 时间设置对话框方法
    openDiningStartTimeDialog() {
      this.tempDiningStartTimeCron = this.formData.dining_start_time_cron_str;
      this.showDiningStartTimeDialog = true;
    },
    openDiningEndTimeDialog() {
      this.tempDiningEndTimeCron = this.formData.dining_end_time_cron_str;
      this.showDiningEndTimeDialog = true;
    },
    openVerifyStartTimeDialog() {
      this.tempVerifyStartTimeCron = this.formData.verify_start_time_cron_str;
      this.showVerifyStartTimeDialog = true;
    },
    openVerifyEndTimeDialog() {
      this.tempVerifyEndTimeCron = this.formData.verify_end_time_cron_str;
      this.showVerifyEndTimeDialog = true;
    },
    confirmDiningStartTime() {
      this.formData.dining_start_time_cron_str = this.tempDiningStartTimeCron;
      this.showDiningStartTimeDialog = false;
    },
    confirmDiningEndTime() {
      this.formData.dining_end_time_cron_str = this.tempDiningEndTimeCron;
      this.showDiningEndTimeDialog = false;
    },
    confirmVerifyStartTime() {
      this.formData.verify_start_time_cron_str = this.tempVerifyStartTimeCron;
      this.showVerifyStartTimeDialog = false;
    },
    confirmVerifyEndTime() {
      this.formData.verify_end_time_cron_str = this.tempVerifyEndTimeCron;
      this.showVerifyEndTimeDialog = false;
    },
  },
};
</script>

<style lang="less" scoped>
.rule-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .rule-info {
    display: flex;
    align-items: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .rule-items-section {
    margin-top: 20px;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;

    .rule-items-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      h3 {
        font-size: 16px;
        color: #303133;
        font-weight: 600;
        margin: 0;
      }

      .rule-items-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .time-preview-content {
    padding: 16px;

    .el-descriptions {
      margin-bottom: 20px;
    }

    .execution-times-section {
      margin-top: 30px;

      h3 {
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.input-tip {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}

.time-input-wrapper {
  display: flex;
  align-items: center;

  .el-input {
    flex: 1;
    margin-right: 10px;
  }

  .el-button {
    flex-shrink: 0;
  }
}
</style>
