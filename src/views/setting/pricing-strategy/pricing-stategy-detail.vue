<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="策略类型" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择策略类型"
          :disabled="formData.id !== undefined"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="策略名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入策略名称" />
      </el-form-item>

      <el-form-item label="策略描述" prop="description">
        <el-input
          type="textarea"
          v-model="formData.description"
          rows="4"
          placeholder="请输入策略描述"
        />
      </el-form-item>

      <el-form-item label="策略范围" prop="scope">
        <el-select v-model="formData.scope" placeholder="请选择策略范围">
          <el-option
            v-for="item in scopeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="生效时间" prop="start_time">
        <el-date-picker
          v-model="formData.start_time"
          type="datetime"
          placeholder="选择生效时间"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="结束时间" prop="end_time">
        <el-date-picker
          v-model="formData.end_time"
          type="datetime"
          placeholder="选择结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="策略状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>

      <!-- 根据策略类型显示不同的表单项 -->
      <!-- 折扣策略表单项 -->
      <template v-if="formData.type === 'discount'">
        <el-form-item label="折扣比例" prop="discount_rate">
          <el-input-number
            v-model="formData.discount_rate"
            :precision="2"
            :min="0"
            :max="1"
            :step="0.1"
          />
          <span class="form-tip">0-1之间的小数，如0.8表示8折</span>
        </el-form-item>

        <el-form-item label="最低消费金额" prop="min_amount">
          <el-input-number
            v-model="formData.min_amount"
            :precision="2"
            :min="0"
            :step="10"
          />
          <span class="form-tip">0表示无最低消费限制</span>
        </el-form-item>

        <el-form-item label="最大优惠金额" prop="max_discount">
          <el-input-number
            v-model="formData.max_discount"
            :precision="2"
            :min="0"
            :step="10"
          />
          <span class="form-tip">0表示无最大优惠限制</span>
        </el-form-item>
      </template>

      <!-- 满减策略表单项 -->
      <template v-if="formData.type === 'full_reduction'">
        <el-form-item label="满足金额" prop="full_amount">
          <el-input-number
            v-model="formData.full_amount"
            :precision="2"
            :min="0"
            :step="10"
          />
        </el-form-item>

        <el-form-item label="减免金额" prop="reduction_amount">
          <el-input-number
            v-model="formData.reduction_amount"
            :precision="2"
            :min="0"
            :step="10"
          />
        </el-form-item>
      </template>

      <!-- 会员价格策略表单项 -->
      <template v-if="formData.type === 'member_price'">
        <el-form-item label="会员等级" prop="member_level">
          <el-select
            v-model="formData.member_level"
            placeholder="请选择会员等级"
          >
            <el-option
              v-for="item in memberLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="会员价格" prop="price">
          <el-input-number
            v-model="formData.price"
            :precision="2"
            :min="0"
            :step="10"
          />
        </el-form-item>
      </template>

      <!-- 限时特价策略表单项 -->
      <template v-if="formData.type === 'time_limited'">
        <el-form-item label="特价金额" prop="special_price">
          <el-input-number
            v-model="formData.special_price"
            :precision="2"
            :min="0"
            :step="10"
          />
        </el-form-item>

        <el-form-item label="库存限制" prop="stock_limit">
          <el-input-number v-model="formData.stock_limit" :min="0" :step="1" />
          <span class="form-tip">0表示无库存限制</span>
        </el-form-item>
      </template>

      <!-- 捆绑订购策略表单项 -->
      <template v-if="formData.type === 'bundle'">
        <el-form-item label="减免金额" prop="deduction">
          <el-input-number
            v-model="formData.deduction"
            :precision="2"
            :min="0"
            :step="1"
          />
          <span class="form-tip">套餐优惠减免的金额</span>
        </el-form-item>

        <el-form-item label="使用周期" prop="usage_cycle">
          <el-select
            v-model="formData.usage_cycle"
            placeholder="请选择使用周期"
          >
            <el-option
              v-for="item in usageCycleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="使用限制" prop="usage_limit">
          <el-input-number v-model="formData.usage_limit" :min="1" :step="1" />
          <span class="form-tip">每个周期内可使用的次数</span>
        </el-form-item>

        <el-form-item label="是否互斥" prop="is_mutual_exclusive">
          <el-radio-group v-model="formData.is_mutual_exclusive">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <span class="form-tip">是否与其他优惠策略互斥</span>
        </el-form-item>

        <el-form-item label="需要重新匹配" prop="need_rematch">
          <el-radio-group v-model="formData.need_rematch">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <span class="form-tip">订单变更时是否需要重新匹配策略</span>
        </el-form-item>

        <el-form-item label="套餐商品" prop="products">
          <div class="product-list">
            <div
              v-for="(product, index) in formData.products"
              :key="index"
              class="product-item"
            >
              <el-select
                v-model="product.product_id"
                placeholder="请选择商品"
                filterable
                remote
                :remote-method="searchProducts"
                :loading="productLoading"
                style="width: 200px; margin-right: 10px"
                @change="handleProductChange(index)"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-input-number
                v-model="product.quantity"
                :min="1"
                :step="1"
                style="width: 120px; margin-right: 10px"
                placeholder="数量"
                @change="handleQuantityChange"
              />
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="removeProduct(index)"
                :disabled="formData.products.length <= 1"
              >
                删除
              </el-button>
            </div>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="addProduct"
              style="margin-top: 10px"
            >
              添加商品
            </el-button>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";

export default {
  name: "PricingStrategyDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      formData: {
        id: undefined,
        name: "",
        type: "discount",
        description: "",
        start_time: "",
        end_time: "",
        scope: "product",
        status: 1,
        // 折扣策略字段
        discount_rate: 1,
        min_amount: 0,
        max_discount: 0,
        // 满减策略字段
        full_amount: 0,
        reduction_amount: 0,
        // 会员价格策略字段
        member_level: "basic",
        price: 0,
        // 限时特价策略字段
        special_price: 0,
        stock_limit: 0,
        // 套餐策略字段
        deduction: 0,
        usage_cycle: "per_order",
        usage_limit: 1,
        is_mutual_exclusive: false,
        need_rematch: false,
        products: [{ product_id: null, quantity: 1 }],
      },
      typeOptions: [
        { label: "折扣", value: "discount" },
        { label: "满减", value: "full_reduction" },
        { label: "会员价", value: "member_price" },
        { label: "限时特价", value: "time_limited" },
        { label: "捆绑订购价", value: "bundle" },
      ],
      scopeOptions: [
        { label: "商品", value: "product" },
        { label: "订单", value: "order" },
      ],
      memberLevelOptions: [
        { label: "普通用户", value: "none" },
        { label: "基础会员", value: "basic" },
        { label: "高级会员", value: "premium" },
        { label: "VIP会员", value: "vip" },
      ],
      statusOptions: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 },
      ],
      usageCycleOptions: [
        { label: "每订单", value: "per_order" },
        { label: "每天", value: "per_day" },
        { label: "每周", value: "per_week" },
        { label: "每月", value: "per_month" },
        { label: "每年", value: "per_year" },
      ],
      productOptions: [],
      productLoading: false,
      rules: {
        name: [{ required: true, message: "请输入策略名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择策略类型", trigger: "change" },
        ],
        scope: [
          { required: true, message: "请选择策略范围", trigger: "change" },
        ],
        start_time: [
          { required: true, message: "请选择生效时间", trigger: "change" },
        ],
        end_time: [
          { required: true, message: "请选择结束时间", trigger: "change" },
        ],
        // 折扣策略验证规则
        discount_rate: [
          { required: true, message: "请输入折扣比例", trigger: "blur" },
        ],
        // 满减策略验证规则
        full_amount: [
          { required: true, message: "请输入满足金额", trigger: "blur" },
        ],
        reduction_amount: [
          { required: true, message: "请输入减免金额", trigger: "blur" },
        ],
        // 会员价格策略验证规则
        member_level: [
          { required: true, message: "请选择会员等级", trigger: "change" },
        ],
        price: [{ required: true, message: "请输入会员价格", trigger: "blur" }],
        // 限时特价策略验证规则
        special_price: [
          { required: true, message: "请输入特价金额", trigger: "blur" },
        ],
        deduction: [
          { required: true, message: "请输入减免金额", trigger: "blur" },
        ],
        usage_cycle: [
          { required: true, message: "请选择使用周期", trigger: "change" },
        ],
        usage_limit: [
          { required: true, message: "请输入使用限制", trigger: "blur" },
        ],
        is_mutual_exclusive: [
          { required: true, message: "请选择是否互斥", trigger: "change" },
        ],
        need_rematch: [
          {
            required: true,
            message: "请选择是否需要重新匹配",
            trigger: "change",
          },
        ],
        products: [
          {
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error("请至少添加一个商品"));
              } else {
                const hasEmptyProduct = value.some(
                  (product) => !product.product_id
                );
                if (hasEmptyProduct) {
                  callback(new Error("请选择所有商品"));
                } else {
                  callback();
                }
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    dialogTitle() {
      return this.editData ? "编辑价格策略" : "新增价格策略";
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData();
      }
    },
    editData: {
      handler(val) {
        if (val && this.visible) {
          this.loadEditData(val);
        }
      },
      deep: true,
    },
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.editData) {
        this.loadEditData(this.editData);
      } else {
        // 新增时重置表单
        this.formData = {
          id: undefined,
          name: "",
          type: "discount",
          description: "",
          start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
          end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
          scope: "product",
          status: 1,
          // 折扣策略字段
          discount_rate: 1,
          min_amount: 0,
          max_discount: 0,
          // 满减策略字段
          full_amount: 0,
          reduction_amount: 0,
          // 会员价格策略字段
          member_level: "basic",
          price: 0,
          // 限时特价策略字段
          special_price: 0,
          stock_limit: 0,
          // 套餐策略字段
          deduction: 0,
          usage_cycle: "per_order",
          usage_limit: 1,
          is_mutual_exclusive: false,
          need_rematch: false,
          products: [{ product_id: null, quantity: 1 }],
        };
      }
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.dataForm) {
          this.$refs.dataForm.clearValidate();
        }
      });
    },

    // 加载编辑数据
    loadEditData(editData) {
      this.loading = true;
      requestApi({
        name: "getPricingStrategy",
        data: {
          id: editData.id,
        },
      })
        .then((response) => {
          this.loading = false;
          if (response && response.code === 200) {
            const strategyData = response.data;

            // 基础字段
            this.formData = {
              id: strategyData.id,
              name: strategyData.name,
              type: strategyData.type,
              description: strategyData.description,
              start_time: strategyData.start_time,
              end_time: strategyData.end_time,
              scope: strategyData.scope,
              status: strategyData.status,
              // 初始化特定字段
              discount_rate: 1,
              min_amount: 0,
              max_discount: 0,
              full_amount: 0,
              reduction_amount: 0,
              member_level: "basic",
              price: 0,
              special_price: 0,
              stock_limit: 0,
              deduction: 0,
              usage_cycle: "per_order",
              usage_limit: 1,
              is_mutual_exclusive: false,
              need_rematch: false,
              products: [{ product_id: null, quantity: 1 }],
            };

            // 根据策略类型，映射对应的特殊字段
            switch (strategyData.type) {
              case "discount":
                this.formData.discount_rate = strategyData.discount_rate;
                this.formData.min_amount = strategyData.min_amount;
                this.formData.max_discount = strategyData.max_discount;
                break;
              case "full_reduction":
                this.formData.full_amount = strategyData.full_amount;
                this.formData.reduction_amount = strategyData.reduction_amount;
                break;
              case "member_price":
                this.formData.member_level = strategyData.member_level;
                this.formData.price = strategyData.price;
                break;
              case "time_limited":
                this.formData.special_price = strategyData.special_price;
                this.formData.stock_limit = strategyData.stock_limit;
                break;
              case "bundle":
                this.formData.deduction = strategyData.deduction;
                this.formData.usage_cycle = strategyData.usage_cycle;
                this.formData.usage_limit = strategyData.usage_limit;
                this.formData.is_mutual_exclusive =
                  strategyData.is_mutual_exclusive;
                this.formData.need_rematch = strategyData.need_rematch;
                this.formData.products = strategyData.products.map(
                  (product) => ({
                    product_id: product.product_id,
                    quantity: product.quantity,
                  })
                );
                break;
            }
          } else {
            this.$message.error(response.message || "获取价格策略详情失败1");
            this.handleCancel();
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error("获取价格策略详情失败2", error);
          this.$message.error("获取价格策略详情失败3");
          this.handleCancel();
        });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 组装提交数据
          let strategyData = {
            name: this.formData.name,
            type: this.formData.type,
            description: this.formData.description,
            start_time: this.formData.start_time,
            end_time: this.formData.end_time,
            scope: this.formData.scope,
            status: this.formData.status,
          };

          // 根据策略类型，添加特定字段
          switch (this.formData.type) {
            case "discount":
              strategyData.discount_rate = this.formData.discount_rate;
              strategyData.min_amount = this.formData.min_amount;
              strategyData.max_discount = this.formData.max_discount;
              break;
            case "full_reduction":
              strategyData.full_amount = this.formData.full_amount;
              strategyData.reduction_amount = this.formData.reduction_amount;
              break;
            case "member_price":
              strategyData.member_level = this.formData.member_level;
              strategyData.price = this.formData.price;
              break;
            case "time_limited":
              strategyData.special_price = this.formData.special_price;
              strategyData.stock_limit = this.formData.stock_limit;
              break;
            case "bundle":
              strategyData.deduction = this.formData.deduction;
              strategyData.usage_cycle = this.formData.usage_cycle;
              strategyData.usage_limit = this.formData.usage_limit;
              strategyData.is_mutual_exclusive =
                this.formData.is_mutual_exclusive;
              strategyData.need_rematch = this.formData.need_rematch;
              strategyData.products = this.formData.products.map((product) => ({
                product_id: product.product_id,
                quantity: product.quantity,
              }));
              break;
          }

          this.loading = true;

          if (this.formData.id !== undefined) {
            // 编辑策略
            strategyData.id = this.formData.id;

            requestApi({
              name: "updatePricingStrategy",
              data: strategyData,
            })
              .then((response) => {
                this.loading = false;
                if (response && response.code === 200) {
                  this.$message({
                    type: "success",
                    message: "价格策略已更新!",
                  });
                  this.$emit("success");
                  this.handleCancel();
                } else {
                  this.$message.error(response.message || "更新价格策略失败");
                }
              })
              .catch((error) => {
                this.loading = false;
                console.error("更新价格策略失败", error);
                this.$message.error("更新价格策略失败");
              });
          } else {
            // 新增策略
            requestApi({
              name: "addPricingStrategy",
              data: strategyData,
            })
              .then((response) => {
                this.loading = false;
                if (response && response.code === 200) {
                  this.$message({
                    type: "success",
                    message: "新增价格策略成功!",
                  });
                  this.$emit("success");
                  this.handleCancel();
                } else {
                  this.$message.error(response.message || "新增价格策略失败");
                }
              })
              .catch((error) => {
                this.loading = false;
                console.error("新增价格策略失败", error);
                this.$message.error("新增价格策略失败");
              });
          }
        }
      });
    },

    // 取消
    handleCancel() {
      this.$emit("close");
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },

    // 搜索商品
    searchProducts(query) {
      if (query !== "") {
        this.productLoading = true;
        requestApi({
          name: "getProductsBySearch",
          data: {
            name: query,
            page: 1,
            pageSize: 20,
          },
        })
          .then((response) => {
            this.productLoading = false;
            if (response && response.code === 200) {
              this.productOptions = (response.data.list || []).map(
                (product) => ({
                  id: product.id,
                  name: product.name,
                  price: product.price,
                })
              );
            } else {
              this.$message.error(response.message || "搜索商品失败");
            }
          })
          .catch((error) => {
            this.productLoading = false;
            console.error("搜索商品失败", error);
            this.$message.error("搜索商品失败");
          });
      } else {
        this.productOptions = [];
      }
    },

    // 处理商品选择
    handleProductChange(index) {
      // 可以在这里添加商品选择后的逻辑，比如自动设置数量等
      const product = this.formData.products[index];
      if (product.product_id && !product.quantity) {
        product.quantity = 1;
      }
      // 触发验证
      this.$nextTick(() => {
        if (this.$refs.dataForm) {
          this.$refs.dataForm.validateField("products");
        }
      });
    },

    // 添加商品
    addProduct() {
      this.formData.products.push({
        product_id: null,
        quantity: 1,
      });
      // 触发验证
      this.$nextTick(() => {
        if (this.$refs.dataForm) {
          this.$refs.dataForm.validateField("products");
        }
      });
    },

    // 移除商品
    removeProduct(index) {
      if (this.formData.products.length > 1) {
        this.formData.products.splice(index, 1);
        // 触发验证
        this.$nextTick(() => {
          if (this.$refs.dataForm) {
            this.$refs.dataForm.validateField("products");
          }
        });
      }
    },

    // 处理数量变化
    handleQuantityChange() {
      // 触发验证
      this.$nextTick(() => {
        if (this.$refs.dataForm) {
          this.$refs.dataForm.validateField("products");
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.product-list {
  margin-top: 10px;
  .product-item {
    margin-bottom: 10px;
  }
}
</style>
