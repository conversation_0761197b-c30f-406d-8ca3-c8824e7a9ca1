<template>
  <div class="product-list">
    <h1>策略列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.name"
            placeholder="策略名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.type"
            placeholder="策略类型"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd"
          >新增策略
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="序号" width="65" align="center" />
      <el-table-column prop="name" label="名称" align="center">
        <template slot-scope="scope">
          <div class="package-info">
            <!--            <el-avatar-->
            <!--                shape="square"-->
            <!--                size="medium"-->
            <!--                :src="row.imageUrl || defaultImage"-->
            <!--            ></el-avatar>-->
            <span class="ml-10">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120" align="center">
        <template slot-scope="scope">
          {{ $options.filters.typeFilter(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="scope" label="范围" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.scope === "product" ? "商品" : "订单" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="validity"
        label="有效期"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.start_time | formatDate }} 至
          {{ scope.row.end_time | formatDate }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)"
            >编辑</el-button
          >

          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>

          <el-button type="text" size="mini" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑策略弹窗 -->
    <PricingStrategyDetail
      :visible="dialogFormVisible"
      :editData="currentEditData"
      @close="handleDialogClose"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";
import PricingStrategyDetail from "./pricing-stategy-detail.vue";

export default {
  name: "PricingStrategyList",
  components: {
    PricingStrategyDetail,
  },
  filters: {
    typeFilter(type) {
      const typeMap = {
        discount: "折扣",
        full_reduction: "满减",
        member_price: "会员价",
        time_limited: "限时特价",
      };
      return typeMap[type] || "";
    },
    formatDate(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD");
    },
  },
  data() {
    return {
      searchForm: {
        name: "",
        type: "",
      },
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      dialogFormVisible: false,
      currentEditData: null,
      // 使用枚举值创建对应的选项
      typeOptions: [
        { label: "折扣", value: "discount" },
        { label: "满减", value: "full_reduction" },
        { label: "会员价", value: "member_price" },
        { label: "限时特价", value: "time_limited" },
        { label: "捆绑订购价", value: "bundle" },
      ],
    };
  },
  created() {
    this.getPricingStrategyList();
  },
  methods: {
    // 获取策略列表
    getPricingStrategyList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      if (this.searchForm.type) {
        params.type = this.searchForm.type;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getPricingStrategiesBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("API响应:", response.data.list);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取价格策略列表失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取价格策略列表失败", error);
          this.$message.error("获取价格策略列表失败");
        });
    },

    handleSearch() {
      this.pagination.page = 1;
      this.getPricingStrategyList();
    },

    resetSearch() {
      this.searchForm = {
        name: "",
        type: "",
      };
      this.getPricingStrategyList();
    },

    handleAdd() {
      this.currentEditData = null;
      this.dialogFormVisible = true;
    },

    handleEdit(row) {
      this.currentEditData = row;
      this.dialogFormVisible = true;
    },

    handleDelete(row) {
      this.$confirm("确认删除该价格策略?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "deletePricingStrategy",
            data: {
              id: row.id,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getPricingStrategyList(); // 刷新列表
              } else {
                this.$message.error(response.message || "删除价格策略失败");
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error("删除价格策略失败", error);
              this.$message.error("删除价格策略失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    toggleStatus(row) {
      // 切换状态: active <-> inactive
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${statusText}该价格策略?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "updatePricingStrategyStatus",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: `价格策略已${statusText}!`,
                });
                row.status = newStatus; // 更新本地状态
                this.getPricingStrategyList(); // 刷新列表
              } else {
                this.$message.error(
                  response.message || `${statusText}价格策略失败`
                );
              }
            })
            .catch((error) => {
              console.error(`${statusText}价格策略失败`, error);
              this.$message.error(`${statusText}价格策略失败`);
            })
            .finally(() => {
              this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    // 弹窗关闭事件
    handleDialogClose() {
      this.dialogFormVisible = false;
      this.currentEditData = null;
    },

    // 弹窗成功事件
    handleDialogSuccess() {
      this.getPricingStrategyList(); // 刷新列表
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getPricingStrategyList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getPricingStrategyList();
    },
  },
};
</script>

<style lang="less" scoped>
.product-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .package-info {
    display: flex;
    align-items: center;

    .ml-10 {
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}
</style>
