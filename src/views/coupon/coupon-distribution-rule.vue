<template>
  <div class="coupon-distribution-rule">
    <h1>发券规则管理</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.keyword"
            placeholder="规则名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.type"
            placeholder="规则类型"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.status"
            placeholder="规则状态"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch">
          查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch">
          重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd">
          新增规则
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="规则名称" min-width="150" />
      <el-table-column prop="description" label="规则描述" min-width="200" />
      <el-table-column prop="type" label="规则类型" width="150">
        <template slot-scope="scope">
          <el-tag :type="getTypeTagType(scope.row.type)">
            {{ getTypeLabel(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "生效" : "未生效" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="execution_time" label="执行时间" width="150" />
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.limit"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 行为函数配置弹窗 -->
    <el-dialog
      title="配置行为函数参数"
      :visible.sync="behaviorConfigVisible"
      width="600px"
      @close="handleBehaviorConfigClose"
    >
      <div v-if="currentBehaviorConfig">
        <h4>{{ getBehaviorTypeName(currentBehaviorType) }}</h4>

        <!-- registered 无需配置参数 -->
        <div v-if="currentBehaviorType === 'registered'" class="no-params">
          <el-alert
            title="用户注册行为无需配置参数"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <!-- viewed 和 shared 配置 content_ids -->
        <div
          v-if="
            currentBehaviorType === 'viewed' || currentBehaviorType === 'shared'
          "
        >
          <el-form label-width="100px">
            <el-form-item label="选择文章">
              <el-select
                v-model="currentBehaviorConfig.params.content_ids"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="搜索并选择文章"
                :remote-method="searchArticles"
                :loading="articleSearchLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="article in articleOptions"
                  :key="article.id"
                  :label="article.title"
                  :value="article.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- paid 配置 order_amount 和 order_items -->
        <div v-if="currentBehaviorType === 'paid'">
          <el-form label-width="100px">
            <el-form-item label="订单金额">
              <el-input-number
                v-model="currentBehaviorConfig.params.order_amount"
                :min="0"
                :precision="2"
                placeholder="请输入订单金额"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="订单项目">
              <div class="order-items-config">
                <div
                  v-for="(item, index) in currentBehaviorConfig.params
                    .order_items"
                  :key="index"
                  class="order-item-config"
                >
                  <el-select
                    v-model="item.product_id"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="搜索并选择产品"
                    :remote-method="searchProducts"
                    :loading="productSearchLoading"
                    style="width: 200px; margin-right: 10px"
                  >
                    <el-option
                      v-for="product in productOptions"
                      :key="product.id"
                      :label="product.name"
                      :value="product.id"
                    />
                  </el-select>
                  <el-input-number
                    v-model="item.quantity"
                    :min="1"
                    placeholder="数量"
                    style="width: 120px; margin-right: 10px"
                  />
                  <el-button
                    type="danger"
                    size="mini"
                    @click="removeOrderItem(index)"
                  >
                    删除
                  </el-button>
                </div>
                <el-button type="primary" size="mini" @click="addOrderItem">
                  添加订单项
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="behaviorConfigVisible = false">取消</el-button>
        <el-button type="primary" @click="saveBehaviorConfig">确定</el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="ruleRules"
        label-width="120px"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
        <el-form-item label="规则类型" prop="type">
          <el-select
            v-model="ruleForm.type"
            placeholder="请选择规则类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            ruleForm.type === 'user_feature' ||
            ruleForm.type === 'user_feature_and_behavior'
          "
          label="用户特征函数"
          prop="user_feature_func"
        >
          <el-input
            :value="JSON.stringify(ruleForm.user_feature_func, null, 2)"
            type="textarea"
            :rows="4"
            placeholder="用户特征函数（暂未实现）"
            readonly
          />
          <div class="form-tip">
            用户特征函数功能暂未实现，当前返回空对象 {}
          </div>
        </el-form-item>
        <el-form-item
          v-if="
            ruleForm.type === 'user_behavior' ||
            ruleForm.type === 'user_feature_and_behavior'
          "
          label="用户行为函数"
          prop="user_behavior_func"
        >
          <div class="behavior-functions">
            <div class="behavior-selector">
              <el-select
                v-model="selectedBehaviorType"
                placeholder="选择行为类型"
                style="width: 200px; margin-right: 10px"
                @change="handleBehaviorTypeChange"
              >
                <el-option
                  v-for="item in behaviorTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button
                type="primary"
                size="mini"
                @click="addBehaviorFunction"
                :disabled="!selectedBehaviorType"
              >
                添加
              </el-button>
            </div>

            <div class="behavior-list">
              <div
                v-for="(behavior, behaviorType) in ruleForm.user_behavior_func"
                :key="behaviorType"
                class="behavior-item"
              >
                <div class="behavior-info">
                  <el-tag type="primary" size="small">
                    {{ getBehaviorTypeName(behaviorType) }}
                  </el-tag>
                  <span class="behavior-params">
                    {{ formatBehaviorParams(behavior, behaviorType) }}
                  </span>
                </div>
                <div class="behavior-actions">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="editBehaviorFunction(behaviorType)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="removeBehaviorFunction(behaviorType)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="发放内容" prop="distribution_content">
          <div class="distribution-content">
            <div
              v-for="(item, index) in ruleForm.distribution_content"
              :key="index"
              class="content-item"
            >
              <el-select
                v-model="item.coupon_batch_id"
                placeholder="选择优惠券批次"
                style="width: 200px; margin-right: 10px"
              >
                <el-option
                  v-for="batch in couponBatches"
                  :key="batch.id"
                  :label="batch.name"
                  :value="batch.id"
                />
              </el-select>
              <el-input-number
                v-model="item.quantity"
                :min="1"
                placeholder="数量"
                style="width: 120px; margin-right: 10px"
              />
              <el-button
                type="danger"
                size="mini"
                @click="removeDistributionContent(index)"
              >
                删除
              </el-button>
            </div>
            <el-button
              type="primary"
              size="mini"
              @click="addDistributionContent"
            >
              添加发放内容
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="执行时间" prop="execution_time">
          <el-input
            v-model="ruleForm.execution_time"
            placeholder="请输入cron表达式，如：0 0 12 * * ?"
          />
        </el-form-item>
        <el-form-item label="规则状态" prop="status">
          <el-radio-group v-model="ruleForm.status">
            <el-radio :label="1">生效</el-radio>
            <el-radio :label="0">未生效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "CouponDistributionRule",
  data() {
    return {
      listLoading: false,
      tableData: [],
      selectedRows: [],
      searchForm: {
        keyword: "",
        type: "",
        status: "",
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
      },
      dialogVisible: false,
      dialogTitle: "新增规则",
      isEdit: false,
      editId: null,
      ruleForm: {
        name: "",
        description: "",
        type: "",
        user_feature_func: {},
        user_behavior_func: {},
        distribution_content: [],
        execution_time: "",
        status: 1,
      },
      ruleRules: {
        name: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择规则类型", trigger: "change" },
        ],
        distribution_content: [
          { required: true, message: "请添加发放内容", trigger: "change" },
        ],
      },
      typeOptions: [
        { value: "user_feature", label: "用户特征" },
        { value: "user_behavior", label: "用户行为" },
        { value: "user_feature_and_behavior", label: "用户特征与行为" },
      ],
      statusOptions: [
        { value: 1, label: "生效" },
        { value: 0, label: "未生效" },
      ],
      behaviorOptions: [
        { value: "created", label: "创建" },
        { value: "updated", label: "更新" },
        { value: "deleted", label: "删除" },
        { value: "login", label: "登录" },
        { value: "logout", label: "登出" },
        { value: "password_changed", label: "密码修改" },
        { value: "viewed", label: "浏览" },
        { value: "shared", label: "分享" },
        { value: "registered", label: "注册" },
        { value: "ordered", label: "下单" },
        { value: "paid", label: "支付" },
        { value: "used", label: "使用" },
        { value: "cancelled", label: "取消" },
        { value: "refunded", label: "退款" },
        { value: "verified", label: "验证" },
      ],
      couponBatches: [],
      // 行为函数配置相关
      selectedBehaviorType: "",
      behaviorConfigVisible: false,
      currentBehaviorConfig: null,
      currentBehaviorType: null,
      behaviorTypeOptions: [
        { value: "registered", label: "用户注册" },
        { value: "viewed", label: "用户浏览活动页面" },
        { value: "shared", label: "用户分享活动页面" },
        { value: "paid", label: "用户支付" },
      ],
      // 文章搜索相关
      articleOptions: [],
      articleSearchLoading: false,
      // 产品搜索相关
      productOptions: [],
      productSearchLoading: false,
    };
  },
  computed: {},
  mounted() {
    this.getList();
    this.getCouponBatches();
  },
  methods: {
    // 获取列表数据
    getList() {
      this.listLoading = true;
      const params = {
        skip: (this.pagination.page - 1) * this.pagination.limit,
        limit: this.pagination.limit,
      };

      // 只有当值不为空时才添加到参数中
      if (this.searchForm.keyword) {
        params.keyword = this.searchForm.keyword;
      }
      if (this.searchForm.type) {
        params.rule_type = this.searchForm.type;
      }
      if (this.searchForm.status) {
        params.rule_status = this.searchForm.status;
      }

      console.log("调用发券规则列表API，参数:", params);
      requestApi({
        name: "getDistributionRules",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("发券规则列表API响应:", response);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.pagination.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取发券规则列表失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取发券规则列表失败", error);
          this.$message.error("获取数据失败");
        });
    },

    // 获取优惠券批次列表
    getCouponBatches() {
      console.log("开始获取优惠券批次列表...");
      requestApi({
        name: "getCouponBatchesBySearch",
        data: {
          page: 1,
          pageSize: 1000,
        },
      })
        .then((response) => {
          console.log("优惠券批次API响应:", response);
          if (response && response.code === 200) {
            console.log("批次数据:", response.data);
            console.log("批次列表:", response.data.list);
            this.couponBatches = response.data.list || [];
            console.log("设置后的couponBatches:", this.couponBatches);

            // 检查数据结构
            if (this.couponBatches.length > 0) {
              console.log("第一个批次数据结构:", this.couponBatches[0]);
            }
          } else {
            console.error("API返回错误:", response);
            this.$message.error(response.message || "获取优惠券批次失败");
          }
        })
        .catch((error) => {
          console.error("获取优惠券批次失败:", error);
          this.$message.error("获取优惠券批次失败");
        });
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1;
      this.getList();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: "",
        type: "",
        status: "",
      };
      this.pagination.page = 1;
      this.getList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增规则";
      this.isEdit = false;
      this.editId = null;
      this.resetForm();

      // 确保有至少一个发放内容项
      if (this.ruleForm.distribution_content.length === 0) {
        this.addDistributionContent();
      }

      // 重新获取批次数据以确保数据是最新的
      this.getCouponBatches();

      this.dialogVisible = true;
    },

    // 编辑
    async handleEdit(row) {
      this.dialogTitle = "编辑规则";
      this.isEdit = true;
      this.editId = row.id;

      try {
        const response = await requestApi({
          name: "getDistributionRule",
          data: { id: row.id },
        });

        if (response.code === 200) {
          const data = response.data;
          this.ruleForm = {
            name: data.name,
            description: data.description || "",
            type: data.type,
            user_feature_func: data.user_feature_func || {},
            user_behavior_func: data.user_behavior_func || {},
            distribution_content: data.distribution_content || [],
            execution_time: data.execution_time || "",
            status:
              data.status === "active"
                ? 1
                : data.status === "inactive"
                ? 0
                : data.status,
          };
          this.dialogVisible = true;
        }
      } catch (error) {
        this.$message.error("获取规则详情失败");
        console.error(error);
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除这条规则吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await requestApi({
            name: "deleteDistributionRule",
            data: { id: row.id },
          });

          if (response.code === 200) {
            this.$message.success("删除成功");
            this.getList();
          }
        } catch (error) {
          this.$message.error("删除失败");
          console.error(error);
        }
      });
    },

    // 状态变更
    handleStatusChange(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确定要${statusText}这条规则吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await requestApi({
            name: "updateDistributionRuleStatus",
            data: { id: row.id, status: newStatus },
          });

          if (response.code === 200) {
            this.$message.success(`${statusText}成功`);
            this.getList();
          }
        } catch (error) {
          this.$message.error(`${statusText}失败`);
          console.error(error);
        }
      });
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.limit = size;
      this.pagination.page = 1;
      this.getList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.page = page;
      this.getList();
    },

    // 提交表单
    handleSubmit() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          try {
            const data = { ...this.ruleForm };

            // 调试信息：打印提交的数据格式
            console.log("提交的数据格式:", JSON.stringify(data, null, 2));

            // 验证必填字段
            if (
              data.type === "user_feature" ||
              data.type === "user_feature_and_behavior"
            ) {
              if (!data.user_feature_func) {
                this.$message.error("用户特征函数不能为空");
                return;
              }
            }

            if (
              data.type === "user_behavior" ||
              data.type === "user_feature_and_behavior"
            ) {
              if (
                !data.user_behavior_func ||
                Object.keys(data.user_behavior_func).length === 0
              ) {
                this.$message.error("用户行为函数不能为空");
                return;
              }
            }

            if (
              !data.distribution_content ||
              data.distribution_content.length === 0
            ) {
              this.$message.error("发放内容不能为空");
              return;
            }

            let response;
            if (this.isEdit) {
              response = await requestApi({
                name: "updateDistributionRule",
                data: { ...data, id: this.editId },
              });
            } else {
              response = await requestApi({
                name: "addDistributionRule",
                data,
              });
            }

            if (response.code === 200) {
              this.$message.success(this.isEdit ? "更新成功" : "创建成功");
              this.dialogVisible = false;
              this.getList();
            }
          } catch (error) {
            this.$message.error(this.isEdit ? "更新失败" : "创建失败");
            console.error(error);
          }
        }
      });
    },

    // 对话框关闭
    handleDialogClose() {
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.ruleForm = {
        name: "",
        description: "",
        type: "",
        user_feature_func: {},
        user_behavior_func: {},
        distribution_content: [],
        execution_time: "",
        status: 1,
      };
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate();
      }
    },

    // 行为函数相关方法
    handleBehaviorTypeChange(value) {
      this.selectedBehaviorType = value;
    },

    addBehaviorFunction() {
      if (!this.selectedBehaviorType) return;

      // 检查是否已经存在相同类型的行为函数
      const exists = this.ruleForm.user_behavior_func.hasOwnProperty(
        this.selectedBehaviorType
      );
      if (exists) {
        this.$message.warning("该行为类型已存在，请选择其他类型");
        return;
      }

      // 创建新的行为函数配置
      const newBehavior = this.createBehaviorFunction(
        this.selectedBehaviorType
      );
      this.$set(
        this.ruleForm.user_behavior_func,
        this.selectedBehaviorType,
        newBehavior
      );
      this.selectedBehaviorType = "";
    },

    createBehaviorFunction(type) {
      const behaviorMap = {
        registered: {
          name: "用户注册",
          func_name: "handle_registered",
          params: {},
        },
        viewed: {
          name: "用户浏览活动页面",
          func_name: "handle_viewed",
          params: {
            content_ids: [],
          },
        },
        shared: {
          name: "用户分享活动页面",
          func_name: "handle_shared",
          params: {
            content_ids: [],
          },
        },
        paid: {
          name: "用户支付",
          func_name: "handle_paid",
          params: {
            order_amount: 0,
            order_items: [
              {
                product_id: null,
                quantity: 1,
              },
            ],
          },
        },
      };

      return JSON.parse(JSON.stringify(behaviorMap[type]));
    },

    editBehaviorFunction(behaviorType) {
      this.currentBehaviorType = behaviorType;
      this.currentBehaviorConfig = JSON.parse(
        JSON.stringify(this.ruleForm.user_behavior_func[behaviorType])
      );
      this.behaviorConfigVisible = true;
    },

    removeBehaviorFunction(behaviorType) {
      this.$confirm("确定要删除这个行为函数吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$delete(this.ruleForm.user_behavior_func, behaviorType);
        this.$message.success("删除成功");
      });
    },

    getBehaviorTypeName(type) {
      const typeMap = {
        registered: "用户注册",
        viewed: "用户浏览活动页面",
        shared: "用户分享活动页面",
        paid: "用户支付",
      };
      return typeMap[type] || type;
    },

    formatBehaviorParams(behavior, behaviorType) {
      if (behaviorType === "registered") {
        return "无需参数";
      } else if (behaviorType === "viewed" || behaviorType === "shared") {
        const count = behavior.params.content_ids
          ? behavior.params.content_ids.length
          : 0;
        return `已选择 ${count} 篇文章`;
      } else if (behaviorType === "paid") {
        const amount = behavior.params.order_amount || 0;
        const itemsCount = behavior.params.order_items
          ? behavior.params.order_items.length
          : 0;
        return `金额: ¥${amount}, 项目: ${itemsCount} 个`;
      }
      return "";
    },

    // 行为函数配置弹窗相关方法
    handleBehaviorConfigClose() {
      this.currentBehaviorConfig = null;
      this.currentBehaviorType = null;
      this.articleOptions = [];
      this.productOptions = [];
    },

    saveBehaviorConfig() {
      if (this.currentBehaviorType) {
        this.$set(
          this.ruleForm.user_behavior_func,
          this.currentBehaviorType,
          this.currentBehaviorConfig
        );
      }
      this.behaviorConfigVisible = false;
      this.$message.success("配置保存成功");
    },

    // 文章搜索
    searchArticles(query) {
      if (!query) {
        this.articleOptions = [];
        return;
      }

      this.articleSearchLoading = true;
      requestApi({
        name: "getArticleListBySearch",
        data: { keyword: query, page: 1, pageSize: 20 },
      })
        .then((response) => {
          this.articleSearchLoading = false;
          if (response && response.code === 200) {
            this.articleOptions = response.data.list || response.data || [];
          } else {
            this.articleOptions = [];
          }
        })
        .catch((error) => {
          this.articleSearchLoading = false;
          console.error("搜索文章失败", error);
          this.articleOptions = [];
        });
    },

    // 产品搜索
    searchProducts(query) {
      if (!query) {
        this.productOptions = [];
        return;
      }

      this.productSearchLoading = true;
      requestApi({
        name: "getProductsBySearch",
        data: { keyword: query, page: 1, pageSize: 20 },
      })
        .then((response) => {
          this.productSearchLoading = false;
          if (response && response.code === 200) {
            this.productOptions = response.data.list || response.data || [];
          } else {
            this.productOptions = [];
          }
        })
        .catch((error) => {
          this.productSearchLoading = false;
          console.error("搜索产品失败", error);
          this.productOptions = [];
        });
    },

    // 订单项管理
    addOrderItem() {
      if (!this.currentBehaviorConfig.params.order_items) {
        this.$set(this.currentBehaviorConfig.params, "order_items", []);
      }
      this.currentBehaviorConfig.params.order_items.push({
        product_id: null,
        quantity: 1,
      });
    },

    removeOrderItem(index) {
      this.currentBehaviorConfig.params.order_items.splice(index, 1);
    },

    // 添加发放内容
    addDistributionContent() {
      this.ruleForm.distribution_content.push({
        coupon_batch_id: "",
        quantity: 1,
      });
    },

    // 删除发放内容
    removeDistributionContent(index) {
      this.ruleForm.distribution_content.splice(index, 1);
    },

    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        user_feature: "primary",
        user_behavior: "success",
        user_feature_and_behavior: "warning",
      };
      return typeMap[type] || "info";
    },

    // 获取类型标签文本
    getTypeLabel(type) {
      const typeMap = {
        user_feature: "用户特征",
        user_behavior: "用户行为",
        user_feature_and_behavior: "用户特征与行为",
      };
      return typeMap[type] || type;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return "";
      const date = new Date(dateTime);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.coupon-distribution-rule {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .distribution-content {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
  }

  .content-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .dialog-footer {
    text-align: right;
  }

  .el-tag {
    margin-right: 5px;
  }

  // 行为函数配置样式
  .behavior-functions {
    .behavior-selector {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .behavior-list {
      .behavior-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        margin-bottom: 10px;
        background-color: #fafafa;

        &:last-child {
          margin-bottom: 0;
        }

        .behavior-info {
          display: flex;
          align-items: center;
          flex: 1;

          .behavior-params {
            margin-left: 10px;
            color: #606266;
            font-size: 12px;
          }
        }

        .behavior-actions {
          display: flex;
          gap: 5px;
        }
      }
    }
  }

  .no-params {
    margin: 20px 0;
  }

  .order-items-config {
    .order-item-config {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar {
    .search-container {
      flex-wrap: wrap;
      justify-content: center;
    }

    .input-group .el-input {
      width: 150px;
    }
  }

  .content-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
