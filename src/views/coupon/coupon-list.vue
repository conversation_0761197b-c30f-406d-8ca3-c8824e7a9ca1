<template>
  <div class="product-list">
    <h1>优惠券列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.name"
            placeholder="优惠券名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.type"
            placeholder="优惠券类型"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd"
          >新增优惠券
        </el-button>
        <el-button type="success" size="mini" @click="handleBatchManagement"
          >批次管理
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="序号" width="65" align="center" />
      <el-table-column prop="name" label="名称" align="left" min-width="200">
        <template slot-scope="scope">
          <div class="coupon-info">
            <div class="coupon-name">{{ scope.row.name }}</div>
            <div class="coupon-description" v-if="scope.row.description">
              {{ scope.row.description }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120" align="center">
        <template slot-scope="scope">
          {{ $options.filters.typeFilter(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="scope" label="范围" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.scope === "product" ? "商品" : "订单" }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)"
            >编辑</el-button
          >

          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>

          <el-button type="text" size="mini" @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button type="text" size="mini" @click="handleViewBatches(scope.row)"
            >查看批次</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑优惠券弹窗 -->
    <coupon-detail
      :visible.sync="dialogFormVisible"
      :title="dialogTitle"
      :detail="formData"
      @submit="submitForm"
      @cancel="dialogFormVisible = false"
    />
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";
import CouponDetail from "./coupon-detail.vue";

export default {
  name: "CouponList",
  components: {
    CouponDetail,
  },
  filters: {
    typeFilter(type) {
      const typeMap = {
        discount: "折扣券",
        full_reduction: "满减券",
        cash: "现金券"
      };
      return typeMap[type] || "";
    },
    formatDate(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD");
    },
  },
  data() {
    return {
      searchForm: {
        name: "",
        type: "",
      },
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      dialogFormVisible: false,
      dialogTitle: "",
      formData: {},
      typeOptions: [
        { label: "现金券", value: "cash" },
        { label: "折扣券", value: "discount" },
        { label: "满减券", value: "full_reduction" },
      ],
    };
  },
  created() {
    this.getCouponList();
  },
  methods: {
    // 获取优惠券列表
    getCouponList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      if (this.searchForm.type) {
        params.type = this.searchForm.type;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getCouponsBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("API响应:", response.data.list);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取优惠券列表失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取优惠券列表失败", error);
          this.$message.error("获取优惠券列表失败");
        });
    },

    handleSearch() {
      this.pagination.page = 1;
      this.getCouponList();
    },

    resetSearch() {
      this.searchForm = {
        name: "",
        type: "",
      };
      this.getCouponList();
    },

    handleAdd() {
      this.dialogTitle = "新增优惠券";
      this.formData = {};
      this.dialogFormVisible = true;
    },

    handleBatchManagement() {
      // 跳转到批次管理页面
      this.$router.push("/coupon/batch-list");
    },

    handleViewBatches(row) {
      // 跳转到批次管理页面，并筛选该优惠券的批次
      this.$router.push({
        path: "/coupon/batch-list",
        query: { coupon_id: row.id, coupon_name: row.name }
      });
    },

    handleEdit(row) {
      this.dialogTitle = "编辑优惠券";
      this.listLoading = true;

      requestApi({
        name: "getCoupon",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          this.listLoading = false;
          if (response && response.code === 200) {
            this.formData = response.data;
            this.dialogFormVisible = true;
          } else {
            this.$message.error(response.message || "获取优惠券详情失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取优惠券详情失败", error);
          this.$message.error("获取优惠券详情失败");
        });
    },

    handleDelete(row) {
      this.$confirm("确认删除格优惠券?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "deleteCoupon",
            data: {
              id: row.id,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getCouponList(); // 刷新列表
              } else {
                this.$message.error(response.message || "删除优惠券失败");
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error("删除优惠券失败", error);
              this.$message.error("删除优惠券失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    toggleStatus(row) {
      // 切换状态: active <-> inactive
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确认${statusText}该优惠券?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "updateCouponStatus",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: `优惠券已${statusText}!`,
                });
                row.status = newStatus; // 更新本地状态
                this.getCouponList(); // 刷新列表
              } else {
                this.$message.error(
                  response.message || `${statusText}优惠券失败`
                );
              }
            })
            .catch((error) => {
              console.error(`${statusText}优惠券失败`, error);
              this.$message.error(`${statusText}优惠券失败`);
            })
            .finally(() => {
              this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    submitForm(formData) {
      this.listLoading = true;

      let couponData = {
        name: formData.name,
        type: formData.type,
        description: formData.description,
        start_time: formData.start_time,
        end_time: formData.end_time,
        status: formData.status,

        // 条件相关字段
        condition_scope: formData.condition_scope || "order",
        condition_objects: formData.condition_objects || [],
        condition_amount: formData.condition_amount || 0,
        usage_quantity: formData.usage_quantity || 0,
        usage_cycle: formData.usage_cycle || "per_order",
        usage_limit: formData.usage_limit || 0,
        mutual_exclusive_rules: formData.mutual_exclusive_rules || [],
        payment_channels: formData.payment_channels || [],

        // 作用相关字段
        apply_scope: formData.apply_scope || "order",
        apply_objects: formData.apply_objects || [],
        apply_order: formData.apply_order || 0,
      };

      // 根据策略类型，添加特定字段
      switch (formData.type) {
        case "discount":
          couponData.discount_rate = formData.discount_rate;
          couponData.min_amount = formData.min_amount;
          couponData.max_discount = formData.max_discount;
          break;
        case "full_reduction":
          couponData.full_amount = formData.full_amount;
          couponData.reduction_amount = formData.reduction_amount;
          break;
        case "cash":
          couponData.amount = formData.amount;
          break;
      }
      console.log("提交的数据:", couponData);

      if (formData.id !== undefined) {
        couponData.id = formData.id;
        // 编辑优惠券
        requestApi({
          name: "updateCoupon",
          data: couponData,
        })
          .then((response) => {
            this.listLoading = false;
            if (response && response.code === 200) {
              this.$message({
                type: "success",
                message: "优惠券已更新!",
              });
              this.dialogFormVisible = false;
              this.getCouponList(); // 刷新列表
            } else {
              this.$message.error(response.message || "更新优惠券失败");
            }
          })
          .catch((error) => {
            this.listLoading = false;
            console.error("更新优惠券失败", error);
            this.$message.error("更新优惠券失败");
          });
      } else {
        // 新增优惠券
        requestApi({
          name: "addCoupon",
          data: couponData,
        })
          .then((response) => {
            this.listLoading = false;
            if (response && response.code === 200) {
              this.$message({
                type: "success",
                message: "新增优惠券成功!",
              });
              this.dialogFormVisible = false;
              this.getCouponList(); // 刷新列表
            } else {
              this.$message.error(response.message || "新增优惠券失败");
            }
          })
          .catch((error) => {
            this.listLoading = false;
            console.error("新增优惠券失败", error);
            this.$message.error("新增优惠券失败");
          });
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getCouponList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getCouponList();
    },
  },
};
</script>

<style lang="less" scoped>
.product-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .package-info {
    display: flex;
    align-items: center;

    .ml-10 {
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .avatar-uploader {
    display: block;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;

    &:hover {
      border-color: #409eff;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }
  }

  .form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.coupon-info {
  text-align: left;
}

.coupon-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.coupon-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}
</style>
