<template>
  <div class="test-page">
    <h1>批次管理测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置是正确的。</p>
    
    <el-button type="primary" @click="testApi">测试API连接</el-button>
    
    <div v-if="apiResult" style="margin-top: 20px;">
      <h3>API测试结果：</h3>
      <pre>{{ apiResult }}</pre>
    </div>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "CouponBatchTest",
  data() {
    return {
      apiResult: null
    };
  },
  methods: {
    testApi() {
      // 测试获取优惠券列表API
      requestApi({
        name: "getCouponsBySearch",
        data: {
          page: 1,
          pageSize: 5
        }
      })
        .then((response) => {
          this.apiResult = JSON.stringify(response, null, 2);
        })
        .catch((error) => {
          this.apiResult = "API调用失败: " + error.message;
        });
    }
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
