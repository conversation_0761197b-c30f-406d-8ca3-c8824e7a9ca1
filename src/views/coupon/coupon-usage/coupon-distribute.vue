<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="选择优惠券" prop="coupon_id">
        <el-select
          v-model="formData.coupon_id"
          placeholder="请选择优惠券"
          filterable
          remote
          reserve-keyword
          :remote-method="searchCoupons"
          :loading="couponLoading"
          style="width: 100%"
          clearable
          @change="onCouponChange"
        >
          <el-option
            v-for="item in couponOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择批次" prop="coupon_batch_id">
        <el-select
          v-model="formData.coupon_batch_id"
          placeholder="请选择批次"
          style="width: 100%"
          clearable
          :disabled="!formData.coupon_id"
        >
          <el-option
            v-for="item in batchOptions"
            :key="item.id"
            :label="`${item.name} (剩余: ${item.quantity})`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择用户" prop="user_ids">
        <el-select
          v-model="formData.user_ids"
          placeholder="请选择用户"
          filterable
          remote
          reserve-keyword
          :remote-method="searchUsers"
          :loading="userLoading"
          multiple
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in userOptions"
            :key="item.id"
            :label="`${item.username} ${item.real_name}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        发放
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "CouponDistribute",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "发放优惠券",
    },
  },
  data() {
    return {
      dialogVisible: false,
      formData: {
        coupon_id: null,
        coupon_batch_id: null,
        user_ids: [],
      },
      couponOptions: [],
      batchOptions: [],
      userOptions: [],
      couponLoading: false,
      batchLoading: false,
      userLoading: false,
      submitLoading: false,
      rules: {
        coupon_id: [
          { required: true, message: "请选择优惠券", trigger: "change" },
        ],
        coupon_batch_id: [
          { required: true, message: "请选择批次", trigger: "change" },
        ],
        user_ids: [
          { required: true, message: "请选择用户", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.resetForm();
      }
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
  },
  methods: {
    // 搜索优惠券
    searchCoupons(query) {
      if (query !== "") {
        this.couponLoading = true;
        requestApi({
          name: "getCouponsByName",
          data: { name: query },
        })
          .then((response) => {
            this.couponLoading = false;
            if (response && response.code === 200) {
              this.couponOptions = response.data || [];
            } else {
              this.$message.error(response.message || "获取优惠券列表失败");
            }
          })
          .catch((error) => {
            this.couponLoading = false;
            console.error("获取优惠券列表失败", error);
            this.$message.error("获取优惠券列表失败");
          });
      } else {
        this.couponOptions = [];
      }
    },

    // 当优惠券选择改变时，加载对应的批次
    onCouponChange(couponId) {
      this.formData.coupon_batch_id = null;
      this.batchOptions = [];

      if (couponId) {
        this.batchLoading = true;
        requestApi({
          name: "getCouponBatchesByCouponId",
          data: { coupon_id: couponId },
        })
          .then((response) => {
            this.batchLoading = false;
            if (response && response.code === 200) {
              this.batchOptions = response.data.list || [];
            } else {
              this.$message.error(response.message || "获取批次列表失败");
            }
          })
          .catch((error) => {
            this.batchLoading = false;
            console.error("获取批次列表失败", error);
            this.$message.error("获取批次列表失败");
          });
      }
    },

    // 搜索用户
    searchUsers(query) {
      if (query !== "") {
        this.userLoading = true;
        requestApi({
          name: "getPersonalListByName",
          data: { name: query },
        })
          .then((response) => {
            this.userLoading = false;
            if (response && response.code === 200) {
              this.userOptions = response.data || [];
            } else {
              this.$message.error(response.message || "获取用户列表失败");
            }
          })
          .catch((error) => {
            this.userLoading = false;
            console.error("获取用户列表失败", error);
            this.$message.error("获取用户列表失败");
          });
      } else {
        this.userOptions = [];
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true;

          // 构造请求数据
          const records = this.formData.user_ids.map((user_id) => ({
            coupon_id: this.formData.coupon_id,
            coupon_batch_id: this.formData.coupon_batch_id,
            user_id: user_id,
          }));

          const requestData = {
            records: records,
          };

          console.log("发放优惠券请求数据:", requestData);

          requestApi({
            name: "distributeCoupons",
            data: requestData,
          })
            .then((response) => {
              this.submitLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "优惠券发放成功!",
                });
                this.$emit("submit", requestData);
                this.handleClose();
              } else {
                this.$message.error(response.message || "优惠券发放失败");
              }
            })
            .catch((error) => {
              this.submitLoading = false;
              console.error("优惠券发放失败", error);
              this.$message.error("优惠券发放失败");
            });
        }
      });
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.$emit("cancel");
    },

    // 重置表单
    resetForm() {
      this.formData = {
        coupon_id: null,
        coupon_batch_id: null,
        user_ids: [],
      };
      this.couponOptions = [];
      this.batchOptions = [];
      this.userOptions = [];
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dialog-footer {
  text-align: right;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
