<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1000px"
      @close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="140px"
      >
        <!-- 基本信息 -->
        <h3 class="section-title">基本信息</h3>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批次名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入批次名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次号" prop="batch_number">
              <el-input-number
                v-model="formData.batch_number"
                :min="1"
                :step="1"
                style="width: 100%"
                placeholder="请输入批次号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关联优惠券" prop="coupon_id">
          <el-select
            v-model="formData.coupon_id"
            placeholder="请选择优惠券"
            filterable
            remote
            reserve-keyword
            :remote-method="searchCoupons"
            :loading="couponLoading"
            style="width: 100%"
            clearable
            @focus="loadCoupons"
          >
            <el-option
              v-for="item in couponOptions"
              :key="item.id"
              :label="`${item.name} (${item.type})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="批次描述" prop="description">
          <el-input
            type="textarea"
            v-model="formData.description"
            rows="3"
            placeholder="请输入批次描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批次数量" prop="quantity">
              <el-input-number
                v-model="formData.quantity"
                :min="1"
                :step="1"
                style="width: 100%"
                placeholder="请输入批次数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效时长(小时)" prop="valid_duration">
              <el-input-number
                v-model="formData.valid_duration"
                :min="1"
                :step="1"
                style="width: 100%"
                placeholder="请输入有效时长"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效时间" prop="start_time">
              <el-date-picker
                v-model="formData.start_time"
                type="datetime"
                placeholder="选择生效时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="end_time">
              <el-date-picker
                v-model="formData.end_time"
                type="datetime"
                placeholder="选择结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="批次状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-divider />
        <h3 class="section-title">发放设置</h3>

        <el-form-item label="发放渠道" prop="distribution_channels">
          <el-checkbox-group v-model="formData.distribution_channels">
            <el-checkbox
              v-for="item in distributionChannelOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="周期发放数量" prop="distribution_quantity">
              <el-input-number
                v-model="formData.distribution_quantity"
                :min="0"
                :step="1"
                style="width: 100%"
                placeholder="0表示无限制"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发放周期" prop="distribution_cycle">
              <el-select
                v-model="formData.distribution_cycle"
                placeholder="请选择发放周期"
                style="width: 100%"
              >
                <el-option
                  v-for="item in usageCycleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider />
        <h3 class="section-title">领取设置</h3>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="领取开始时间" prop="receive_start_time">
              <el-date-picker
                v-model="formData.receive_start_time"
                type="datetime"
                placeholder="选择领取开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领取结束时间" prop="receive_end_time">
              <el-date-picker
                v-model="formData.receive_end_time"
                type="datetime"
                placeholder="选择领取结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="周期可领取数量" prop="receive_quantity">
              <el-input-number
                v-model="formData.receive_quantity"
                :min="0"
                :step="1"
                style="width: 100%"
                placeholder="0表示无限制"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领取周期" prop="receive_cycle">
              <el-select
                v-model="formData.receive_cycle"
                placeholder="请选择领取周期"
                style="width: 100%"
              >
                <el-option
                  v-for="item in usageCycleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ formData.id ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";

export default {
  name: "CouponBatchDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      formData: {
        id: undefined,
        name: "",
        description: "",
        batch_number: 1,
        quantity: 100,
        start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
        end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
        valid_duration: 24,
        coupon_id: null,
        status: 1,
        distribution_channels: [],
        distribution_quantity: 0,
        distribution_cycle: "per_day",
        receive_quantity: 1,
        receive_cycle: "per_day",
        receive_start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
        receive_end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
      },
      couponOptions: [],
      couponLoading: false,
      distributionChannelOptions: [
        { label: "新人注册", value: "new_user" },
        { label: "浏览活动", value: "view_activity" },
        { label: "分享活动", value: "share_activity" },
        { label: "销售购买", value: "purchase" },
      ],
      usageCycleOptions: [
        { label: "每次订单", value: "per_order" },
        { label: "每天", value: "per_day" },
        { label: "每周", value: "per_week" },
        { label: "每月", value: "per_month" },
        { label: "每年", value: "per_year" },
      ],
      statusOptions: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 },
      ],
      rules: {
        name: [
          { required: true, message: "请输入批次名称", trigger: "blur" },
        ],
        coupon_id: [
          { required: true, message: "请选择关联优惠券", trigger: "change" },
        ],
        batch_number: [
          { required: true, message: "请输入批次号", trigger: "blur" },
        ],
        quantity: [
          { required: true, message: "请输入批次数量", trigger: "blur" },
          { type: "number", min: 1, message: "批次数量必须大于0", trigger: "blur" },
        ],
        start_time: [
          { required: true, message: "请选择生效时间", trigger: "change" },
        ],
        end_time: [
          { required: true, message: "请选择结束时间", trigger: "change" },
        ],
        valid_duration: [
          { required: true, message: "请输入有效时长", trigger: "blur" },
          { type: "number", min: 1, message: "有效时长必须大于0", trigger: "blur" },
        ],
        receive_start_time: [
          { required: true, message: "请选择领取开始时间", trigger: "change" },
        ],
        receive_end_time: [
          { required: true, message: "请选择领取结束时间", trigger: "change" },
        ],
        distribution_quantity: [
          { type: "number", min: 0, message: "发放数量不能小于0", trigger: "blur" },
        ],
        receive_quantity: [
          { type: "number", min: 0, message: "领取数量不能小于0", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    title() {
      return this.formData.id ? "编辑优惠券批次" : "新增优惠券批次";
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      // 只在对话框打开且没有detail数据时初始化
      if (val && (!this.detail || Object.keys(this.detail).length === 0)) {
        this.initFormData();
      }
    },
    detail: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initFormData();
        }
      },
      immediate: true,
    },
  },
  methods: {
    initFormData() {
      if (this.detail && Object.keys(this.detail).length > 0) {
        // 使用detail的数据，但保留默认值作为fallback
        this.formData = {
          id: this.detail.id,
          name: this.detail.name || "",
          description: this.detail.description || "",
          batch_number: Number(this.detail.batch_number) || 1,
          quantity: Number(this.detail.quantity) || 100,
          start_time: this.detail.start_time || moment().format("YYYY-MM-DD HH:mm:ss"),
          end_time: this.detail.end_time || moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
          valid_duration: Number(this.detail.valid_duration) || 24,
          coupon_id: this.detail.coupon_id || null,
          status: this.detail.status !== undefined ? this.detail.status : 1,

          // 发放相关字段
          distribution_channels: Array.isArray(this.detail.distribution_channels) ? this.detail.distribution_channels : [],
          distribution_quantity: Number(this.detail.distribution_quantity) || 0,
          distribution_cycle: this.detail.distribution_cycle || "per_day",

          // 领取相关字段
          receive_quantity: Number(this.detail.receive_quantity) || 1,
          receive_cycle: this.detail.receive_cycle || "per_day",
          receive_start_time: this.detail.receive_start_time || moment().format("YYYY-MM-DD HH:mm:ss"),
          receive_end_time: this.detail.receive_end_time || moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
        };

        // 如果有关联的优惠券，加载到选项中
        if (this.detail.coupon_id && this.detail.coupon) {
          this.couponOptions = [this.detail.coupon];
        }
      } else {
        this.formData = {
          id: undefined,
          name: "",
          description: "",
          batch_number: 1,
          quantity: 100,
          start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
          end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
          valid_duration: 24,
          coupon_id: null,
          status: 1,
          distribution_channels: [],
          distribution_quantity: 0,
          distribution_cycle: "per_day",
          receive_quantity: 1,
          receive_cycle: "per_day",
          receive_start_time: moment().format("YYYY-MM-DD HH:mm:ss"),
          receive_end_time: moment().add(1, "year").format("YYYY-MM-DD HH:mm:ss"),
        };
        // 清空优惠券选项
        this.couponOptions = [];
      }
    },

    // 加载优惠券列表
    loadCoupons() {
      if (this.couponOptions.length === 0) {
        this.couponLoading = true;
        requestApi({
          name: "getCouponsBySearch",
          data: {
            page: 1,
            pageSize: 50,
          },
        })
          .then((response) => {
            this.couponLoading = false;
            if (response && response.code === 200) {
              this.couponOptions = response.data.list || [];
            }
          })
          .catch((error) => {
            this.couponLoading = false;
            console.error("获取优惠券列表失败", error);
          });
      }
    },

    // 搜索优惠券
    searchCoupons(query) {
      if (query !== "") {
        this.couponLoading = true;
        requestApi({
          name: "getCouponsByName",
          data: { name: query },
        })
          .then((response) => {
            this.couponLoading = false;
            if (response && response.code === 200) {
              this.couponOptions = response.data || [];
            }
          })
          .catch((error) => {
            this.couponLoading = false;
            console.error("获取优惠券列表失败", error);
          });
      } else {
        this.couponOptions = [];
      }
    },

    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 构造提交数据
          const submitData = {
            ...this.formData,
          };

          // 判断是新增还是编辑
          const isEdit = !!this.formData.id;
          const apiName = isEdit ? "updateCouponBatch" : "addCouponBatch";
          const successMessage = isEdit ? "更新批次成功!" : "创建批次成功!";
          const errorMessage = isEdit ? "更新批次失败" : "创建批次失败";

          requestApi({
            name: apiName,
            data: submitData,
          })
            .then((response) => {
              this.loading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: successMessage,
                });
                this.handleClose();
                this.$emit("success");
              } else {
                this.$message.error(response.message || errorMessage);
              }
            })
            .catch((error) => {
              this.loading = false;
              console.error(errorMessage, error);
              this.$message.error(errorMessage + ": " + (error.message || "未知错误"));
            });
        }
      });
    },

    handleCancel() {
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },

    handleClose() {
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped>
.section-title {
  margin: 20px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-divider {
  margin: 25px 0;
}
</style>

<style lang="less" scoped>
.section-title {
  margin: 10px 0;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
