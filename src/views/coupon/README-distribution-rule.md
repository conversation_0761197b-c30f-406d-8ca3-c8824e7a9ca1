# 发券规则管理功能说明

## 功能概述

发券规则管理功能用于管理优惠券的自动发放规则，支持基于用户特征、用户行为或两者结合的条件来自动发放优惠券。

## 页面路径

- 主管理页面：`/coupon/distribution-rule`
- 测试页面：`/coupon/distribution-rule-test`

## 功能特性

### 1. 规则类型

- **用户特征**：基于用户特征函数判断是否发放优惠券
- **用户行为**：基于用户行为类型触发优惠券发放
- **用户特征与行为**：同时满足用户特征和行为条件才发放优惠券

### 2. 用户行为类型

支持以下用户行为类型：
- `created` - 创建
- `updated` - 更新
- `deleted` - 删除
- `login` - 登录
- `logout` - 登出
- `password_changed` - 密码修改
- `viewed` - 浏览
- `shared` - 分享
- `registered` - 注册
- `ordered` - 下单
- `paid` - 支付
- `used` - 使用
- `cancelled` - 取消
- `refunded` - 退款
- `verified` - 验证

### 3. 发放内容配置

每个规则可以配置多个发放内容，每个发放内容包括：
- 优惠券批次ID
- 发放数量

### 4. 执行时间

支持使用cron表达式来定义规则的执行时间，例如：
- `0 0 12 * * ?` - 每天中午12点执行
- `0 0 0 * * ?` - 每天午夜执行
- `0 0 9 * * MON` - 每周一上午9点执行

### 5. 规则状态

- `active` - 生效
- `inactive` - 未生效

## 页面功能

### 列表页面功能

1. **搜索过滤**
   - 按规则名称搜索
   - 按规则类型过滤
   - 按规则状态过滤

2. **表格操作**
   - 查看规则详情
   - 编辑规则
   - 启用/禁用规则
   - 删除规则

3. **批量操作**
   - 支持多选
   - 批量状态变更

### 新增/编辑功能

1. **基本信息**
   - 规则名称（必填）
   - 规则描述
   - 规则类型（必填）

2. **条件配置**
   - 用户特征函数（当类型包含用户特征时必填）
   - 用户行为类型（当类型包含用户行为时必填）

3. **发放配置**
   - 发放内容（必填）
   - 执行时间（cron表达式）
   - 规则状态

## API接口

### 后端接口

所有接口都在 `/coupon/distribution-rules/` 路径下：

- `GET /` - 获取规则列表
- `POST /` - 创建新规则
- `GET /{id}` - 获取规则详情
- `PUT /{id}` - 更新规则
- `DELETE /{id}` - 删除规则
- `PUT /{id}/status` - 更新规则状态

### 前端API配置

在 `src/api/coupon.js` 中定义了相关的API接口配置。

## 数据模型

### 请求数据结构

```javascript
{
  name: "规则名称",
  description: "规则描述",
  type: "user_behavior", // user_feature | user_behavior | user_feature_and_behavior
  user_feature_function: "用户特征函数代码",
  user_behavior_types: ["registered", "login"],
  distribution_content: [
    {
      coupon_batch_id: 1,
      quantity: 2
    }
  ],
  execution_time: "0 0 12 * * ?",
  status: "active" // active | inactive
}
```

### 响应数据结构

```javascript
{
  code: 200,
  message: "success",
  data: {
    id: 1,
    name: "规则名称",
    description: "规则描述",
    type: "user_behavior",
    user_feature_function: null,
    user_behavior_types: ["registered"],
    distribution_content: [
      {
        coupon_batch_id: 1,
        quantity: 2
      }
    ],
    execution_time: "0 0 12 * * ?",
    status: "active",
    created_at: "2023-12-01T10:00:00",
    updated_at: "2023-12-01T10:00:00"
  }
}
```

## 使用说明

1. **访问页面**：在优惠券管理菜单下找到"发券规则管理"
2. **创建规则**：点击"新增规则"按钮，填写规则信息
3. **配置条件**：根据规则类型配置相应的条件
4. **设置发放内容**：选择要发放的优惠券批次和数量
5. **启用规则**：保存后启用规则使其生效

## 注意事项

1. 用户特征函数需要是有效的代码，用于判断用户是否符合条件
2. cron表达式需要符合标准格式
3. 发放内容中的优惠券批次必须存在且有效
4. 规则只有在"生效"状态下才会执行
5. 删除规则前请确认没有正在执行的任务

## 测试功能

提供了测试页面用于验证API接口和功能是否正常工作，包括：
- 测试获取规则列表
- 测试创建规则
- 测试获取优惠券批次
- 查看枚举值配置

访问测试页面：`/coupon/distribution-rule-test`
