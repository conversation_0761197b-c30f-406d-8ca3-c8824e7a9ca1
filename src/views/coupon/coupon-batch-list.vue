<template>
  <div class="coupon-batch-list">
    <h1>优惠券批次管理</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.keyword"
            placeholder="批次名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <div class="input-group">
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            size="mini"
            clearable
            class="filter-item"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch">
          查询
        </el-button>
        <el-button type="primary" size="mini" @click="resetSearch">
          重置
        </el-button>
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="handleAdd">
          新增批次
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column prop="id" label="批次ID" width="80" align="center" />
      <el-table-column prop="name" label="批次名称" align="center" min-width="150" />
      <el-table-column prop="quantity" label="数量" width="80" align="center" />
      <el-table-column label="有效期" width="180" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.start_time && scope.row.end_time">
            {{ scope.row.start_time | formatDate }} 至<br/>
            {{ scope.row.end_time | formatDate }}
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="发放渠道" width="150" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.distribution_channels && scope.row.distribution_channels.length > 0">
            <el-tag
              v-for="channel in scope.row.distribution_channels"
              :key="channel"
              size="mini"
              style="margin: 2px;"
            >
              {{ getChannelLabel(channel) }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click="toggleStatus(scope.row)"
            :style="{ color: scope.row.status === 1 ? '#f56c6c' : '#67c23a' }"
          >
            {{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
          <el-button type="text" size="mini" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 批次详情组件 -->
    <CouponBatchDetail
      :visible.sync="dialogFormVisible"
      :detail="currentBatch"
      @success="handleBatchSuccess"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import moment from "moment";
import CouponBatchDetail from "./coupon-batch-detail.vue";

export default {
  name: "CouponBatchList",
  components: {
    CouponBatchDetail,
  },
  filters: {
    formatDate(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD");
    },
  },
  filters: {
    formatDate(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD");
    },
  },
  data() {
    return {
      searchForm: {
        keyword: "",
        status: null,
      },
      listLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      dialogFormVisible: false,
      currentBatch: {}, // 当前编辑的批次数据
      statusOptions: [
        { label: "已启用", value: 1 },
        { label: "已禁用", value: 0 },
      ],
    };
  },
  created() {
    console.log("批次管理页面已加载");
    console.log("当前token:", this.$store.getters.token);
    this.getCouponBatchList();
  },
  methods: {
    // 获取发放渠道标签
    getChannelLabel(channel) {
      const channelMap = {
        'new_user': '新人注册',
        'view_activity': '浏览活动',
        'share_activity': '分享活动',
        'purchase': '销售购买'
      };
      return channelMap[channel] || channel;
    },

    // 获取优惠券批次列表
    getCouponBatchList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.keyword) {
        params.keyword = this.searchForm.keyword;
      }
      if (this.searchForm.status !== null) {
        params.status = this.searchForm.status;
      }

      console.log("调用批次搜索API，参数:", params);
      requestApi({
        name: "getCouponBatchesBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          console.log("批次列表API响应:", response);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取优惠券批次列表失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取优惠券批次列表失败", error);
          console.error("错误详情:", error.response);
          this.$message.error(
            "获取优惠券批次列表失败: " + (error.message || "未知错误")
          );
        });
    },
    // 获取优惠券批次列表
    getCouponBatchList() {
      this.listLoading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.keyword) {
        params.keyword = this.searchForm.keyword;
      }
      if (this.searchForm.coupon_id) {
        params.coupon_id = this.searchForm.coupon_id;
      }
      if (this.searchForm.status !== null) {
        params.status = this.searchForm.status;
      }

      requestApi({
        name: "getCouponBatchesBySearch",
        data: params,
      })
        .then((response) => {
          this.listLoading = false;
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取优惠券批次列表失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取优惠券批次列表失败", error);
          this.$message.error("获取优惠券批次列表失败");
        });
    },

    handleSearch() {
      this.pagination.page = 1;
      this.getCouponBatchList();
    },

    resetSearch() {
      this.searchForm = {
        keyword: "",
        status: null,
      };
      this.pagination.page = 1;
      this.getCouponBatchList();
    },



    handleAdd() {
      this.currentBatch = {}; // 清空当前批次数据，表示新增
      this.dialogFormVisible = true;
    },

    handleEdit(row) {
      this.listLoading = true;

      // 获取批次详情
      requestApi({
        name: "getCouponBatch",
        data: { id: row.id },
      })
        .then((response) => {
          this.listLoading = false;
          if (response && response.code === 200) {
            this.currentBatch = response.data; // 直接使用API返回的数据
            this.dialogFormVisible = true;
          } else {
            this.$message.error(response.message || "获取批次详情失败");
          }
        })
        .catch((error) => {
          this.listLoading = false;
          console.error("获取批次详情失败", error);
          this.$message.error("获取批次详情失败");
        });
    },

    // 批次操作成功回调
    handleBatchSuccess() {
      this.getCouponBatchList(); // 刷新列表
    },

    // 对话框关闭回调
    handleDialogClose() {
      this.currentBatch = {}; // 清空当前批次数据
    },

    handleDelete(row) {
      this.$confirm(`确定要删除批次"${row.name}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "deleteCouponBatch",
            data: { id: row.id },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getCouponBatchList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error("删除失败", error);
              this.$message.error("删除失败: " + (error.message || "未知错误"));
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    // 切换状态
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "启用" : "禁用";

      this.$confirm(`确定要${statusText}批次"${row.name}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "updateCouponBatchStatus",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: `${statusText}成功!`,
                });
                // 更新本地数据
                row.status = newStatus;
              } else {
                this.$message.error(response.message || `${statusText}失败`);
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error(`${statusText}失败`, error);
              this.$message.error(
                `${statusText}失败: ` + (error.message || "未知错误")
              );
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: `已取消${statusText}`,
          });
        });
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getCouponBatchList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getCouponBatchList();
    },
  },
};
</script>

<style lang="less" scoped>
.coupon-batch-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input,
        .el-select {
          width: 180px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}
</style>
