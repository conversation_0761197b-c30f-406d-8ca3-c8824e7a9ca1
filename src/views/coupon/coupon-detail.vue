<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      @close="handleClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="优惠券类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择优惠券类型"
            :disabled="formData.id !== undefined"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="优惠券名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入优惠券名称" />
        </el-form-item>

        <el-form-item label="优惠券描述" prop="description">
          <el-input
            type="textarea"
            v-model="formData.description"
            rows="4"
            placeholder="请输入优惠券描述"
          />
        </el-form-item>

        <el-form-item label="优惠券状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>

        <el-divider />
        <h3 class="section-title">使用条件</h3>

        <el-form-item label="条件范围" prop="condition_scope">
          <el-select
            v-model="formData.condition_scope"
            placeholder="请选择条件范围"
          >
            <el-option
              v-for="item in scopeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="条件对象" prop="condition_objects">
          <div class="product-selector">
            <el-button
              @click="showConditionProductDialog = true"
              type="primary"
              size="small"
            >
              选择产品 ({{ conditionProductsDisplay.length }})
            </el-button>
            <div
              v-if="conditionProductsDisplay.length > 0"
              class="selected-products"
            >
              <el-tag
                v-for="item in conditionProductsDisplay"
                :key="item.id"
                closable
                @close="removeConditionProduct(item.id)"
                style="margin: 2px"
              >
                {{ item.name }} (数量: {{ item.quantity }})
              </el-tag>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="条件金额" prop="condition_amount">
          <el-input-number
            v-model="formData.condition_amount"
            :precision="2"
            :min="0"
            :step="10"
          />
          <span class="form-tip">满足条件的最低金额</span>
        </el-form-item>

        <el-form-item label="使用数量" prop="usage_quantity">
          <el-input-number
            v-model="formData.usage_quantity"
            :min="1"
            :step="1"
          />
          <span class="form-tip">周期内可使用数量</span>
        </el-form-item>

        <el-form-item label="使用周期" prop="usage_cycle">
          <el-select
            v-model="formData.usage_cycle"
            placeholder="请选择使用周期"
          >
            <el-option
              v-for="item in usageCycleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="互斥规则" prop="mutual_exclusive_rules">
          <div class="coupon-selector">
            <el-button
              @click="showMutualExclusiveDialog = true"
              type="primary"
              size="small"
            >
              选择互斥优惠券 ({{ mutualExclusiveCouponsDisplay.length }})
            </el-button>
            <div
              v-if="mutualExclusiveCouponsDisplay.length > 0"
              class="selected-coupons"
            >
              <el-tag
                v-for="item in mutualExclusiveCouponsDisplay"
                :key="item.id"
                closable
                @close="removeMutualExclusiveCoupon(item.id)"
                style="margin: 2px"
              >
                {{ item.name }}
              </el-tag>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="支付渠道" prop="payment_channels">
          <el-checkbox-group v-model="formData.payment_channels">
            <el-checkbox
              v-for="item in paymentChannelOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider />
        <h3 class="section-title">作用设置</h3>

        <el-form-item label="作用范围" prop="apply_scope">
          <el-select
            v-model="formData.apply_scope"
            placeholder="请选择作用范围"
          >
            <el-option
              v-for="item in scopeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="作用对象" prop="apply_objects">
          <div class="product-selector">
            <el-button
              @click="showApplyProductDialog = true"
              type="primary"
              size="small"
            >
              选择产品 ({{ applyProductsDisplay.length }})
            </el-button>
            <div
              v-if="applyProductsDisplay.length > 0"
              class="selected-products"
            >
              <el-tag
                v-for="item in applyProductsDisplay"
                :key="item.id"
                closable
                @close="removeApplyProduct(item.id)"
                style="margin: 2px"
              >
                {{ item.name }} (数量: {{ item.quantity }})
              </el-tag>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="计算顺序" prop="apply_order">
          <el-input-number v-model="formData.apply_order" :min="0" :step="1" />
          <span class="form-tip">优惠券计算的优先级，数字越小优先级越高</span>
        </el-form-item>

        <!-- 根据优惠券类型显示不同的表单项 -->
        <!-- 折扣优惠券表单项 -->
        <template v-if="formData.type === 'discount'">
          <el-form-item label="最低消费金额" prop="min_amount">
            <el-input-number
              v-model="formData.min_amount"
              :precision="2"
              :min="0"
              :step="10"
            />
            <span class="form-tip">0表示无最低消费限制</span>
          </el-form-item>

          <el-divider />
          <h3 class="section-title">触发动作</h3>

          <el-form-item label="折扣比例" prop="discount_rate">
            <el-input-number
              v-model="formData.discount_rate"
              :precision="2"
              :min="0"
              :max="1"
              :step="0.1"
            />
            <span class="form-tip">0-1之间的小数，如0.8表示8折</span>
          </el-form-item>
          <el-form-item label="最大优惠金额" prop="max_discount">
            <el-input-number
              v-model="formData.max_discount"
              :precision="2"
              :min="0"
              :step="10"
            />
            <span class="form-tip">0表示无最大优惠限制</span>
          </el-form-item>
        </template>

        <!-- 满减优惠券表单项 -->
        <template v-if="formData.type === 'full_reduction'">
          <el-form-item label="满足金额" prop="full_amount">
            <el-input-number
              v-model="formData.full_amount"
              :precision="2"
              :min="0"
              :step="10"
            />
          </el-form-item>

          <el-divider />
          <h3 class="section-title">触发动作</h3>

          <el-form-item label="减免金额" prop="reduction_amount">
            <el-input-number
              v-model="formData.reduction_amount"
              :precision="2"
              :min="0"
              :step="10"
            />
          </el-form-item>
        </template>

        <!-- 限时特价优惠券表单项 -->
        <template v-if="formData.type === 'cash'">
          <el-divider />
          <h3 class="section-title">触发动作</h3>

          <el-form-item label="抵扣金额" prop="amount">
            <el-input-number
              v-model="formData.amount"
              :precision="2"
              :min="0"
              :step="10"
            />
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 条件产品选择对话框 -->
    <el-dialog
      title="选择条件产品"
      :visible.sync="showConditionProductDialog"
      width="800px"
    >
      <div class="product-search">
        <el-input
          v-model="productSearchKeyword"
          placeholder="搜索产品名称"
          @input="searchProducts"
          clearable
        >
          <el-button slot="append" @click="searchProducts">搜索</el-button>
        </el-input>
      </div>
      <el-table
        :data="productOptions"
        style="width: 100%; margin-top: 20px"
        max-height="400"
      >
        <el-table-column prop="name" label="产品名称" />
        <el-table-column prop="price" label="价格" width="100" />
        <el-table-column label="数量" width="120">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.selectedQuantity"
              :min="1"
              :step="1"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="addConditionProduct(scope.row)"
              type="primary"
              size="small"
              :disabled="isConditionProductSelected(scope.row.id)"
            >
              {{ isConditionProductSelected(scope.row.id) ? "已选择" : "选择" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showConditionProductDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 作用产品选择对话框 -->
    <el-dialog
      title="选择作用产品"
      :visible.sync="showApplyProductDialog"
      width="800px"
    >
      <div class="product-search">
        <el-input
          v-model="productSearchKeyword"
          placeholder="搜索产品名称"
          @input="searchProducts"
          clearable
        >
          <el-button slot="append" @click="searchProducts">搜索</el-button>
        </el-input>
      </div>
      <el-table
        :data="productOptions"
        style="width: 100%; margin-top: 20px"
        max-height="400"
      >
        <el-table-column prop="name" label="产品名称" />
        <el-table-column prop="price" label="价格" width="100" />
        <el-table-column label="数量" width="120">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.selectedQuantity"
              :min="1"
              :step="1"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="addApplyProduct(scope.row)"
              type="primary"
              size="small"
              :disabled="isApplyProductSelected(scope.row.id)"
            >
              {{ isApplyProductSelected(scope.row.id) ? "已选择" : "选择" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showApplyProductDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 互斥优惠券选择对话框 -->
    <el-dialog
      title="选择互斥优惠券"
      :visible.sync="showMutualExclusiveDialog"
      width="800px"
    >
      <div class="coupon-search">
        <el-input
          v-model="couponSearchKeyword"
          placeholder="搜索优惠券名称"
          @input="searchCoupons"
          clearable
        >
          <el-button slot="append" @click="searchCoupons">搜索</el-button>
        </el-input>
      </div>
      <el-table
        :data="couponOptions"
        style="width: 100%; margin-top: 20px"
        max-height="400"
      >
        <el-table-column prop="name" label="优惠券名称" />
        <el-table-column prop="type" label="类型" width="100" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="addMutualExclusiveCoupon(scope.row)"
              type="primary"
              size="small"
              :disabled="
                isMutualExclusiveCouponSelected(scope.row.id) ||
                scope.row.id === formData.id
              "
            >
              {{
                isMutualExclusiveCouponSelected(scope.row.id)
                  ? "已选择"
                  : "选择"
              }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showMutualExclusiveDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "CouponDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      formData: {
        id: undefined,
        name: "",
        type: "cash",
        description: "",

        // 条件相关字段
        condition_scope: "order",
        condition_objects: [],
        condition_amount: 0,
        usage_quantity: 1,
        usage_cycle: "per_order",
        mutual_exclusive_rules: [],
        payment_channels: [],

        // 作用相关字段
        apply_scope: "order",
        apply_objects: [],
        apply_order: 0,

        status: 1,

        discount_rate: 1,
        min_amount: 0,
        max_discount: 0,

        full_amount: 0,
        reduction_amount: 0,

        amount: 0,
      },
      typeOptions: [
        { label: "折扣券", value: "discount" },
        { label: "满减券", value: "full_reduction" },
        { label: "现金券", value: "cash" },
      ],
      scopeOptions: [
        { label: "订单", value: "order" },
        { label: "商品", value: "product" },
      ],
      statusOptions: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 },
      ],
      usageCycleOptions: [
        { label: "每次订单", value: "per_order" },
        { label: "每天", value: "per_day" },
        { label: "每周", value: "per_week" },
        { label: "每月", value: "per_month" },
        { label: "每年", value: "per_year" },
      ],
      paymentChannelOptions: [
        { label: "微信支付", value: "wechat" },
        { label: "个人账户余额", value: "personal_balance" },
        { label: "企业账户余额", value: "enterprise_balance" },
      ],

      // 对话框状态
      showConditionProductDialog: false,
      showApplyProductDialog: false,
      showMutualExclusiveDialog: false,

      // 搜索相关
      productSearchKeyword: "",
      couponSearchKeyword: "",
      productOptions: [],
      couponOptions: [],

      // 选中的产品和优惠券
      selectedConditionProducts: [],
      selectedApplyProducts: [],
      selectedMutualExclusiveCoupons: [],
      rules: {
        name: [
          { required: true, message: "请输入优惠券名称", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择优惠券类型", trigger: "change" },
        ],
        condition_scope: [
          { required: true, message: "请选择条件范围", trigger: "change" },
        ],
        apply_scope: [
          { required: true, message: "请选择作用范围", trigger: "change" },
        ],
        discount_rate: [
          { required: true, message: "请输入折扣比例", trigger: "blur" },
        ],
        full_amount: [
          { required: true, message: "请输入满足金额", trigger: "blur" },
        ],
        reduction_amount: [
          { required: true, message: "请输入减免金额", trigger: "blur" },
        ],
        amount: [
          { required: true, message: "请输入抵扣金额", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    // 条件产品显示列表
    conditionProductsDisplay() {
      return this.selectedConditionProducts.map((item) => ({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
      }));
    },
    // 作用产品显示列表
    applyProductsDisplay() {
      return this.selectedApplyProducts.map((item) => ({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
      }));
    },
    // 互斥优惠券显示列表
    mutualExclusiveCouponsDisplay() {
      return this.selectedMutualExclusiveCoupons.map((item) => ({
        id: item.id,
        name: item.name,
      }));
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      // 只在对话框打开且没有detail数据时初始化
      if (val && (!this.detail || Object.keys(this.detail).length === 0)) {
        this.initFormData();
      }
    },
    detail: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initFormData();
        }
      },
      immediate: true,
    },
    showConditionProductDialog(val) {
      if (val && this.productOptions.length === 0) {
        this.getActiveProducts();
      }
    },
    showApplyProductDialog(val) {
      if (val && this.productOptions.length === 0) {
        this.getActiveProducts();
      }
    },
    showMutualExclusiveDialog(val) {
      if (val && this.couponOptions.length === 0) {
        this.getCoupons();
      }
    },
  },
  methods: {
    initFormData() {
      if (this.detail && Object.keys(this.detail).length > 0) {
        // 使用detail的数据，但保留默认值作为fallback
        this.formData = {
          id: this.detail.id,
          name: this.detail.name || "",
          type: this.detail.type || "cash",
          description: this.detail.description || "",
          start_time: this.detail.start_time || "",
          end_time: this.detail.end_time || "",

          // 条件相关字段
          condition_scope: this.detail.condition_scope || "order",
          condition_objects: this.detail.condition_objects || [],
          condition_amount: this.detail.condition_amount || 0,
          usage_quantity: this.detail.usage_quantity || 0,
          usage_cycle: this.detail.usage_cycle || "per_order",
          usage_limit: this.detail.usage_limit || 0,
          mutual_exclusive_rules: this.detail.mutual_exclusive_rules || [],
          payment_channels: this.detail.payment_channels || [],

          // 作用相关字段
          apply_scope: this.detail.apply_scope || "order",
          apply_objects: this.detail.apply_objects || [],
          apply_order: this.detail.apply_order || 0,

          status: this.detail.status !== undefined ? this.detail.status : 1,

          // 特定类型字段 - 确保正确获取数值
          discount_rate: Number(this.detail.discount_rate) || 1,
          min_amount: Number(this.detail.min_amount) || 0,
          max_discount: Number(this.detail.max_discount) || 0,
          full_amount: Number(this.detail.full_amount) || 0,
          reduction_amount: Number(this.detail.reduction_amount) || 0,
          amount: Number(this.detail.amount) || 0,
        };
        // 初始化选中的产品和优惠券
        this.initSelectedData();
      } else {
        this.formData = {
          id: undefined,
          name: "",
          type: "cash",
          description: "",

          // 条件相关字段
          condition_scope: "order",
          condition_objects: [],
          condition_amount: 0,
          usage_quantity: 1,
          usage_cycle: "per_order",
          mutual_exclusive_rules: [],
          payment_channels: [],

          // 作用相关字段
          apply_scope: "order",
          apply_objects: [],
          apply_order: 0,

          status: 1,

          discount_rate: 1,
          min_amount: 0,
          max_discount: 0,

          full_amount: 0,
          reduction_amount: 0,

          amount: 0,
        };
        // 清空选中的数据
        this.selectedConditionProducts = [];
        this.selectedApplyProducts = [];
        this.selectedMutualExclusiveCoupons = [];
      }
    },

    // 初始化选中的数据
    async initSelectedData() {
      // 清空现有数据，避免重复
      this.selectedConditionProducts = [];
      this.selectedApplyProducts = [];
      this.selectedMutualExclusiveCoupons = [];

      // 初始化条件产品
      if (
        this.formData.condition_objects &&
        this.formData.condition_objects.length > 0
      ) {
        for (const obj of this.formData.condition_objects) {
          const productId = Object.keys(obj)[0];
          const quantity = obj[productId];
          try {
            const product = await this.getProductById(parseInt(productId));
            this.selectedConditionProducts.push({
              id: parseInt(productId),
              quantity: quantity,
              name: product ? product.name : `产品${productId}`,
            });
          } catch (error) {
            this.selectedConditionProducts.push({
              id: parseInt(productId),
              quantity: quantity,
              name: `产品${productId}`,
            });
          }
        }
      }

      // 初始化作用产品
      if (
        this.formData.apply_objects &&
        this.formData.apply_objects.length > 0
      ) {
        for (const obj of this.formData.apply_objects) {
          const productId = Object.keys(obj)[0];
          const quantity = obj[productId];
          try {
            const product = await this.getProductById(parseInt(productId));
            this.selectedApplyProducts.push({
              id: parseInt(productId),
              quantity: quantity,
              name: product ? product.name : `产品${productId}`,
            });
          } catch (error) {
            this.selectedApplyProducts.push({
              id: parseInt(productId),
              quantity: quantity,
              name: `产品${productId}`,
            });
          }
        }
      }

      // 初始化互斥优惠券
      if (
        this.formData.mutual_exclusive_rules &&
        this.formData.mutual_exclusive_rules.length > 0
      ) {
        // 去重处理
        const uniqueCouponIds = [
          ...new Set(this.formData.mutual_exclusive_rules),
        ];
        for (const couponId of uniqueCouponIds) {
          try {
            const coupon = await this.getCouponById(couponId);
            this.selectedMutualExclusiveCoupons.push({
              id: couponId,
              name: coupon ? coupon.name : `优惠券${couponId}`,
            });
          } catch (error) {
            this.selectedMutualExclusiveCoupons.push({
              id: couponId,
              name: `优惠券${couponId}`,
            });
          }
        }
      }
    },

    // 根据ID获取产品信息
    async getProductById(productId) {
      try {
        const response = await requestApi({
          name: "getProduct",
          data: { product_id: productId },
        });
        if (response && response.code === 200) {
          return response.data;
        }
      } catch (error) {
        console.error("获取产品信息失败", error);
      }
      return null;
    },

    // 根据ID获取优惠券信息
    async getCouponById(couponId) {
      try {
        const response = await requestApi({
          name: "getCoupon",
          data: { id: couponId },
        });
        if (response && response.code === 200) {
          return response.data;
        }
      } catch (error) {
        console.error("获取优惠券信息失败", error);
      }
      return null;
    },

    // 搜索产品
    searchProducts() {
      if (this.productSearchKeyword.trim() === "") {
        this.getActiveProducts();
        return;
      }

      requestApi({
        name: "getProductsBySearch",
        data: {
          keyword: this.productSearchKeyword,
          page: 1,
          pageSize: 50,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.productOptions = (response.data.list || []).map((product) => ({
              ...product,
              selectedQuantity: 1,
            }));
          }
        })
        .catch((error) => {
          console.error("搜索产品失败", error);
          this.$message.error("搜索产品失败");
        });
    },

    // 获取活跃产品列表
    getActiveProducts() {
      requestApi({
        name: "getActiveProducts",
        data: {
          skip: 0,
          limit: 50,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.productOptions = (response.data || []).map((product) => ({
              ...product,
              selectedQuantity: 1,
            }));
          }
        })
        .catch((error) => {
          console.error("获取产品列表失败", error);
          this.$message.error("获取产品列表失败");
        });
    },

    // 搜索优惠券
    searchCoupons() {
      if (this.couponSearchKeyword.trim() === "") {
        this.getCoupons();
        return;
      }

      requestApi({
        name: "getCouponsBySearch",
        data: {
          keyword: this.couponSearchKeyword,
          page: 1,
          pageSize: 50,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.couponOptions = response.data.list || [];
          }
        })
        .catch((error) => {
          console.error("搜索优惠券失败", error);
          this.$message.error("搜索优惠券失败");
        });
    },

    // 获取优惠券列表
    getCoupons() {
      requestApi({
        name: "getCouponsBySearch",
        data: {
          page: 1,
          pageSize: 50,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.couponOptions = response.data.list || [];
          }
        })
        .catch((error) => {
          console.error("获取优惠券列表失败", error);
          this.$message.error("获取优惠券列表失败");
        });
    },

    // 条件产品相关方法
    addConditionProduct(product) {
      const existingIndex = this.selectedConditionProducts.findIndex(
        (p) => p.id === product.id
      );
      if (existingIndex === -1) {
        this.selectedConditionProducts.push({
          id: product.id,
          name: product.name,
          quantity: product.selectedQuantity || 1,
        });
        this.updateConditionObjects();
      }
    },

    removeConditionProduct(productId) {
      this.selectedConditionProducts = this.selectedConditionProducts.filter(
        (p) => p.id !== productId
      );
      this.updateConditionObjects();
    },

    isConditionProductSelected(productId) {
      return this.selectedConditionProducts.some((p) => p.id === productId);
    },

    updateConditionObjects() {
      this.formData.condition_objects = this.selectedConditionProducts.map(
        (product) => ({
          [product.id]: product.quantity,
        })
      );
    },

    // 作用产品相关方法
    addApplyProduct(product) {
      const existingIndex = this.selectedApplyProducts.findIndex(
        (p) => p.id === product.id
      );
      if (existingIndex === -1) {
        this.selectedApplyProducts.push({
          id: product.id,
          name: product.name,
          quantity: product.selectedQuantity || 1,
        });
        this.updateApplyObjects();
      }
    },

    removeApplyProduct(productId) {
      this.selectedApplyProducts = this.selectedApplyProducts.filter(
        (p) => p.id !== productId
      );
      this.updateApplyObjects();
    },

    isApplyProductSelected(productId) {
      return this.selectedApplyProducts.some((p) => p.id === productId);
    },

    updateApplyObjects() {
      this.formData.apply_objects = this.selectedApplyProducts.map(
        (product) => ({
          [product.id]: product.quantity,
        })
      );
    },

    // 互斥优惠券相关方法
    addMutualExclusiveCoupon(coupon) {
      const existingIndex = this.selectedMutualExclusiveCoupons.findIndex(
        (c) => c.id === coupon.id
      );
      if (existingIndex === -1 && coupon.id !== this.formData.id) {
        this.selectedMutualExclusiveCoupons.push({
          id: coupon.id,
          name: coupon.name,
        });
        this.updateMutualExclusiveRules();
      }
    },

    removeMutualExclusiveCoupon(couponId) {
      this.selectedMutualExclusiveCoupons =
        this.selectedMutualExclusiveCoupons.filter((c) => c.id !== couponId);
      this.updateMutualExclusiveRules();
    },

    isMutualExclusiveCouponSelected(couponId) {
      return this.selectedMutualExclusiveCoupons.some((c) => c.id === couponId);
    },

    updateMutualExclusiveRules() {
      this.formData.mutual_exclusive_rules =
        this.selectedMutualExclusiveCoupons.map((coupon) => coupon.id);
    },

    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 确保数据格式正确
          this.updateConditionObjects();
          this.updateApplyObjects();
          this.updateMutualExclusiveRules();

          this.$emit("submit", this.formData);
        }
      });
    },
    handleCancel() {
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },
    handleClose() {
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },
  },

  // 组件创建时初始化数据
  created() {
    // 组件创建时初始化产品和优惠券列表
    this.getActiveProducts();
    this.getCoupons();
  },
};
</script>

<style lang="less" scoped>
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.product-selector,
.coupon-selector {
  width: 100%;
}

.selected-products,
.selected-coupons {
  margin-top: 10px;
  min-height: 32px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.selected-products:empty,
.selected-coupons:empty {
  display: none;
}

.product-search,
.coupon-search {
  margin-bottom: 20px;
}

.section-title {
  margin: 20px 0 10px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}
</style>
