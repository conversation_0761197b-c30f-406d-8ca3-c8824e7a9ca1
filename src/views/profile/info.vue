<template>
  <div class="user-info">
    <el-form :model="formData" ref="formDataRef">
      <el-form-item prop="user_name">
        <span slot="label">姓名：</span>
        <el-input v-model.trim="formData.user_name" disabled placeholder="输入姓名" maxlength="20"/>
      </el-form-item>
      <el-form-item prop="phone" :rules="[{ validator: this.checkPhoneNum, trigger: 'blur' }]">
        <span slot="label">电话：</span>
        <el-input v-model.trim="formData.phone" disabled placeholder="输入电话" maxlength="11"/>
      </el-form-item>
<!--      <el-form-item prop="identity" :rules="[{ min: 17, max: 18, pattern: /^[0-9a-zA-Z]*$/, message: '请输入正确的身份证', trigger: 'blur' }]">-->
<!--        <span slot="label">身份证：</span>-->
<!--        <el-input v-model.trim="formData.identity" disabled placeholder="输入身份证" />-->
<!--      </el-form-item>-->
      <el-form-item>
        <span slot="label">邮箱地址：</span>
        <el-input v-model.trim="formData.email" placeholder="输入邮箱地址" maxlength="50"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitUser" style="width: 100px">提交</el-button>
        <el-button type="warning" @click="changePassword" style="width: 100px" v-if="auth == 2">修改密码</el-button>
      </el-form-item>
      <el-dialog title="修改密码" width="400px" :visible.sync="dialogVisible">
        <div>
          <el-form :model="ValidateForm" ref="ValidateForm" label-width="80px" class="demo-ruleForm">
            <el-form-item label="验证码" prop="valid_code" :rules="[{ required: true, message: '请输入验证码' }]">
              <el-input type="text" maxlength="6" v-model.trim="ValidateForm.valid_code" placeholder="请输入验证码"
                @keyup.enter.native="handleLogin">
                <el-button type="text" slot="suffix" :style="{ color: time != 60 ? '#333' : '#333' }"
                  :disabled="time != 60" @click="getCode(formData.phone)">{{ buttonName }}
                </el-button>
              </el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="passwd" :rules="[{ required: true, message: '请输入密码' }]">
              <el-input v-model.number="ValidateForm.passwd" autocomplete="off"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm('ValidateForm')">提交</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </el-dialog>
    </el-form>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import { getToken, getTypes } from "@/utils/auth"; // get token from cookie
import { phone } from "@/utils/validate";
let interval = null;
const defaultDataItem = {
  user_id: null,
  email: "",
  identity: "",
  phone: "",
  user_name: "",
};
export default {
  name: "UserInfo",
  components: {},
  data() {
    return {
      formData: Object.assign({}, defaultDataItem),
      auth: getTypes(),
      dialogVisible: false,
      time: 60,
      buttonName: "获取验证码",
      ValidateForm: {
        phone: "", // 手机号
        valid_code: "", // 验证码
        passwd: "",
      }
    };
  },
  created() {
    this.getUserInfo();
  },
  methods: {
    // 手机号码校验函数
    checkPhoneNum(rule, value, callback) {
      var reg = /^1[3456789]\d{9}$/;
      if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('手机号码格式不正确'));
      }
    },
    // 获取验证码
    getCode(name) {
      let self = this;
      if (!phone(name)) {
        this.$message({
          message: "请输入正确的手机号",
          type: "error",
        });
        return;
      }
      requestApi({
        name: "sendValidateCode",
        data: {
          phone: this.formData.phone,
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$message({
            message: "验证码发送成功",
            type: "success",
          });

          interval = window.setInterval(() => {
            self.buttonName = "重新发送 " + self.time + "s";
            --self.time;
            if (self.time < 0) {
              self.buttonName = "获取验证码";
              self.time = 60;
              self.isDisabled = false;
              window.clearInterval(interval);
            }
          }, 1000);
          self.isDisabled = true;
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 修改密码
    changePassword() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.ValidateForm.clearValidate();
      });
    },
    // 提交修改密码
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ValidateForm.phone = this.formData.phone;
          requestApi({
            name: "updateUserPasswd",
            data: this.ValidateForm,
          }).then(async (res) => {
            if (res.code === 200) {
              await this.$store.dispatch("user/logout");
              // await this.$store.dispatch('user/resetToken')
              location.reload(); // 为了重新实例化vue-router对象以避免bug
              this.$router.push(`/login?redirect=${this.$route.fullPath}`);
              this.$message.success(res.msg);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 获取用户
    getUserInfo() {
      this.listLoading = true;
      requestApi({
        name: "getUserInfo",
      }).then((res) => {
        if (res.code == 200) {
          this.formData = res.data;
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 提交结果
    submitUser() {
      this.$refs["formDataRef"].validate((valid) => {
        if (valid) {
          const data = this.formData;
          requestApi({
            name: "updateUserInfo",
            data,
          }).then((res) => {
            if (res.code === 200) {
              this.$message.success("保存成功");
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.user-info {
  background-color: #fff;
  padding: 20px;

  /deep/.el-input__inner {
    max-width: 500px;
  }
}
</style>
