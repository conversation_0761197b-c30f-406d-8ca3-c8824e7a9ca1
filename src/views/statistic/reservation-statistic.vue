<template>
  <div class="meal-statistic">
    <div class="header-info">
      <h1>报餐就餐统计</h1>
      <div class="refresh-time">数据更新时间：{{ refreshTime }}</div>
    </div>

    <div class="calendar-container">
      <el-date-picker
        v-model="selectedDate"
        type="date"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        @change="handleDateChange"
        style="width: 200px"
      >
      </el-date-picker>
    </div>

    <el-table
      :data="statisticData"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      :summary-method="getSummaries"
      show-summary
    >
      <el-table-column
        prop="username"
        label="用户名"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="real_name"
        label="姓名"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nick_name"
        label="微信昵称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="product_name"
        label="产品名称"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
          prop="payment_enterprise"
          label="支付方"
          width="250"
          align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_date"
        label="预定日期"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_time"
        label="预定时段"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="paid_full_count"
        label="已预定"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="verified_count"
        label="已核销"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span class="verified-count">{{ scope.row.verified_count }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="cancelled_count"-->
      <!--        label="已取消"-->
      <!--        width="100"-->
      <!--        align="center"-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          <span class="cancelled-count">{{ scope.row.cancelled_count }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "ReservationStatistic",
  data() {
    return {
      refreshTime: new Date().toLocaleString(),
      selectedDate: new Date().toISOString().split("T")[0],
      listLoading: false,
      statisticData: [],
      summary: {
        paid_full: 0,
        verified: 0,
        cancelled: 0,
      },
    };
  },
  methods: {
    handleDateChange(date) {
      this.getStatisticData(date);
    },

    getStatisticData(date) {
      this.listLoading = true;

      const params = {
        dining_start_time: `${date} 00:00:00`,
        dining_end_time: `${date} 23:59:59`,
        page: 1,
        page_size: 10000,
        status: ["PAID_FULL", "VERIFIED", "AUTO_VERIFIED"],
      };

      requestApi({
        name: "getReservationReport",
        data: params,
      })
        .then((res) => {
          if (res.code === 200) {
            // 处理数据，按用户分组统计
            const userMap = new Map();

            // 过滤数据
            const filteredList = res.data.list.filter((item) => {
              const status = item.status.toUpperCase();
              // 过滤掉指定手机号
              if (
                item.phone === "17324408209" ||
                item.phone === "15766972573" ||
                item.phone === "17324408208" ||
                item.phone === "17328792528"
              ) {
                return false;
              }
              return ["PAID_FULL", "VERIFIED", "AUTO_VERIFIED"].includes(status);
            });

            filteredList.forEach((item) => {
              // 处理预订时段
              let reservation_period_date = "";
              let reservation_period_time = "";
              let sortTime = "";

              if (
                item.reservation_period &&
                item.reservation_period.includes("_")
              ) {
                const periodParts = item.reservation_period.split("_");
                if (periodParts.length === 2) {
                  // 提取日期部分 YYMMDD
                  const datePart = periodParts[0].substring(0, 6);
                  const year = "20" + datePart.substring(0, 2);
                  const month = datePart.substring(2, 4);
                  const day = datePart.substring(4, 6);
                  reservation_period_date = `${year}年${month}月${day}日`;

                  // 提取时间部分 HHmm_HHmm
                  const startTime = periodParts[0].substring(6, 10);
                  const endTime = periodParts[1].substring(6, 10);
                  const startHour = startTime.substring(0, 2);
                  const startMin = startTime.substring(2, 4);
                  const endHour = endTime.substring(0, 2);
                  const endMin = endTime.substring(2, 4);
                  reservation_period_time = `${startHour}点${startMin}分～${endHour}点${endMin}分`;

                  // 用于排序的时间字符串
                  sortTime = `${year}-${month}-${day} ${startHour}:${startMin}`;
                }
              }

              // 修改分组键：按 username + product_name + reservation_period + payment_enterprise 分组
              const key = `${item.username}_${item.product_name}_${item.reservation_period}_${item.payment_enterprise}`;
              if (!userMap.has(key)) {
                userMap.set(key, {
                  username: item.username,
                  real_name: item.real_name,
                  nick_name: item.nick_name,
                  product_name: item.product_name,
                  reservation_period_date,
                  reservation_period_time,
                  sortTime,
                  payment_enterprise: item.payment_enterprise,
                  paid_full_count: 0,
                  verified_count: 0,
                  cancelled_count: 0,
                });
              }

              const userData = userMap.get(key);
              switch (item.status.toUpperCase()) {
                case "PAID_FULL":
                  userData.paid_full_count = userData.paid_full_count + item.quantity;
                  break;
                case "VERIFIED":
                  userData.verified_count = userData.verified_count + item.quantity;
                  break;
                case "AUTO_VERIFIED":
                  userData.verified_count = userData.verified_count + item.quantity;
                  break;
              }
            });

            // 转换为数组并排序
            this.statisticData = Array.from(userMap.values()).sort((a, b) => {
              return new Date(a.sortTime) - new Date(b.sortTime);
            });
            this.refreshTime = new Date().toLocaleString();
          } else {
            this.$message.error(res.message || "获取统计数据失败");
            this.statisticData = [];
          }
          this.listLoading = false;
        })
        .catch((error) => {
          console.error("获取统计数据失败", error);
          this.$message.error("获取统计数据失败");
          this.statisticData = [];
          this.listLoading = false;
        });
    },

    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "小计";
          return;
        }
        if (index === 7) {
          // 已预定列（调整索引，因为添加了产品名称列）
          const values = data.map((item) => item.paid_full_count);
          sums[index] = values.reduce((prev, curr) => {
            return prev + curr;
          }, 0);
        } else if (index === 8) {
          // 已核销列（调整索引，因为添加了产品名称列）
          const values = data.map((item) => item.verified_count);
          sums[index] = values.reduce((prev, curr) => {
            return prev + curr;
          }, 0);
        } else if (index === 9) {
          // 已取消列（调整索引，因为添加了产品名称列）
          const values = data.map((item) => item.cancelled_count);
          sums[index] = values.reduce((prev, curr) => {
            return prev + curr;
          }, 0);
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
  },

  created() {
    this.getStatisticData(this.selectedDate);
  },
};
</script>

<style lang="less" scoped>
.meal-statistic {
  background-color: #fff;
  padding: 20px;

  .header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
    }

    .refresh-time {
      color: #909399;
      font-size: 14px;
    }
  }

  .calendar-container {
    margin-bottom: 20px;
  }

  .verified-count {
    color: #67c23a;
  }

  .cancelled-count {
    color: #f56c6c;
  }
}
</style>
