<template>
  <div class="order-statistic">
    <div class="header-info">
      <h1>订单预订统计</h1>
      <div class="refresh-time">数据更新时间：{{ refreshTime }}</div>
    </div>

    <div class="calendar-container">
      <el-date-picker
        v-model="selectedDate"
        type="date"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        @change="handleDateChange"
        style="width: 200px"
      >
      </el-date-picker>
    </div>

    <el-table
      :data="statisticData"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      :summary-method="getSummaries"
      show-summary
      :span-method="arraySpanMethod"
    >
      <el-table-column
        prop="order_no"
        label="订单ID"
        width="180"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="real_name"
        label="姓名"
        width="100"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="username"
        label="用户名"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact_name"
        label="联系人"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact_phone"
        label="联系电话"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="persons"
        label="用餐人数"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_date"
        label="预订日期"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_time"
        label="预订时段"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_time"
        label="下单时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="status"
        label="订单状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span :class="getOrderStatusClass(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="total_amount"
        label="订单总金额"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span>¥{{ scope.row.total_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="actual_amount_paid"
        label="实付金额"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span>¥{{ scope.row.actual_amount_paid }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="payment_method"
        label="支付方式"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ getPaymentMethodText(scope.row.payment_method) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="product_name"
        label="产品名称"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="quantity"
        label="数量"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="price"
        label="单价"
        width="80"
        align="center"
      >
        <template slot-scope="scope">
          <span>¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="subtotal"
        label="小计"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span>¥{{ scope.row.subtotal }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="payment_enterprise"
        label="支付方"
        width="200"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="verified_count"
        label="已核销"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span class="verified-count">{{ scope.row.verified_count || 0 }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "ReservationOrderStatistic",
  data() {
    return {
      refreshTime: new Date().toLocaleString(),
      selectedDate: new Date().toISOString().split("T")[0],
      listLoading: false,
      statisticData: [],
      spanArr: [], // 用于合并单元格
      pos: 0,
    };
  },
  methods: {
    handleDateChange(date) {
      this.getStatisticData(date);
    },

    getStatisticData(date) {
      this.listLoading = true;

      const params = {
        dining_start_time: `${date} 00:00:00`,
        dining_end_time: `${date} 23:59:59`,
        page: 1,
        page_size: 10000,
        status: ["PAID_FULL", "VERIFIED", "AUTO_VERIFIED"],
      };

      requestApi({
        name: "getReservationOrderReport",
        data: params,
      })
        .then((res) => {
          if (res.code === 200) {
            // 处理数据，展开订单项目
            const expandedData = [];

            res.data.list.forEach((order) => {
              // 处理预订时段
              let reservation_period_date = "";
              let reservation_period_time = "";

              if (
                order.reservation_period &&
                order.reservation_period.includes("_")
              ) {
                const periodParts = order.reservation_period.split("_");
                if (periodParts.length === 2) {
                  // 提取日期部分 YYMMDD
                  const datePart = periodParts[0].substring(0, 6);
                  const year = "20" + datePart.substring(0, 2);
                  const month = datePart.substring(2, 4);
                  const day = datePart.substring(4, 6);
                  reservation_period_date = `${year}年${month}月${day}日`;

                  // 提取时间部分 HHmm_HHmm
                  const startTime = periodParts[0].substring(6, 10);
                  const endTime = periodParts[1].substring(6, 10);
                  const startHour = startTime.substring(0, 2);
                  const startMin = startTime.substring(2, 4);
                  const endHour = endTime.substring(0, 2);
                  const endMin = endTime.substring(2, 4);
                  reservation_period_time = `${startHour}点${startMin}分～${endHour}点${endMin}分`;
                }
              }

              // 为每个订单项目创建一行数据
              if (order.order_items && order.order_items.length > 0) {
                order.order_items.forEach((item, index) => {
                  expandedData.push({
                    // 订单基本信息
                    order_no: order.order_no,
                    real_name: order.real_name,
                    username: order.username,
                    nick_name: order.nick_name,
                    phone: order.phone,
                    contact_name: order.contact_name,
                    contact_phone: order.contact_phone,
                    persons: order.persons,
                    reservation_period_date,
                    reservation_period_time,
                    reservation_time: order.reservation_time,
                    remark: order.remark || '',
                    status: order.status,
                    total_amount: order.total_amount,
                    actual_amount_paid: order.actual_amount_paid,
                    payment_method: order.payment_method,

                    // 订单项目信息
                    product_name: item.product_name,
                    quantity: item.quantity,
                    price: item.price,
                    subtotal: item.subtotal,
                    payment_enterprise: item.payment_enterprise,

                    // 核销状态（根据订单状态判断）
                    verified_count: order.status === 'VERIFIED' || order.status === 'AUTO_VERIFIED' ? item.quantity : 0,

                    // 用于合并单元格的标识
                    order_id: order.id,
                    item_index: index,
                    is_first_item: index === 0,
                    items_count: order.order_items.length,
                  });
                });
              } else {
                // 如果没有订单项目，创建一个空的行
                expandedData.push({
                  order_no: order.order_no,
                  real_name: order.real_name,
                  username: order.username,
                  nick_name: order.nick_name,
                  phone: order.phone,
                  contact_name: order.contact_name,
                  contact_phone: order.contact_phone,
                  persons: order.persons,
                  reservation_period_date,
                  reservation_period_time,
                  reservation_time: order.reservation_time,
                  remark: order.remark || '',
                  status: order.status,
                  total_amount: order.total_amount,
                  actual_amount_paid: order.actual_amount_paid,
                  payment_method: order.payment_method,
                  product_name: '',
                  quantity: 0,
                  price: 0,
                  subtotal: 0,
                  payment_enterprise: '',
                  verified_count: 0,
                  order_id: order.id,
                  item_index: 0,
                  is_first_item: true,
                  items_count: 1,
                });
              }
            });

            this.statisticData = expandedData;
            this.getSpanArr(expandedData);
            this.refreshTime = new Date().toLocaleString();
          } else {
            this.$message.error(res.message || "获取统计数据失败");
            this.statisticData = [];
          }
          this.listLoading = false;
        })
        .catch((error) => {
          console.error("获取统计数据失败", error);
          this.$message.error("获取统计数据失败");
          this.statisticData = [];
          this.listLoading = false;
        });
    },

    // 计算合并单元格的数组
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;

      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 如果当前行和上一行是同一个订单，则合并
          if (data[i].order_id === data[i - 1].order_id) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },

    // 合并单元格方法
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列：订单ID、姓名、用户名、联系人、联系电话、用餐人数、预订日期、预订时段、下单时间、备注、订单状态、订单总金额、实付金额、支付方式
      const mergeColumns = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];

      if (mergeColumns.includes(columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },

    // 获取订单状态样式类
    getOrderStatusClass(status) {
      if (status === "PENDING") {
        return "status-pending";
      } else if (status === "PAID_FULL") {
        return "status-paid-full";
      } else if (status === "CANCELLED") {
        return "status-canceled";
      } else if (status === "VERIFIED") {
        return "status-verified";
      } else if (status === "AUTO_VERIFIED") {
        return "status-verified";
      }
      return "";
    },

    // 获取支付方式文本
    getPaymentMethodText(method) {
      switch (method) {
        case "ENTERPRISE_ACCOUNT_BALANCE":
          return "企业账户余额";
        case "ACCOUNT_BALANCE":
          return "账户余额";
        case "WECHAT_PAY":
          return "微信支付";
        case "ALIPAY":
          return "支付宝";
        default:
          return method || '';
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "PENDING":
          return "待支付";
        case "PAID_FULL":
          return "已支付";
        case "VERIFIED":
          return "已就餐";
        case "CANCELLED":
          return "已取消";
        case "AUTO_VERIFIED":
          return "超时自动核销";
        default:
          return status;
      }
    },

    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "小计";
          return;
        }

        // 订单总金额汇总（第11列）
        if (index === 11) {
          // 去重计算，只计算每个订单的第一行
          const uniqueOrders = {};
          data.forEach(item => {
            if (item.is_first_item) {
              uniqueOrders[item.order_id] = item.total_amount;
            }
          });
          const totalAmount = Object.values(uniqueOrders).reduce((prev, curr) => prev + curr, 0);
          sums[index] = `¥${totalAmount}`;
        }
        // 实付金额汇总（第12列）
        else if (index === 12) {
          const uniqueOrders = {};
          data.forEach(item => {
            if (item.is_first_item) {
              uniqueOrders[item.order_id] = item.actual_amount_paid;
            }
          });
          const totalPaid = Object.values(uniqueOrders).reduce((prev, curr) => prev + curr, 0);
          sums[index] = `¥${totalPaid}`;
        }
        // 数量汇总（第15列）
        else if (index === 15) {
          const totalQuantity = data.reduce((prev, curr) => prev + (curr.quantity || 0), 0);
          sums[index] = totalQuantity;
        }
        // 小计汇总（第17列）
        else if (index === 17) {
          const totalSubtotal = data.reduce((prev, curr) => prev + (curr.subtotal || 0), 0);
          sums[index] = `¥${totalSubtotal}`;
        }
        // 已核销汇总（第19列）
        else if (index === 19) {
          const totalVerified = data.reduce((prev, curr) => prev + (curr.verified_count || 0), 0);
          sums[index] = totalVerified;
        }
        else {
          sums[index] = "";
        }
      });
      return sums;
    },
  },

  created() {
    this.getStatisticData(this.selectedDate);
  },
};
</script>

<style lang="less" scoped>
.order-statistic {
  background-color: #fff;
  padding: 20px;

  .header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
    }

    .refresh-time {
      color: #909399;
      font-size: 14px;
    }
  }

  .calendar-container {
    margin-bottom: 20px;
  }

  .status-pending {
    color: #e6a23c;
  }

  .status-paid-full {
    color: #3ea5ea;
  }

  .status-verified {
    color: #67c23a;
  }

  .status-canceled {
    color: #f56c6c;
  }

  .verified-count {
    color: #67c23a;
  }

  // 表格样式优化
  /deep/ .el-table {
    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa;
        color: #303133;
        font-weight: bold;
      }
    }

    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }

    // 合并单元格的边框处理
    .el-table__body {
      td {
        border-right: 1px solid #ebeef5;
      }
    }
  }

  // 固定列样式
  /deep/ .el-table__fixed-left {
    box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  }

  // 汇总行样式
  /deep/ .el-table__footer-wrapper {
    .el-table__footer {
      td {
        background-color: #fafafa;
        font-weight: bold;
        color: #303133;
      }
    }
  }
}
</style>
