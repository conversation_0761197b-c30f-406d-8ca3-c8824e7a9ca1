<template>
  <div class="order-record">
    <div class="page-header"><i class="el-icon-document"></i>商务餐订单记录</div>

    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <div class="form-row">
          <el-form-item label="手机:">
            <el-input
              v-model="filterForm.phone"
              placeholder="输入手机号"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="订餐人:">
            <el-input
              v-model="filterForm.realName"
              placeholder="输入订餐人"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="微信昵称:">
            <el-input
              v-model="filterForm.nickName"
              placeholder="输入微信昵称"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系人:">
            <el-input
              v-model="filterForm.contactName"
              placeholder="输入联系人"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="订单状态:">
            <el-select
              v-model="filterForm.status"
              placeholder="请选择"
              clearable
              size="mini"
            >
              <el-option label="已支付" value="PAID_FULL"></el-option>
<!--              <el-option label="待支付" value="PENDING"></el-option>-->
              <el-option label="已就餐" value="VERIFIED"></el-option>
              <el-option label="已取消" value="CANCELLED"></el-option>
              <el-option label="超时自动核销" value="AUTO_VERIFIED"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="就餐日期:">
            <el-date-picker
              v-model="filterForm.diningStartDate"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
            <span style="margin: 0 5px">-</span>
            <el-date-picker
              v-model="filterForm.diningEndDate"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="下单日期:">
            <el-date-picker
              v-model="filterForm.reservationStartDate"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
            <span style="margin: 0 5px">-</span>
            <el-date-picker
              v-model="filterForm.reservationEndDate"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
          </el-form-item>
          <el-button type="primary" @click="handleFilter" size="mini"
            >查询
          </el-button>
          <el-button @click="resetFilter" size="mini">重置</el-button>
        </div>
      </el-form>
    </div>

    <div class="action-container">
      <el-button
        type="success"
        icon="el-icon-download"
        size="mini"
        @click="exportData"
        >导出清单
      </el-button>
    </div>

    <el-table
      :data="orderRecords"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      max-height="600"
    >
      <el-table-column
        prop="id"
        label="序号"
        width="60"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="order_no"
        label="订单号"
        width="180"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="phone"
        label="手机"
        width="110"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="real_name"
        label="姓名"
        width="80"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="nick_name"
        label="微信昵称"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact_name"
        label="联系人"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact_phone"
        label="联系电话"
        width="110"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="persons"
        label="用餐人数"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_date"
        label="预订日期"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_time"
        label="预订时段"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_time"
        label="下单时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="total_amount"
        label="订单总额"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span>¥{{ scope.row.total_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="actual_amount_paid"
        label="实付金额"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span>¥{{ scope.row.actual_amount_paid }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="payment_method"
        label="支付方式"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ getPaymentMethodText(scope.row.payment_method) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="90" align="center">
        <template slot-scope="scope">
          <span :class="getOrderStatusClass(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            class="primary-btn"
            @click="viewOrderDetail(scope.row)"
          >
            订单详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
      title="订单详情"
      :visible.sync="orderDetailVisible"
      width="600px"
      class="order-detail-dialog"
    >
      <div class="detail-content" v-if="currentOrder">
        <div class="order-info">
          <h4>订单信息</h4>
          <div class="order-items">
            <div class="order-item">
              <span class="item-label">订单号:</span>
              <span class="item-value">{{ currentOrder.order_no }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">订餐人:</span>
              <span class="item-value">{{ currentOrder.real_name }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">联系人:</span>
              <span class="item-value">{{ currentOrder.contact_name }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">联系电话:</span>
              <span class="item-value">{{ currentOrder.contact_phone }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">用餐人数:</span>
              <span class="item-value">{{ currentOrder.persons }}人</span>
            </div>
            <div class="order-item">
              <span class="item-label">预订时间:</span>
              <span class="item-value">{{ currentOrder.reservation_period_date }} {{ currentOrder.reservation_period_time }}</span>
            </div>
            <div class="order-item">
              <span class="item-label">备注:</span>
              <span class="item-value">{{ currentOrder.remark || '无' }}</span>
            </div>
          </div>
        </div>

        <div class="order-products">
          <h4>订单明细</h4>
          <el-table :data="currentOrder.order_items" border size="mini">
            <el-table-column prop="product_name" label="产品名称" align="center"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
            <el-table-column prop="price" label="单价" width="80" align="center">
              <template slot-scope="scope">
                <span>¥{{ scope.row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="subtotal" label="小计" width="80" align="center">
              <template slot-scope="scope">
                <span>¥{{ scope.row.subtotal }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="payment_enterprise" label="支付方" align="center"></el-table-column>
          </el-table>
        </div>

        <div class="order-total">
          <div class="total-item">
            <span class="total-label">订单总额:</span>
            <span class="total-value">¥{{ currentOrder.total_amount }}</span>
          </div>
          <div class="total-item">
            <span class="total-label">应付金额:</span>
            <span class="total-value">¥{{ currentOrder.payable_amount }}</span>
          </div>
          <div class="total-item final-total">
            <span class="total-label">实付金额:</span>
            <span class="total-value">¥{{ currentOrder.actual_amount_paid }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import axios from "axios";
import { getToken } from "@/utils/auth";

export default {
  name: "ReservationOrderRecord",
  data() {
    return {
      title: "商务餐订单记录",

      // 筛选表单
      filterForm: {
        phone: "",
        realName: "",
        nickName: "",
        contactName: "",
        status: "",
        diningStartDate: "",
        diningEndDate: "",
        reservationStartDate: "",
        reservationEndDate: "",
      },

      // 表格加载状态
      listLoading: false,

      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 对话框
      orderDetailVisible: false,
      currentOrder: null,

      // 订单记录数据
      orderRecords: [],
    };
  },

  methods: {
    // 获取订单状态样式类
    getOrderStatusClass(status) {
      if (status === "PENDING") {
        return "status-pending";
      } else if (status === "PAID_FULL") {
        return "status-paid-full";
      } else if (status === "CANCELLED") {
        return "status-canceled";
      } else if (status === "VERIFIED") {
        return "status-verified";
      } else if (status === "AUTO_VERIFIED") {
        return "status-verified";
      }
      return "";
    },

    // 获取支付方式文本
    getPaymentMethodText(method) {
      switch (method) {
        case "ENTERPRISE_ACCOUNT_BALANCE":
          return "企业账户余额";
        case "ACCOUNT_BALANCE":
          return "账户余额";
        case "WECHAT_PAY":
          return "微信支付";
        case "ALIPAY":
          return "支付宝";
        default:
          return method;
      }
    },

    // 处理筛选
    handleFilter() {
      this.currentPage = 1;
      this.getOrderRecords();
    },

    // 重置筛选
    resetFilter() {
      this.filterForm = {
        phone: "",
        realName: "",
        nickName: "",
        contactName: "",
        status: "",
        diningStartDate: "",
        diningEndDate: "",
        reservationStartDate: "",
        reservationEndDate: "",
      };
      this.handleFilter();
    },

    // 获取订单记录
    getOrderRecords() {
      this.listLoading = true;

      // 构建请求参数
      const params = {
        phone: this.filterForm.phone || undefined,
        page: this.currentPage,
        page_size: this.pageSize,
      };

      // 处理姓名参数
      if (this.filterForm.realName) {
        params.real_name = this.filterForm.realName;
      }

      // 处理微信昵称参数
      if (this.filterForm.nickName) {
        params.nick_name = this.filterForm.nickName;
      }

      // 处理联系人参数
      if (this.filterForm.contactName) {
        params.contact_name = this.filterForm.contactName;
      }

      // 处理状态参数
      if (this.filterForm.status) {
        params.status = [this.filterForm.status];
      } else {
        params.status = ["PAID_FULL", "CANCELLED", "VERIFIED", "AUTO_VERIFIED"];
      }

      // 处理就餐日期参数
      if (this.filterForm.diningStartDate) {
        params.dining_start_time = `${this.filterForm.diningStartDate} 00:00:00`;
      }

      if (this.filterForm.diningEndDate) {
        params.dining_end_time = `${this.filterForm.diningEndDate} 23:59:59`;
      }

      // 处理下单日期参数
      if (this.filterForm.reservationStartDate) {
        params.reservation_start_time = `${this.filterForm.reservationStartDate} 00:00:00`;
      }

      if (this.filterForm.reservationEndDate) {
        params.reservation_end_time = `${this.filterForm.reservationEndDate} 23:59:59`;
      }

      // 调用API请求获取数据
      requestApi({
        name: "getReservationOrderReport",
        data: params,
      })
        .then((res) => {
          if (res.code === 200) {
            // 转换响应数据为组件需要的格式
            this.orderRecords = res.data.list.map((item) => {
              // 处理预订时段
              let reservation_period_date = "";
              let reservation_period_time = "";

              if (
                item.reservation_period &&
                item.reservation_period.includes("_")
              ) {
                const periodParts = item.reservation_period.split("_");
                if (periodParts.length === 2) {
                  // 提取日期部分 YYMMDD
                  const datePart = periodParts[0].substring(0, 6);
                  const year = "20" + datePart.substring(0, 2);
                  const month = datePart.substring(2, 4);
                  const day = datePart.substring(4, 6);
                  reservation_period_date = `${year}年${month}月${day}日`;

                  // 提取时间部分 HHmm_HHmm
                  const startTime = periodParts[0].substring(6, 10);
                  const endTime = periodParts[1].substring(6, 10);
                  const startHour = startTime.substring(0, 2);
                  const startMin = startTime.substring(2, 4);
                  const endHour = endTime.substring(0, 2);
                  const endMin = endTime.substring(2, 4);
                  reservation_period_time = `${startHour}点${startMin}分～${endHour}点${endMin}分`;
                }
              }

              return {
                ...item,
                reservation_period_date,
                reservation_period_time,
              };
            });
            this.total = res.data.total;
          } else {
            this.$message.error(res.message || "获取订单记录失败");
            this.orderRecords = [];
            this.total = 0;
          }
          this.listLoading = false;
        })
        .catch((error) => {
          console.error("获取订单记录失败", error);
          this.$message.error("获取订单记录失败");
          this.orderRecords = [];
          this.total = 0;
          this.listLoading = false;
        });
    },

    // 页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getOrderRecords();
    },

    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getOrderRecords();
    },

    // 导出数据
    exportData() {
      // 构建导出请求参数
      const params = {
        phone: this.filterForm.phone || undefined,
      };

      // 处理姓名参数
      if (this.filterForm.realName) {
        params.real_name = this.filterForm.realName;
      }

      // 处理微信昵称参数
      if (this.filterForm.nickName) {
        params.nick_name = this.filterForm.nickName;
      }

      // 处理联系人参数
      if (this.filterForm.contactName) {
        params.contact_name = this.filterForm.contactName;
      }

      // 处理状态参数
      if (this.filterForm.status) {
        params.status = [this.filterForm.status];
      } else {
        params.status = ["PAID_FULL", "CANCELLED", "VERIFIED", "AUTO_VERIFIED"];
      }

      // 处理就餐日期参数
      if (this.filterForm.diningStartDate) {
        params.dining_start_time = `${this.filterForm.diningStartDate} 00:00:00`;
      }

      if (this.filterForm.diningEndDate) {
        params.dining_end_time = `${this.filterForm.diningEndDate} 23:59:59`;
      }

      // 处理下单日期参数
      if (this.filterForm.reservationStartDate) {
        params.reservation_start_time = `${this.filterForm.reservationStartDate} 00:00:00`;
      }

      if (this.filterForm.reservationEndDate) {
        params.reservation_end_time = `${this.filterForm.reservationEndDate} 23:59:59`;
      }

      this.$message({
        message: "导出中，请稍候...",
        type: "info",
      });

      // 清理掉undefined值
      const requestData = {};
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined) {
          requestData[key] = params[key];
        }
      });

      const url = `${process.env.VUE_APP_BASE_API}/report/reservation_order/download_excel`;

      axios({
        url: url,
        method: "POST",
        responseType: "blob",
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "X-XSRF-TOKEN": document.cookie.replace(
            /(?:(?:^|.*;\s*)XSRF-TOKEN\s*\=\s*([^;]*).*$)|^.*$/,
            "$1"
          ),
          "Content-Type": "application/json",
        },
        data: requestData,
      })
        .then((response) => {
          const filename =
            "reservation_order_report_" + new Date().getTime() + ".xlsx";
          const blob = new Blob([response.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          this.$message({
            message: "导出成功",
            type: "success",
          });
        })
        .catch((error) => {
          console.error("导出失败:", error);
          this.$message.error("导出失败，请重试");
        });
    },

    // 查看订单详情
    viewOrderDetail(row) {
      this.currentOrder = row;
      this.orderDetailVisible = true;
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "PENDING":
          return "待支付";
        case "PAID_FULL":
          return "已支付";
        case "VERIFIED":
          return "已就餐";
        case "CANCELLED":
          return "已取消";
        case "AUTO_VERIFIED":
          return "超时自动核销";
        default:
          return status;
      }
    },
  },

  created() {
    this.getOrderRecords();
  },
};
</script>

<style lang="less" scoped>
.order-record {
  background-color: #fff;
  padding: 20px;

  .page-header {
    font-size: 18px;
    color: #303133;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409eff;
    }
  }

  .filter-container {
    margin-bottom: 15px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0px;

      .el-button {
        height: 32px;
        flex: 0 0 auto;
        line-height: normal;
        margin-left: 10px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .action-container {
    margin-bottom: 15px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .status-pending {
    color: #e6a23c;
  }

  .status-paid-full {
    color: #3ea5ea;
  }

  .status-verified {
    color: #67c23a;
  }

  .status-canceled {
    color: #f56c6c;
  }

  .primary-btn {
    color: #409eff;
  }

  /deep/ .el-select {
    width: 150px;
  }

  .order-detail-dialog {
    .detail-content {
      padding: 10px;

      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }

      .order-info {
        margin-bottom: 20px;

        .order-items {
          .order-item {
            display: flex;
            margin-bottom: 10px;

            .item-label {
              width: 100px;
              text-align: right;
              padding-right: 12px;
              color: #606266;
            }

            .item-value {
              flex: 1;
              text-align: left;
            }
          }
        }
      }

      .order-products {
        margin-bottom: 20px;
      }

      .order-total {
        border-top: 1px solid #ebeef5;
        padding-top: 15px;

        .total-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          .total-label {
            color: #606266;
          }

          .total-value {
            font-weight: bold;
            color: #303133;
          }

          &.final-total {
            .total-value {
              color: #f56c6c;
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
