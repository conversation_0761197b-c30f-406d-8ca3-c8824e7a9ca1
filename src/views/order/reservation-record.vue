<template>
  <div class="meal-record">
    <div class="page-header"><i class="el-icon-tickets"></i>自助餐订单记录</div>

    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <div class="form-row">
          <el-form-item label="手机:">
            <el-input
              v-model="filterForm.phone"
              placeholder="输入手机号"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="报餐人:">
            <el-input
              v-model="filterForm.realName"
              placeholder="输入报餐人"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="微信昵称:">
            <el-input
              v-model="filterForm.nickName"
              placeholder="输入微信昵称"
              clearable
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="支付方:">
            <el-select
              v-model="filterForm.paymentEnterprise"
              placeholder="请选择支付方"
              clearable
              filterable
              remote
              :remote-method="searchEnterprises"
              :loading="enterpriseLoading"
              size="mini"
              style="width: 200px"
            >
              <el-option
                v-for="item in enterpriseOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="预定状态:">
            <el-select
              v-model="filterForm.status"
              placeholder="请选择"
              clearable
              sisize="mini"
            >
              <el-option label="已报餐" value="paid_full"></el-option>
              <!--              <el-option label="待支付" value="pending"></el-option>-->
              <el-option label="已就餐" value="verified"></el-option>
              <el-option label="已取消" value="cancelled"></el-option>
              <el-option label="超时自动核销" value="auto_verified"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="就餐日期:">
            <el-date-picker
              v-model="filterForm.diningStartDate"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
            <span style="margin: 0 5px">-</span>
            <el-date-picker
              v-model="filterForm.diningEndDate"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="下单日期:">
            <el-date-picker
              v-model="filterForm.reservationStartDate"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
            <span style="margin: 0 5px">-</span>
            <el-date-picker
              v-model="filterForm.reservationEndDate"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 140px"
              size="mini"
            >
            </el-date-picker>
          </el-form-item>
          <el-button type="primary" @click="handleFilter" size="mini"
            >查询
          </el-button>
          <el-button @click="resetFilter" size="mini">重置</el-button>
        </div>
      </el-form>
    </div>

    <div class="action-container">
      <el-button
        type="success"
        icon="el-icon-download"
        size="mini"
        @click="exportData"
        >导出清单
      </el-button>
    </div>

    <el-table
      :data="mealRecords"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      max-height="600"
    >
      <el-table-column
        prop="id"
        label="序号"
        width="60"
        align="center"
        fixed="left"
      ></el-table-column>
      <!--      <el-table-column-->
      <!--          prop="username"-->
      <!--          label="帐号"-->
      <!--          width="80"-->
      <!--          align="center"-->
      <!--          fixed="left"-->
      <!--      ></el-table-column>-->
      <el-table-column
        prop="phone"
        label="手机"
        width="110"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="real_name"
        label="姓名"
        width="80"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="nick_name"
        label="微信昵称"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="payment_enterprise"
        label="支付方"
        width="250"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="product_name"
        label="产品名称"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_date"
        label="预订日期"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_period_time"
        label="预订时段"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reservation_time"
        label="下单时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="quantity"
        label="数量"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="payable_amount"
        label="费用"
        width="80"
        align="center"
      ></el-table-column>

      <!--      <el-table-column-->
      <!--        prop="updated_at"-->
      <!--        label="核销时间"-->
      <!--        width="150"-->
      <!--        align="center"-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{-->
      <!--            scope.row.status === "VERIFIED" ||-->
      <!--            scope.row.status === "AUTO_VERIFIED"-->
      <!--              ? scope.row.updated_at-->
      <!--              : ""-->
      <!--          }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <!--      <el-table-column-->
      <!--        prop="updated_at"-->
      <!--        label="取消时间"-->
      <!--        width="150"-->
      <!--        align="center"-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{-->
      <!--            scope.row.status === "CANCELLED" ? scope.row.updated_at : ""-->
      <!--          }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <el-table-column prop="status" label="状态" width="90" align="center">
        <template slot-scope="scope">
          <span :class="getMealStatusClass(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="操作" width="180" align="center" fixed="right">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button-->
      <!--            type="text"-->
      <!--            class="primary-btn"-->
      <!--            @click="viewOrderDetail(scope.row)"-->
      <!--          >-->
      <!--            订单详情-->
      <!--          </el-button>-->
      <!--          <el-button-->
      <!--            type="text"-->
      <!--            class="warning-btn"-->
      <!--            @click="cancelMeal(scope.row)"-->
      <!--            v-if="scope.row.mealStatus === 'PENDING'"-->
      <!--          >-->
      <!--            取消订单-->
      <!--          </el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
      title="订单详情"
      :visible.sync="orderDetailVisible"
      width="500px"
      class="order-detail-dialog"
    >
      <div class="detail-content">
        <div class="order-items">
          <div class="order-item">
            <span class="item-label">预订ID</span>
            <span class="item-value">{{
              currentOrder ? currentOrder.reservationId : ""
            }}</span>
          </div>
          <div class="order-item">
            <span class="item-label">预订人</span>
            <span class="item-value">{{
              currentOrder ? currentOrder.person : ""
            }}</span>
          </div>
          <div class="order-item">
            <span class="item-label">预订产品</span>
            <span class="item-value">{{
              currentOrder ? currentOrder.orderType : ""
            }}</span>
          </div>
          <div class="order-item">
            <span class="item-label">预订数量</span>
            <span class="item-value"
              >{{ currentOrder ? currentOrder.people : "" }} 份</span
            >
          </div>
          <div class="order-item">
            <span class="item-label">预订时间</span>
            <span class="item-value">{{
              currentOrder ? currentOrder.mealTime : ""
            }}</span>
          </div>
          <div class="order-item">
            <span class="item-label">预订时段</span>
            <span class="item-value">{{
              currentOrder ? currentOrder.mealPeriod : ""
            }}</span>
          </div>
          <div class="order-item">
            <span class="item-label">预订状态</span>
            <span class="item-value">{{
              currentOrder ? getStatusText(currentOrder.mealStatus) : ""
            }}</span>
          </div>
        </div>

        <div class="order-total">
          <span class="total-label">费用:</span>
          <span class="total-value"
            >{{ currentOrder ? currentOrder.amount : 0 }}元</span
          >
        </div>

        <div class="order-discount">
          <div class="discount-header">
            <span>减免详情</span>
          </div>
          <div class="discount-item">
            <span class="item-label">商家抵付</span>
            <span class="item-value discount-value">-200 元</span>
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>

        <div class="order-total final-total">
          <span class="total-label">订单总价:</span>
          <span class="total-value">229元</span>
        </div>

        <div class="actual-payment">
          <span class="payment-title">实付金额</span>
          <span class="payment-amount">¥ 229.00</span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailVisible = false">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import axios from "axios";
import { getToken } from "@/utils/auth";
import enterpriseApi from "@/api/enterprise";

export default {
  name: "ReservationRecord",
  data() {
    return {
      title: "预订就餐记录",

      // 筛选表单
      filterForm: {
        phone: "",
        realName: "",
        nickName: "",
        paymentEnterprise: "",
        status: "",
        diningStartDate: "",
        diningEndDate: "",
        reservationStartDate: "",
        reservationEndDate: "",
      },

      // 表格加载状态
      listLoading: false,

      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 14,

      // 对话框
      orderDetailVisible: false,
      currentOrder: null,

      // 报餐记录数据
      mealRecords: [],

      // 企业选项相关
      enterpriseOptions: [],
      enterpriseLoading: false,
    };
  },

  methods: {
    // 搜索企业
    searchEnterprises(query) {
      if (query !== "") {
        this.enterpriseLoading = true;
        requestApi({
          name: "getEnterpriseNameListByKeyword",
          data: { keyword: query },
        })
          .then((res) => {
            this.enterpriseLoading = false;
            if (res.code === 200) {
              this.enterpriseOptions = res.data.map((item) => ({
                value: item.company_name,
                label: item.company_name,
              }));
            } else {
              this.enterpriseOptions = [];
            }
          })
          .catch((error) => {
            this.enterpriseLoading = false;
            this.enterpriseOptions = [];
            console.error("搜索企业失败:", error);
          });
      } else {
        this.enterpriseOptions = [];
      }
    },

    // 获取就餐状态样式类
    getMealStatusClass(status) {
      if (status === "PENDING") {
        return "status-pending";
      } else if (status === "PAID_DEPOSIT") {
        return "status-paid-deposit";
      } else if (status === "PAID_FULL") {
        return "status-paid-full";
      } else if (status === "CANCELLED") {
        return "status-canceled";
      } else if (status === "VERIFIED") {
        return "status-verified";
      } else if (status === "AUTO_VERIFIED") {
        return "status-verified";
      }
      return "";
    },

    // 处理筛选
    handleFilter() {
      this.currentPage = 1;
      this.getMealRecords();
    },

    // 重置筛选
    resetFilter() {
      this.filterForm = {
        phone: "",
        realName: "",
        nickName: "",
        paymentEnterprise: "",
        status: "",
        diningStartDate: "",
        diningEndDate: "",
        reservationStartDate: "",
        reservationEndDate: "",
      };
      this.handleFilter();
    },

    // 获取报餐记录
    getMealRecords() {
      this.listLoading = true;

      // 构建请求参数
      const params = {
        phone: this.filterForm.phone || undefined,
        page: this.currentPage,
        page_size: this.pageSize,
      };

      // 处理姓名参数
      if (this.filterForm.realName) {
        params.real_name = this.filterForm.realName;
      }

      // 处理姓名参数
      if (this.filterForm.nickName) {
        params.nick_name = this.filterForm.nickName;
      }

      // 处理支付方参数
      if (this.filterForm.paymentEnterprise) {
        params.payment_enterprise = this.filterForm.paymentEnterprise;
      }

      // 处理状态参数
      if (this.filterForm.status) {
        params.status = [this.filterForm.status];
      } else {
        params.status = ["PAID_FULL", "CANCELLED", "VERIFIED", "AUTO_VERIFIED"];
      }

      // 处理日期参数
      if (this.filterForm.diningStartDate) {
        params.dining_start_time = `${this.filterForm.diningStartDate} 00:00:00`;
      }

      if (this.filterForm.diningEndDate) {
        params.dining_end_time = `${this.filterForm.diningEndDate} 23:59:59`;
      }

      // 处理日期参数
      if (this.filterForm.reservationStartDate) {
        params.reservation_start_time = `${this.filterForm.reservationStartDate} 00:00:00`;
      }

      if (this.filterForm.reservationEndDate) {
        params.reservation_end_time = `${this.filterForm.reservationEndDate} 23:59:59`;
      }

      // 调用API请求获取数据
      requestApi({
        name: "getReservationReport",
        data: params,
      })
        .then((res) => {
          if (res.code === 200) {
            // 转换响应数据为组件需要的格式
            this.mealRecords = res.data.list.map((item) => {
              // 处理预订时段
              let reservation_period_date = "";
              let reservation_period_time = "";

              if (
                item.reservation_period &&
                item.reservation_period.includes("_")
              ) {
                const periodParts = item.reservation_period.split("_");
                if (periodParts.length === 2) {
                  // 提取日期部分 YYMMDD
                  const datePart = periodParts[0].substring(0, 6);
                  const year = "20" + datePart.substring(0, 2);
                  const month = datePart.substring(2, 4);
                  const day = datePart.substring(4, 6);
                  reservation_period_date = `${year}年${month}月${day}日`;

                  // 提取时间部分 HHmm_HHmm
                  const startTime = periodParts[0].substring(6, 10);
                  const endTime = periodParts[1].substring(6, 10);
                  const startHour = startTime.substring(0, 2);
                  const startMin = startTime.substring(2, 4);
                  const endHour = endTime.substring(0, 2);
                  const endMin = endTime.substring(2, 4);
                  reservation_period_time = `${startHour}点${startMin}分～${endHour}点${endMin}分`;
                }
              }

              return {
                ...item,
                reservation_period_date,
                reservation_period_time,
              };
            });
            this.total = res.data.total;
          } else {
            this.$message.error(res.message || "获取报餐就餐记录失败111");
            this.mealRecords = [];
            this.total = 0;
          }
          this.listLoading = false;
        })
        .catch((error) => {
          console.error("获取报餐就餐记录失败", error);
          this.$message.error("获取报餐就餐记录失败2222");
          this.mealRecords = [];
          this.total = 0;
          this.listLoading = false;
        });
    },

    // 页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMealRecords();
    },

    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMealRecords();
    },

    // 导出数据
    exportData() {
      // 构建导出请求参数
      const params = {
        phone: this.filterForm.phone || undefined,
      };

      // 处理姓名参数
      if (this.filterForm.realName) {
        params.real_name = this.filterForm.realName;
      }

      // 处理微信昵称参数
      if (this.filterForm.nickName) {
        params.nick_name = this.filterForm.nickName;
      }

      // 处理支付方参数
      if (this.filterForm.paymentEnterprise) {
        params.payment_enterprise = this.filterForm.paymentEnterprise;
      }

      // 处理状态参数
      console.log("filterForm 状态参数：", this.filterForm);
      if (this.filterForm.status) {
        params.status = [this.filterForm.status];
      } else {
        params.status = ["PAID_FULL", "CANCELLED", "VERIFIED", "AUTO_VERIFIED"];
      }
      console.log("param 状态：", params.status);

      // 处理就餐日期参数
      if (this.filterForm.diningStartDate) {
        params.dining_start_time = `${this.filterForm.diningStartDate} 00:00:00`;
      }

      if (this.filterForm.diningEndDate) {
        params.dining_end_time = `${this.filterForm.diningEndDate} 23:59:59`;
      }

      // 处理下单日期参数
      if (this.filterForm.reservationStartDate) {
        params.reservation_start_time = `${this.filterForm.reservationStartDate} 00:00:00`;
      }

      if (this.filterForm.reservationEndDate) {
        params.reservation_end_time = `${this.filterForm.reservationEndDate} 23:59:59`;
      }

      this.$message({
        message: "导出中，请稍候...",
        type: "info",
      });

      // 清理掉undefined值
      const requestData = {};
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined) {
          requestData[key] = params[key];
        }
      });

      const url = `${process.env.VUE_APP_BASE_API}/report/reservation/download_excel`;

      axios({
        url: url,
        method: "POST",
        responseType: "blob",
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "X-XSRF-TOKEN": document.cookie.replace(
            /(?:(?:^|.*;\s*)XSRF-TOKEN\s*\=\s*([^;]*).*$)|^.*$/,
            "$1"
          ),
          "Content-Type": "application/json",
        },
        data: requestData,
      })
        .then((response) => {
          const filename =
            "reservation_report_" + new Date().getTime() + ".xlsx";
          const blob = new Blob([response.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          this.$message({
            message: "导出成功",
            type: "success",
          });
        })
        .catch((error) => {
          console.error("导出失败:", error);
          this.$message.error("导出失败，请重试");
        });
    },

    // 查看订单详情
    viewOrderDetail(row) {
      this.currentOrder = row;
      this.orderDetailVisible = true;
    },

    // 取消订单
    cancelMeal(row) {
      if (row.mealStatus === "COMPLETED" || row.mealStatus === "CANCELED") {
        this.$message({
          message: `该订单已${
            row.mealStatus === "COMPLETED" ? "就餐" : "取消"
          }，不能取消`,
          type: "warning",
        });
        return;
      }

      this.$confirm("确定要取消该订单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用取消预订API
          requestApi({
            name: "cancelReservation",
            data: {
              reservation_id: row.reservationId,
            },
          })
            .then((res) => {
              if (res.code === 200) {
                // 更新本地状态
                row.mealStatus = "CANCELED";
                row.mealActualTime = new Date()
                  .toISOString()
                  .replace("T", " ")
                  .substring(0, 19);

                this.$message({
                  type: "success",
                  message: "订单已取消",
                });
              } else {
                this.$message.error(res.message || "取消订单失败");
              }
            })
            .catch((error) => {
              console.error("取消订单失败", error);
              this.$message.error("取消订单失败");
            });
        })
        .catch(() => {});
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "PENDING":
          return "待支付";
        case "PAID_DEPOSIT":
          return "已付定金";
        case "PAID_FULL":
          return "已预定";
        case "VERIFIED":
          return "已就餐";
        case "CANCELLED":
          return "已取消";
        case "AUTO_VERIFIED":
          return "超时自动核销";
        default:
          return status;
      }
    },
  },

  created() {
    this.getMealRecords();
  },
};
</script>

<style lang="less" scoped>
.meal-record {
  background-color: #fff;
  padding: 20px;

  .page-header {
    font-size: 18px;
    color: #303133;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409eff;
    }
  }

  .filter-container {
    margin-bottom: 15px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0px;

      .el-button {
        height: 32px;
        flex: 0 0 auto;
        line-height: normal;
        margin-left: 10px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .action-container {
    margin-bottom: 15px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .status-paid-full {
    color: #3ea5ea;
  }

  .status-verified {
    color: #67c23a;
  }

  .status-not-eaten {
    color: #f56c6c;
  }

  .status-canceled {
    color: #e6a23c;
  }

  .status-rejected {
    color: #f56c6c;
  }

  .status-timeout {
    color: #909399;
  }

  .primary-btn {
    color: #409eff;
  }

  .warning-btn {
    color: #e6a23c;
    margin-left: 5px;
  }

  /deep/ .el-select {
    width: 150px;
  }

  .order-detail-dialog {
    .detail-content {
      padding: 10px;

      .order-items {
        .order-item {
          display: flex;
          margin-bottom: 10px;

          .item-label {
            width: 100px;
            text-align: right;
            padding-right: 12px;
            color: #606266;
          }

          .item-value {
            flex: 1;
            text-align: left;
          }
        }
      }

      .order-total {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-top: 1px solid #ebeef5;
        margin-top: 10px;

        .total-label {
          color: #606266;
        }

        .total-value {
          font-weight: bold;
          color: #f56c6c;
        }
      }
    }
  }
}
</style>
