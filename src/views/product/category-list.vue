<template>
  <div class="category-list">
    <h1>产品分类</h1>

    <!-- 头部搜索和操作区域 -->
    <div class="search-bar">
      <!--
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.name"
            placeholder="分类名称"
            size="mini"
            class="filter-item"
          />
        </div>
        <el-button type="primary" size="mini" @click="handleSearch">
          查询
        </el-button>
        <el-button size="mini" @click="resetSearch">重置</el-button>
      </div>
      -->
      <div class="left-buttons">
        <el-button
          type="primary"
          size="mini"
          @click="handleAdd"
          icon="el-icon-plus"
        >
          新增分类
        </el-button>
      </div>
    </div>
    <!-- 头部搜索区域结束 -->

    <!-- 分类树 -->
    <div class="tree-container">
      <el-tree
        v-loading="treeLoading"
        :data="treeData"
        :props="defaultProps"
        :default-expand-all="true"
        :expand-on-click-node="false"
        node-key="id"
        class="category-tree"
      >
        <div class="tree-node" slot-scope="{ node, data }">
          <div class="node-content">
            <div class="node-info">
              <img
                v-if="data.image && data.image !== 'string'"
                :src="data.image"
                class="category-image"
                alt="分类图片"
              />
              <div class="category-icon" v-else>
                <i class="el-icon-folder"></i>
              </div>
              <div class="node-details">
                <div class="node-title">{{ data.name }}</div>
                <div class="node-description" v-if="data.description">
                  {{ data.description }}
                </div>
                <div class="node-meta">
                  <el-tag
                    :type="data.status === 1 ? 'success' : 'danger'"
                    size="mini"
                  >
                    {{ data.status === 1 ? "启用" : "禁用" }}
                  </el-tag>
                  <span class="sort-order">排序: {{ data.sort_order }}</span>
                  <span class="created-time">{{ data.created_at }}</span>
                </div>
              </div>
            </div>
            <div class="node-actions">
              <el-button
                type="text"
                size="mini"
                @click="handleAddChild(data)"
                icon="el-icon-plus"
              >
                添加子分类
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="handleEdit(data)"
                icon="el-icon-edit"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="mini"
                :class="
                  data.status === 1
                    ? 'el-button-text-red'
                    : 'el-button-text-green'
                "
                @click="toggleStatus(data)"
              >
                {{ data.status === 1 ? "禁用" : "启用" }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                class="el-button-text-red"
                @click="handleDelete(data)"
                icon="el-icon-delete"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </el-tree>
    </div>
    <!-- 分类树结束 -->

    <!-- 新增/编辑分类弹窗 -->
    <category-detail
      :dialog-visible="dialogFormVisible"
      :edit-data="currentCategory"
      @close="handleDialogClose"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import CategoryDetail from "./category-detail.vue";

export default {
  name: "CategoryList",
  components: {
    CategoryDetail,
  },
  data() {
    return {
      treeLoading: false,
      treeData: [],
      searchForm: {
        name: "",
      },
      defaultProps: {
        children: "children",
        label: "name",
      },
      dialogFormVisible: false,
      currentCategory: null,
    };
  },
  mounted() {
    console.log("CategoryList组件已加载");
    this.getCategoryTree();
  },
  methods: {
    // 获取分类树数据
    getCategoryTree() {
      this.treeLoading = true;

      const params = {};
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      requestApi({
        name: "getCategoryTree",
        data: params,
      })
        .then((response) => {
          this.treeLoading = false;
          console.log("分类树API响应:", response);
          if (response && response.code === 200) {
            this.treeData = response.data || [];
          } else {
            this.$message.error(response.message || "获取分类树失败");
          }
        })
        .catch((error) => {
          this.treeLoading = false;
          console.error("获取分类树失败", error);
          this.$message.error("获取分类树失败");
        });
    },

    // // 搜索功能
    // handleSearch() {
    //   this.getCategoryTree();
    // },

    // // 重置搜索
    // resetSearch() {
    //   this.searchForm = {
    //     name: "",
    //   };
    //   this.getCategoryTree();
    // },

    // 新增分类
    handleAdd() {
      this.currentCategory = {
        parent_id: null,
      };
      this.dialogFormVisible = true;
    },

    // 添加子分类
    handleAddChild(data) {
      this.currentCategory = {
        parent_id: data.id,
      };
      this.dialogFormVisible = true;
    },

    // 编辑分类
    handleEdit(data) {
      this.currentCategory = { ...data };
      this.dialogFormVisible = true;
    },

    // 删除分类
    handleDelete(data) {
      this.$confirm(
        `确定要删除分类"${data.name}"吗？删除后将无法恢复！`,
        "删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          requestApi({
            name: "deleteCategory",
            data: { id: data.id },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除分类成功");
                this.getCategoryTree();
              } else {
                this.$message.error(response.message || "删除分类失败");
              }
            })
            .catch((error) => {
              console.error("删除分类失败", error);
              this.$message.error("删除分类失败");
            });
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 切换分类状态
    toggleStatus(data) {
      const action = data.status === 1 ? "禁用" : "启用";
      this.$confirm(`确定要${action}分类"${data.name}"吗？`, `${action}确认`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "updateCategoryStatus",
            data: {
              id: data.id,
              status: data.status === 1 ? 0 : 1,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success(`${action}分类成功`);
                this.getCategoryTree();
              } else {
                this.$message.error(response.message || `${action}分类失败`);
              }
            })
            .catch((error) => {
              console.error(`${action}分类失败`, error);
              this.$message.error(`${action}分类失败`);
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    },

    // 处理弹窗关闭
    handleDialogClose() {
      this.dialogFormVisible = false;
      this.currentCategory = null;
    },

    // 处理弹窗成功
    handleDialogSuccess() {
      this.dialogFormVisible = false;
      this.currentCategory = null;
      this.getCategoryTree();
    },
  },
};
</script>

<style lang="less" scoped>
.category-list {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 84px);

  h1 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 24px;
    font-weight: bold;
  }
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;

  .search-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .input-group {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .filter-item {
      width: 200px;
    }
  }

  .left-buttons {
    display: flex;
    gap: 10px;
  }
}

.tree-container {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
  padding: 20px;
}

.category-tree {
  /deep/ .el-tree-node__content {
    height: auto;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  /deep/ .el-tree-node__expand-icon {
    color: #409eff;
    font-size: 16px;
  }

  /deep/ .el-tree-node__label {
    width: 100%;
  }
}

.tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
}

.node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.category-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #e6e6e6;
}

.category-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 4px;
  color: #999;

  i {
    font-size: 20px;
  }
}

.node-details {
  flex: 1;
}

.node-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.node-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
}

.node-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #999;

  .sort-order {
    color: #666;
  }

  .created-time {
    color: #999;
  }
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 15px;
}
</style>
