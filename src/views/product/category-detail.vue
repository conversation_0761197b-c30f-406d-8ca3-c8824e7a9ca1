<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" />
      </el-form-item>

      <el-form-item label="分类描述" prop="description">
        <el-input
          type="textarea"
          v-model="formData.description"
          placeholder="请输入分类描述"
          :rows="3"
        />
      </el-form-item>

      <el-form-item label="分类键" prop="key">
        <el-input v-model="formData.key" placeholder="请输入分类键名称" />
      </el-form-item>

      <el-form-item label="父级分类" prop="parent_id">
        <el-select
          v-model="formData.parent_id"
          placeholder="请选择父级分类（不选择则为顶级分类）"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in parentCategories"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :disabled="item.id === formData.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分类图片" prop="image">
        <div class="upload-box" style="width: 120px; height: 120px">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="handleUpload"
          >
            <img
              v-if="formData.image && formData.image !== 'string'"
              :src="formData.image"
              class="upload-img"
              alt="分类图片"
            />
            <div v-else class="upload-box-add">
              <i class="el-icon-plus"></i>
              <div>上传图片</div>
            </div>
          </el-upload>
        </div>
      </el-form-item>

      <el-form-item label="排序序号" prop="sort_order">
        <el-input-number
          v-model="formData.sort_order"
          :min="0"
          :step="1"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "CategoryDetail",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      submitLoading: false,
      parentCategories: [],
      formData: {
        id: undefined,
        name: "",
        description: "",
        image: "",
        sort_order: 0,
        parent_id: null,
        status: 1,
        key: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入分类名称", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "分类名称长度在 2 到 50 个字符",
            trigger: "blur",
          },
        ],
        description: [
          { max: 200, message: "描述长度不能超过 200 个字符", trigger: "blur" },
        ],
        sort_order: [
          { type: "number", message: "排序序号必须为数字", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    dialogTitle() {
      return this.formData.id ? "编辑分类" : "新增分类";
    },
  },
  watch: {
    dialogVisible: {
      handler(newVal) {
        if (newVal) {
          this.initForm();
          this.getParentCategories();
        } else {
          this.resetForm();
        }
      },
      immediate: true,
    },
    editData: {
      handler(newVal) {
        if (newVal && this.dialogVisible) {
          this.initForm();
        }
      },
      deep: true,
    },
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.editData) {
        // 编辑模式
        this.formData = {
          id: this.editData.id,
          name: this.editData.name || "",
          description: this.editData.description || "",
          image: this.editData.image || "",
          sort_order: this.editData.sort_order || 0,
          parent_id: this.editData.parent_id || null,
          status: this.editData.status !== undefined ? this.editData.status : 1,
          key: this.editData.key || "",
        };
      } else {
        // 新增模式
        this.formData = {
          id: undefined,
          name: "",
          description: "",
          image: "",
          sort_order: 0,
          parent_id: null,
          status: 1,
          key: "",
        };
      }
    },

    // 重置表单
    resetForm() {
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields();
      }
      this.formData = {
        id: undefined,
        name: "",
        description: "",
        image: "",
        sort_order: 0,
        parent_id: null,
        status: 1,
        key: "",
      };
    },

    // 获取父级分类列表
    getParentCategories() {
      requestApi({
        name: "getCategoryTree",
        data: {},
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.parentCategories = this.flattenCategories(response.data || []);
          }
        })
        .catch((error) => {
          console.error("获取父级分类失败", error);
        });
    },

    // 扁平化分类数据（用于父级分类选择）
    flattenCategories(categories, level = 0) {
      let result = [];
      categories.forEach((category) => {
        const prefix = "　".repeat(level);
        result.push({
          id: category.id,
          name: prefix + category.name,
        });
        if (category.children && category.children.length > 0) {
          result = result.concat(
            this.flattenCategories(category.children, level + 1)
          );
        }
      });
      return result;
    },

    // 图片上传前验证
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/PNG 格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return false;
      }
      return true;
    },

    // 处理图片上传（这里需要根据实际项目的上传接口进行调整）
    handleUpload(options) {
      // 这里应该调用实际的图片上传接口
      // 暂时使用本地预览
      const file = options.file;
      const reader = new FileReader();
      reader.onload = (e) => {
        this.formData.image = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;

          const apiName = this.formData.id ? "updateCategory" : "addCategory";
          const requestData = { ...this.formData };

          // 如果是编辑模式，需要传递id参数
          if (this.formData.id) {
            requestData.id = this.formData.id;
          }

          requestApi({
            name: apiName,
            data: requestData,
          })
            .then((response) => {
              this.submitLoading = false;
              if (response && response.code === 200) {
                this.$message.success(
                  this.formData.id ? "编辑分类成功" : "新增分类成功"
                );
                this.$emit("success");
              } else {
                this.$message.error(
                  response.message ||
                    (this.formData.id ? "编辑分类失败" : "新增分类失败")
                );
              }
            })
            .catch((error) => {
              this.submitLoading = false;
              console.error("提交分类失败", error);
              this.$message.error(
                this.formData.id ? "编辑分类失败" : "新增分类失败"
              );
            });
        } else {
          this.$message.error("请检查表单输入");
          return false;
        }
      });
    },

    // 取消操作
    handleCancel() {
      this.$emit("close");
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.dialog-footer {
  text-align: right;
}

.upload-box {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: #409eff;
  }

  .upload-box-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c939d;
    font-size: 12px;

    .el-icon-plus {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }

  .upload-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.avatar-uploader {
  width: 100%;
  height: 100%;

  /deep/ .el-upload {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
