<template>
  <div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      @close="handleClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="产品类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择产品类型"
            :disabled="formData.id !== undefined"
          >
            <el-option label="普通产品" value="direct_sale" />
            <el-option label="预订产品" value="reservation" />
            <el-option label="虚拟产品" value="virtual" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品分类" prop="meal_type">
          <el-select v-model="formData.meal_type" placeholder="请选择分类">
            <el-option label="商务餐" value="business" />
            <el-option label="自助餐" value="buffet" />
            <el-option label="优惠券" value="coupon" />
          </el-select>
        </el-form-item>
        <el-form-item label="目录分类">
          <div class="category-selection">
            <div
              class="selected-categories"
              v-if="selectedCategoriesDisplay.length > 0"
            >
              <el-tag
                v-for="category in selectedCategoriesDisplay"
                :key="category.id"
                closable
                @close="removeCategory(category.id)"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ category.name }}
              </el-tag>
            </div>
            <div v-else class="no-category-selected">
              <span style="color: #909399">未选择目录分类</span>
            </div>
            <el-button
              type="primary"
              size="small"
              @click="showCategoryDialog"
              style="margin-top: 8px"
            >
              选择分类
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="产品图片" prop="imageUrl">
          <div class="upload-container">
            <el-upload
              class="avatar-uploader"
              action="/upload"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :before-upload="beforeUploadFile"
              :http-request="customUploadRequest"
            >
              <img
                v-if="formData.imageUrl"
                :src="formData.imageUrl"
                class="avatar"
                alt="产品图片"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="产品价格" prop="price">
          <el-input-number
            v-model="formData.price"
            :precision="2"
            :min="0"
            :step="0.5"
          />
        </el-form-item>
        <!--        <template v-if="formData.type === 'reservation'">-->
        <!--          <el-form-item label="预订费用" prop="reservation_fee">-->
        <!--            <el-input-number-->
        <!--              v-model="formData.reservation_fee"-->
        <!--              :precision="2"-->
        <!--              :min="0"-->
        <!--              :step="0.5"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="最大预订数量" prop="max_reservations">-->
        <!--            <div style="display: flex; align-items: center">-->
        <!--              <el-input-number-->
        <!--                v-model="formData.max_reservations"-->
        <!--                :min="-1"-->
        <!--                :precision="0"-->
        <!--                :step="1"-->
        <!--              />-->
        <!--              <span class="input-tip" style="color: #ff0000; font-size: 12px">-->
        <!--                输入-1表示不限制最大预订数量-->
        <!--              </span>-->
        <!--            </div>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="预订截止时间">-->
        <!--            <el-date-picker-->
        <!--              v-model="formData.reservation_deadline"-->
        <!--              type="datetime"-->
        <!--              placeholder="选择预订截止时间"-->
        <!--              value-format="yyyy-MM-dd HH:mm:ss"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="取消截止时间">-->
        <!--            <el-date-picker-->
        <!--              v-model="formData.cancellation_deadline"-->
        <!--              type="datetime"-->
        <!--              placeholder="选择取消截止时间"-->
        <!--              value-format="yyyy-MM-dd HH:mm:ss"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="是否需要审批" prop="is_approval_required">-->
        <!--            <el-switch-->
        <!--              v-model="formData.is_approval_required"-->
        <!--              active-text="是"-->
        <!--              inactive-text="否"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </template>-->
        <el-form-item label="库存数量" prop="stock">
          <div style="display: flex; align-items: center">
            <el-input-number
              v-model="formData.stock"
              :min="-1"
              :precision="0"
              :step="1"
            />
            <span class="input-tip" style="color: #ff0000; font-size: 12px">
              输入-1表示不限制库存
            </span>
          </div>
        </el-form-item>
        <el-form-item label="上架状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">上架</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 虚拟产品特有字段 -->
        <template v-if="formData.type === 'virtual'">
          <el-form-item label="关联对象类型" prop="object_type">
            <el-select
              v-model="formData.object_type"
              placeholder="请选择关联对象类型"
            >
              <el-option label="优惠券批次" value="coupon_batch" />
            </el-select>
          </el-form-item>
          <el-form-item label="关联对象" prop="object_id">
            <div class="coupon-batch-selector">
              <div v-if="selectedCouponBatch" class="selected-coupon-batch">
                <el-tag closable @close="clearCouponBatch">
                  {{ selectedCouponBatch.name }} (ID:
                  {{ selectedCouponBatch.id }})
                </el-tag>
              </div>
              <div v-else class="no-coupon-batch-selected">
                <span style="color: #909399">未选择优惠券批次</span>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="showCouponBatchDialog"
                style="margin-top: 8px"
              >
                选择优惠券批次
              </el-button>
            </div>
          </el-form-item>
        </template>

        <el-form-item label="产品描述">
          <el-input
            type="textarea"
            v-model="formData.description"
            rows="4"
            placeholder="请输入产品描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 目录分类选择对话框 -->
    <el-dialog
      title="选择目录分类"
      :visible.sync="categoryDialogVisible"
      width="500px"
      @close="handleCategoryDialogClose"
    >
      <div class="category-tree-container">
        <el-tree
          ref="categoryTree"
          :data="categoryTreeData"
          :props="categoryTreeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="[]"
          :default-expand-all="true"
          @check="handleCategoryCheck"
        >
          <div class="tree-node" slot-scope="{ node, data }">
            <span class="tree-node-label">{{ data.name }}</span>
          </div>
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCategoryDialogClose">取消</el-button>
        <el-button type="primary" @click="handleCategoryConfirm"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 优惠券批次选择对话框 -->
    <el-dialog
      title="选择优惠券批次"
      :visible.sync="couponBatchDialogVisible"
      width="600px"
      @close="handleCouponBatchDialogClose"
    >
      <div class="coupon-batch-search">
        <el-input
          v-model="couponBatchSearchKeyword"
          placeholder="请输入优惠券批次名称进行搜索"
          @input="searchCouponBatches"
          clearable
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="searchCouponBatches"
          ></el-button>
        </el-input>
      </div>
      <div class="coupon-batch-list-container">
        <el-table
          :data="couponBatchList"
          v-loading="couponBatchLoading"
          @row-click="selectCouponBatch"
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column
            prop="name"
            label="批次名称"
            min-width="200"
          ></el-table-column>
          <el-table-column
            prop="description"
            label="描述"
            min-width="150"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="total_count"
            label="总数量"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="used_count"
            label="已使用"
            width="100"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.status === 'active' ? 'success' : 'info'"
              >
                {{ scope.row.status === "active" ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div
          v-if="couponBatchList.length === 0 && !couponBatchLoading"
          class="no-data"
        >
          <span style="color: #909399">暂无数据</span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCouponBatchDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleCouponBatchConfirm"
          :disabled="!selectedCouponBatchRow"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "ProductDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      categoryDialogVisible: false,
      categoryTreeData: [],
      categoryTreeProps: {
        children: "children",
        label: "name",
      },
      selectedCategoriesDisplay: [],
      // 优惠券批次选择相关
      couponBatchDialogVisible: false,
      couponBatchList: [],
      couponBatchLoading: false,
      couponBatchSearchKeyword: "",
      selectedCouponBatchRow: null,
      selectedCouponBatch: null,
      formData: {
        id: undefined,
        name: "",
        imageUrl: "",
        imageFile: null,
        type: "direct_sale",
        category_ids: [],
        price: 0,
        stock: 0,
        status: 1,
        description: "",
        reservation_fee: 0,
        max_reservations: 1,
        reservation_deadline: null,
        cancellation_deadline: null,
        is_approval_required: false,
        meal_type: "business",
        // 虚拟产品字段
        object_id: null,
        object_type: null,
      },
      rules: {
        name: [{ required: true, message: "请输入产品名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择产品类型", trigger: "change" },
        ],
        price: [{ required: true, message: "请输入产品价格", trigger: "blur" }],
        stock: [{ required: true, message: "请输入库存数量", trigger: "blur" }],
        reservation_fee: [
          { required: true, message: "请输入预订费用", trigger: "blur" },
        ],
        max_reservations: [
          { required: true, message: "请输入最大预订数量", trigger: "blur" },
        ],
        meal_type: [
          { required: true, message: "请选择餐型", trigger: "change" },
        ],
        // 虚拟产品字段验证
        object_type: [
          {
            required: true,
            message: "请选择关联对象类型",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.formData.type === "virtual" && !value) {
                callback(new Error("虚拟产品必须选择关联对象类型"));
              } else {
                callback();
              }
            },
          },
        ],
        object_id: [
          {
            required: true,
            message: "请选择关联对象",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.formData.type === "virtual" && !value) {
                callback(new Error("虚拟产品必须选择关联对象"));
              } else {
                callback();
              }
            },
          },
        ],
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
    dialogTitle() {
      return this.editData ? "编辑产品" : "新增产品";
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData();
        this.getCategoryOptions();
      }
    },
    editData: {
      handler(val) {
        if (val && this.visible) {
          this.loadEditData(val);
        }
      },
      deep: true,
    },
    categoryTreeData: {
      handler(newData) {
        if (
          newData &&
          newData.length > 0 &&
          this.formData.category_ids &&
          this.formData.category_ids.length > 0
        ) {
          this.$nextTick(() => {
            // 更新选中分类的显示
            this.updateSelectedCategoriesDisplay();
            // 设置树组件的选中状态
            if (this.$refs.categoryTree) {
              this.$refs.categoryTree.setCheckedKeys(
                this.formData.category_ids
              );
            }
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    // 获取分类选项
    getCategoryOptions() {
      requestApi({
        name: "getCategoryTree",
        data: {},
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.categoryTreeData = response.data || [];
          } else {
            this.$message.error(response.message || "获取分类列表失败");
          }
        })
        .catch((error) => {
          console.error("获取分类列表失败", error);
          this.$message.error("获取分类列表失败");
        });
    },

    // 显示分类选择对话框
    showCategoryDialog() {
      this.categoryDialogVisible = true;
      // 延迟设置选中状态，确保对话框完全打开和树组件渲染完成
      this.$nextTick(() => {
        // 等待对话框和树组件完全渲染
        setTimeout(() => {
          if (
            this.$refs.categoryTree &&
            this.formData.category_ids.length > 0
          ) {
            this.$refs.categoryTree.setCheckedKeys(this.formData.category_ids);
          }
        }, 100);
      });
    },

    // 处理分类选择对话框关闭
    handleCategoryDialogClose() {
      this.categoryDialogVisible = false;
      // 恢复树组件的选中状态为当前已选择的分类
      if (this.$refs.categoryTree) {
        this.$refs.categoryTree.setCheckedKeys(this.formData.category_ids);
      }
    },

    // 处理分类选择
    handleCategoryCheck(data, checked) {
      // 此方法在树节点选中状态改变时触发
      // 实际的处理在确认时进行
    },

    // 确认分类选择
    handleCategoryConfirm() {
      const checkedNodes = this.$refs.categoryTree.getCheckedNodes();
      const checkedKeys = this.$refs.categoryTree.getCheckedKeys();

      // 同时获取选中的节点和keys，确保数据一致性
      this.formData.category_ids = checkedKeys;
      this.updateSelectedCategoriesDisplay();
      this.categoryDialogVisible = false;
    },

    // 更新选中分类的显示
    updateSelectedCategoriesDisplay() {
      if (
        !this.formData.category_ids ||
        this.formData.category_ids.length === 0
      ) {
        this.selectedCategoriesDisplay = [];
        return;
      }

      // 如果分类树数据还没有加载完成，暂时不更新显示
      if (!this.categoryTreeData || this.categoryTreeData.length === 0) {
        return;
      }

      // 从树形数据中找到对应的分类
      const findCategoryById = (categories, id) => {
        for (let category of categories) {
          if (category.id === id) {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const found = findCategoryById(category.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      this.selectedCategoriesDisplay = this.formData.category_ids
        .map((id) => findCategoryById(this.categoryTreeData, id))
        .filter((category) => category !== null);
    },

    // 移除单个分类
    removeCategory(categoryId) {
      this.formData.category_ids = this.formData.category_ids.filter(
        (id) => id !== categoryId
      );
      this.updateSelectedCategoriesDisplay();

      // 更新树形控件的选中状态
      if (this.$refs.categoryTree) {
        this.$refs.categoryTree.setCheckedKeys(this.formData.category_ids);
      }
    },

    // 显示优惠券批次选择对话框
    showCouponBatchDialog() {
      this.couponBatchDialogVisible = true;
      this.couponBatchSearchKeyword = "";
      this.selectedCouponBatchRow = null;
      this.searchCouponBatches();
    },

    // 搜索优惠券批次
    searchCouponBatches() {
      this.couponBatchLoading = true;
      requestApi({
        name: "getCouponBatchList",
        data: {
          keyword: this.couponBatchSearchKeyword,
          page: 1,
          pageSize: 50,
        },
      })
        .then((response) => {
          this.couponBatchLoading = false;
          if (response && response.code === 200) {
            this.couponBatchList = response.data.list || [];
          } else {
            this.$message.error(response.message || "获取优惠券批次列表失败");
            this.couponBatchList = [];
          }
        })
        .catch((error) => {
          this.couponBatchLoading = false;
          console.error("获取优惠券批次列表失败", error);
          this.$message.error("获取优惠券批次列表失败");
          this.couponBatchList = [];
        });
    },

    // 选择优惠券批次行
    selectCouponBatch(row) {
      this.selectedCouponBatchRow = row;
    },

    // 确认选择优惠券批次
    handleCouponBatchConfirm() {
      if (this.selectedCouponBatchRow) {
        this.selectedCouponBatch = this.selectedCouponBatchRow;
        this.formData.object_id = this.selectedCouponBatchRow.id;
        this.formData.object_type = "coupon_batch";
        this.couponBatchDialogVisible = false;
      }
    },

    // 关闭优惠券批次选择对话框
    handleCouponBatchDialogClose() {
      this.couponBatchDialogVisible = false;
      this.selectedCouponBatchRow = null;
    },

    // 清除优惠券批次选择
    clearCouponBatch() {
      this.selectedCouponBatch = null;
      this.formData.object_id = null;
      this.formData.object_type = null;
    },

    // 加载优惠券批次信息
    loadCouponBatchInfo(couponBatchId) {
      requestApi({
        name: "getCouponBatchDetail",
        data: {
          id: couponBatchId,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.selectedCouponBatch = response.data;
          } else {
            console.warn("获取优惠券批次详情失败:", response.message);
          }
        })
        .catch((error) => {
          console.error("获取优惠券批次详情失败", error);
        });
    },

    // 初始化表单数据
    initFormData() {
      if (this.editData) {
        this.loadEditData(this.editData);
      } else {
        this.formData = {
          id: undefined,
          name: "",
          imageUrl: "",
          imageFile: null,
          type: "direct_sale",
          category_ids: [],
          price: 0,
          stock: 0,
          status: 1,
          description: "",
          reservation_fee: 0,
          max_reservations: 1,
          reservation_deadline: null,
          cancellation_deadline: null,
          is_approval_required: false,
          meal_type: "business",
          // 虚拟产品字段
          object_id: null,
          object_type: null,
        };
        this.selectedCategoriesDisplay = [];
      }
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.dataForm) {
          this.$refs.dataForm.clearValidate();
        }
      });
    },

    // 加载编辑数据
    loadEditData(editData) {
      this.loading = true;
      requestApi({
        name: "getProduct",
        data: {
          product_id: editData.id,
        },
      })
        .then((response) => {
          this.loading = false;
          if (response && response.code === 200) {
            // 从categories数组中提取category_ids
            const categoryIds = response.data.categories
              ? response.data.categories.map((cat) => cat.id)
              : [];

            // 将API返回的数据映射到表单
            this.formData = {
              id: response.data.id || "",
              name: response.data.name || "",
              imageUrl: response.data.imageUrl || response.data.image || "",
              imageFile: null,
              type: response.data.type || "direct_sale",
              category_ids: categoryIds,
              price: response.data.price || 0,
              stock: response.data.stock || 0,
              status: response.data.status || 0,
              description: response.data.description || "",
              reservation_fee: response.data.reservation_fee || 0,
              max_reservations: response.data.max_reservations || 1,
              reservation_deadline: response.data.reservation_deadline || "",
              cancellation_deadline: response.data.cancellation_deadline || "",
              is_approval_required: response.data.is_approval_required || false,
              meal_type: response.data.meal_type || "business",
              // 虚拟产品字段
              object_id: response.data.object_id || null,
              object_type: response.data.object_type || null,
            };

            // 延迟更新选中的分类显示，确保分类树数据已加载
            this.$nextTick(() => {
              this.updateSelectedCategoriesDisplay();
              // 同步树组件的选中状态
              if (
                this.$refs.categoryTree &&
                this.formData.category_ids.length > 0
              ) {
                this.$refs.categoryTree.setCheckedKeys(
                  this.formData.category_ids
                );
              }

              // 如果是虚拟产品且有object_id，加载对应的优惠券批次信息
              if (
                this.formData.type === "virtual" &&
                this.formData.object_id &&
                this.formData.object_type === "coupon_batch"
              ) {
                this.loadCouponBatchInfo(this.formData.object_id);
              }
            });
          } else {
            this.$message.error(response.message || "获取产品详情失败");
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error("获取产品详情失败", error);
          this.$message.error("获取产品详情失败");
        });
    },

    // 图片上传成功回调
    handleImageSuccess(res) {
      if (res && res.code === 200) {
        this.formData.imageUrl = res.data.url;
        // 保存实际上传的文件对象，以便在保存时直接使用
        this.formData.imageFile = res.data.originalFile;
      } else {
        this.$message.error(res.message || "图片上传失败");
      }
    },

    // 文件上传前验证
    beforeUploadFile(file) {
      // 检查文件大小和类型
      const isJPG =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png";
      const isLtSize = file.size / 1024 / 1024 < 5; // 限制5MB

      if (!isJPG) {
        this.$message.error("上传图片必须是JPG/JPEG/PNG格式!");
      }
      if (!isLtSize) {
        this.$message.error("上传图片大小不能超过5MB!");
      }

      return isJPG && isLtSize;
    },

    // 自定义上传请求
    customUploadRequest(options) {
      const { file } = options;

      // 直接模拟上传成功，将文件保存到formData中
      // 实际场景中可能需要先上传到服务器然后获取URL
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        // 模拟成功的响应
        const response = {
          code: 200,
          data: {
            url: reader.result, // 使用Base64作为预览
            originalFile: file, // 保存原始文件对象
          },
        };
        // 调用成功回调
        this.handleImageSuccess(response);
      };
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          if (this.formData.id !== undefined) {
            // 编辑产品
            // 创建FormData对象以支持文件上传
            const formData = new FormData();
            formData.append("name", this.formData.name);
            formData.append("type", this.formData.type);
            formData.append("meal_type", this.formData.meal_type);
            if (
              this.formData.category_ids &&
              this.formData.category_ids.length > 0
            ) {
              formData.append(
                "category_ids",
                JSON.stringify(this.formData.category_ids)
              );
            }
            formData.append("price", this.formData.price);
            formData.append("stock", this.formData.stock);
            formData.append("status", this.formData.status);
            formData.append("description", this.formData.description || "");
            formData.append("reservation_fee", this.formData.reservation_fee);
            formData.append("max_reservations", this.formData.max_reservations);
            formData.append(
              "is_approval_required",
              this.formData.is_approval_required
            );

            // 虚拟产品字段
            if (this.formData.object_id !== null) {
              formData.append("object_id", this.formData.object_id);
            }
            if (this.formData.object_type) {
              formData.append("object_type", this.formData.object_type);
            }

            // 处理时间字段
            if (
              this.formData.reservation_deadline &&
              this.formData.reservation_deadline !== ""
            ) {
              formData.append(
                "reservation_deadline",
                this.formData.reservation_deadline
              );
            }
            if (
              this.formData.cancellation_deadline &&
              this.formData.cancellation_deadline !== ""
            ) {
              formData.append(
                "cancellation_deadline",
                this.formData.cancellation_deadline
              );
            }

            // 图片处理
            if (this.formData.imageFile) {
              formData.append("image_file", this.formData.imageFile);
            } else if (this.formData.imageUrl) {
              formData.append("imageUrl", this.formData.imageUrl);
            }

            // 将id添加到formData中，而不是创建包装对象
            formData.append("id", this.formData.id);

            requestApi({
              name: "updateProduct",
              data: formData,
            })
              .then((response) => {
                this.loading = false;
                if (response && response.code === 200) {
                  this.$message({
                    type: "success",
                    message: "产品信息已更新!",
                  });
                  this.$emit("success");
                  this.handleCancel();
                } else {
                  this.$message.error(response.message || "更新产品失败");
                }
              })
              .catch((error) => {
                this.loading = false;
                console.error("更新产品失败", error);
                this.$message.error("更新产品失败");
              });
          } else {
            // 新增产品
            // 创建FormData对象以支持文件上传
            const formData = new FormData();
            formData.append("name", this.formData.name);
            formData.append("type", this.formData.type);
            formData.append("meal_type", this.formData.meal_type);
            if (
              this.formData.category_ids &&
              this.formData.category_ids.length > 0
            ) {
              formData.append(
                "category_ids",
                JSON.stringify(this.formData.category_ids)
              );
            }
            formData.append("price", this.formData.price);
            formData.append("stock", this.formData.stock);
            formData.append("status", this.formData.status);
            formData.append("description", this.formData.description || "");
            formData.append("reservation_fee", this.formData.reservation_fee);
            formData.append("max_reservations", this.formData.max_reservations);
            formData.append(
              "is_approval_required",
              this.formData.is_approval_required
            );

            // 虚拟产品字段
            if (this.formData.object_id !== null) {
              formData.append("object_id", this.formData.object_id);
            }
            if (this.formData.object_type) {
              formData.append("object_type", this.formData.object_type);
            }

            // 处理时间字段
            if (this.formData.reservation_deadline) {
              formData.append(
                "reservation_deadline",
                this.formData.reservation_deadline
              );
            }
            if (this.formData.cancellation_deadline) {
              formData.append(
                "cancellation_deadline",
                this.formData.cancellation_deadline
              );
            }

            // 图片处理
            if (this.formData.imageFile) {
              formData.append("image_file", this.formData.imageFile);
            } else if (this.formData.imageUrl) {
              formData.append("imageUrl", this.formData.imageUrl);
            }

            requestApi({
              name: "addProduct",
              data: formData,
            })
              .then((response) => {
                this.loading = false;
                if (response && response.code === 200) {
                  this.$message({
                    type: "success",
                    message: "新增产品成功!",
                  });
                  this.$emit("success");
                  this.handleCancel();
                } else {
                  this.$message.error(response.message || "新增产品失败");
                }
              })
              .catch((error) => {
                this.loading = false;
                console.error("新增产品失败", error);
                this.$message.error("新增产品失败");
              });
          }
        }
      });
    },

    // 取消
    handleCancel() {
      this.$emit("close");
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.upload-container {
  display: flex;
  align-items: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
  object-fit: cover;
}

.category-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  min-height: 60px;
  background-color: #fafafa;
}

.selected-categories {
  margin-bottom: 8px;
}

.no-category-selected {
  margin-bottom: 8px;
}

.category-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 10px;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-node-label {
  font-size: 14px;
  color: #333;
}

/deep/ .el-tree-node__content {
  height: auto;
  padding: 6px 0;
}

/deep/ .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.coupon-batch-selector {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  min-height: 60px;
  background-color: #fafafa;
}

.selected-coupon-batch {
  margin-bottom: 8px;
}

.no-coupon-batch-selected {
  margin-bottom: 8px;
}

.coupon-batch-search {
  margin-bottom: 15px;
}

.coupon-batch-list-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #909399;
}
</style>
