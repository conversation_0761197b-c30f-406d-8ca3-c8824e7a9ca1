<template>
  <div class="product-list">
    <h1>产品列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
            v-model="searchForm.name"
            placeholder="产品名称"
            size="mini"
            class="filter-item"
          />
          <el-select
            v-model="searchForm.category_ids"
            placeholder="选择分类"
            size="mini"
            class="filter-item"
            clearable
            filterable
            multiple
            collapse-tags
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
        <el-button type="primary" size="mini" @click="handleSearch"
          >查询
        </el-button>
        <el-button size="mini" @click="resetSearch">重置 </el-button>
      </div>
      <div class="left-buttons">
        <el-button
          type="primary"
          size="mini"
          @click="handleAdd"
          icon="el-icon-plus"
          >新增产品
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="id" label="ID" width="65" align="center" />
      <el-table-column prop="name" label="名称" align="center">
        <template slot-scope="scope">
          <div class="package-info">
            <el-avatar
              shape="square"
              size="medium"
              :src="scope.row.imageUrl || scope.row.image || defaultImage"
            ></el-avatar>
            <span class="ml-10">{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120" align="center">
        <template slot-scope="scope">
          {{ $options.filters.typeFilter(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="meal_type" label="餐型" width="120" align="center">
        <template slot-scope="scope">
          {{ $options.filters.mealTypeFilter(scope.row.meal_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="单价" width="120" align="center">
        <template slot-scope="scope"> {{ scope.row.price }}元</template>
      </el-table-column>
      <el-table-column
        prop="stock"
        label="库存"
        width="100"
        align="center"
        :formatter="formatStock"
      />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "已上架" : "已下架" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="350">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)"
            >编辑</el-button
          >

          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "下架" : "上架" }}
          </el-button>

          <el-button
            type="text"
            size="mini"
            @click="handleManageContent(scope.row)"
            >内容管理</el-button
          >

          <el-button
            type="text"
            size="mini"
            @click="handleManagePricing(scope.row)"
            >价格策略</el-button
          >

          <el-button
            type="text"
            size="mini"
            @click="handleManageRule(scope.row)"
            >规则管理</el-button
          >

          <el-button type="text" size="mini" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增/编辑产品弹窗 -->
    <product-detail
      :visible="dialogFormVisible"
      :edit-data="currentEditData"
      @close="handleDialogClose"
      @success="handleDialogSuccess"
    />
    <!-- 新增/编辑产品弹窗结束 -->

    <!-- 内容管理对话框 -->
    <el-dialog
      title="内容管理"
      :visible.sync="contentDialogVisible"
      width="800px"
    >
      <div class="content-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="contentSearchForm.title"
              placeholder="内容标题"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="contentSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button type="primary" size="mini" @click="handleContentSearch"
              >查询</el-button
            >
            <el-button type="primary" size="mini" @click="resetContentSearch"
              >重置</el-button
            >
          </div>
          <div class="content-buttons">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddContent"
              >添加内容
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="contentLoading"
          :data="contentData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="内容标题"></el-table-column>
          <el-table-column
            prop="type"
            label="内容类型"
            width="120"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleRemoveContent(scope.row)"
                >移除内容
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handleContentCurrentChange"
            @size-change="handleContentSizeChange"
            :current-page.sync="contentPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="contentPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="contentTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 内容管理对话框结束 -->

    <!-- 添加内容对话框 -->
    <el-dialog
      title="添加内容"
      :visible.sync="addContentDialogVisible"
      width="800px"
    >
      <div class="content-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="contentListSearchForm.name"
              placeholder="内容标题"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="contentListSearchForm.type"
              placeholder="内容类型"
              size="mini"
              class="search-input"
            >
              <el-option label="文章" value="article"></el-option>
              <el-option label="视频" value="dish"></el-option>
            </el-select>
            <el-select
              v-model="contentListSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              @click="handleContentListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="resetContentListSearch"
              >重置</el-button
            >
          </div>
        </div>

        <el-table
          v-loading="contentListLoading"
          :data="contentListData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="内容标题"></el-table-column>
          <el-table-column
            prop="type"
            label="内容类型"
            width="120"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleJoinContentAction(scope.row)"
                >添加
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handleContentListCurrentChange"
            @size-change="handleContentListSizeChange"
            :current-page.sync="contentListPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="contentListPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="contentListTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 添加内容对话框结束 -->

    <!-- 价格策略管理对话框 -->
    <el-dialog
      title="价格策略管理"
      :visible.sync="pricingDialogVisible"
      width="800px"
    >
      <div class="pricing-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="pricingSearchForm.name"
              placeholder="策略名称"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="pricingSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button type="primary" size="mini" @click="handlePricingSearch"
              >查询</el-button
            >
            <el-button type="primary" size="mini" @click="resetPricingSearch"
              >重置</el-button
            >
          </div>
          <div class="pricing-buttons">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddPricing"
              >应用价格策略
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="pricingLoading"
          :data="pricingData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="策略名称"></el-table-column>
          <el-table-column
            prop="type"
            label="策略类型"
            width="120"
          ></el-table-column>
          <el-table-column prop="discount" label="折扣" width="120">
            <template slot-scope="scope"> {{ scope.row.discount }}% </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleRemovePricing(scope.row)"
                >取消策略
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handlePricingCurrentChange"
            @size-change="handlePricingSizeChange"
            :current-page.sync="pricingPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pricingPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pricingTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 价格策略管理对话框结束 -->

    <!-- 应用价格策略对话框 -->
    <el-dialog
      title="应用价格策略"
      :visible.sync="addPricingDialogVisible"
      width="800px"
    >
      <div class="pricing-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="pricingListSearchForm.name"
              placeholder="策略名称"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="pricingListSearchForm.type"
              placeholder="策略类型"
              size="mini"
              class="search-input"
            >
              <el-option label="折扣" value="discount"></el-option>
              <el-option label="满减" value="reduction"></el-option>
            </el-select>
            <el-select
              v-model="pricingListSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              @click="handlePricingListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="resetPricingListSearch"
              >重置</el-button
            >
          </div>
        </div>

        <el-table
          v-loading="pricingListLoading"
          :data="pricingListData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="策略名称"></el-table-column>
          <el-table-column
            prop="type"
            label="策略类型"
            width="120"
          ></el-table-column>
          <el-table-column prop="discount" label="折扣" width="120">
            <template slot-scope="scope"> {{ scope.row.discount }}% </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleApplyPricingAction(scope.row)"
                >应用
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handlePricingListCurrentChange"
            @size-change="handlePricingListSizeChange"
            :current-page.sync="pricingListPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pricingListPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pricingListTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 应用价格策略对话框结束 -->

    <!-- 规则管理对话框 -->
    <el-dialog title="规则管理" :visible.sync="ruleDialogVisible" width="800px">
      <div class="rule-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="ruleSearchForm.name"
              placeholder="规则名称"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="ruleSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button type="primary" size="mini" @click="handleRuleSearch"
              >查询</el-button
            >
            <el-button type="primary" size="mini" @click="resetRuleSearch"
              >重置</el-button
            >
          </div>
          <div class="rule-buttons">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddRule"
              >应用规则
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="ruleLoading"
          :data="ruleData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="规则名称"></el-table-column>
          <el-table-column prop="type" label="规则类型" width="120">
            <template slot-scope="scope">
              {{ $options.filters.ruleTypeFilter(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleRemoveRule(scope.row)"
                >取消规则
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handleRuleCurrentChange"
            @size-change="handleRuleSizeChange"
            :current-page.sync="rulePagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="rulePagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="ruleTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 规则管理对话框结束 -->

    <!-- 应用规则对话框 -->
    <el-dialog
      title="应用规则"
      :visible.sync="addRuleDialogVisible"
      width="800px"
    >
      <div class="rule-dialog-content">
        <div class="search-header">
          <div class="search-container">
            <el-input
              v-model="ruleListSearchForm.name"
              placeholder="规则名称"
              size="mini"
              class="search-input"
            ></el-input>
            <el-select
              v-model="ruleListSearchForm.type"
              placeholder="规则类型"
              size="mini"
              class="search-input"
            >
              <el-option label="规则" value="rule"></el-option>
              <el-option label="预订规则" value="reservation"></el-option>
              <el-option
                label="用餐预订规则"
                value="dining_reservation"
              ></el-option>
            </el-select>
            <el-select
              v-model="ruleListSearchForm.status"
              placeholder="状态"
              size="mini"
              class="search-input"
            >
              <el-option label="开启" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button type="primary" size="mini" @click="handleRuleListSearch"
              >查询</el-button
            >
            <el-button type="primary" size="mini" @click="resetRuleListSearch"
              >重置</el-button
            >
          </div>
        </div>

        <el-table
          v-loading="ruleListLoading"
          :data="ruleListData"
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="规则名称"></el-table-column>
          <el-table-column prop="type" label="规则类型" width="120">
            <template slot-scope="scope">
              {{ $options.filters.ruleTypeFilter(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.status === 1 ? 'status-normal' : 'status-disabled'
                "
              >
                {{ scope.row.status === 1 ? "开启" : "禁用" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="150">
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleJoinRuleAction(scope.row)"
                >应用
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @current-change="handleRuleListCurrentChange"
            @size-change="handleRuleListSizeChange"
            :current-page.sync="ruleListPagination.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="ruleListPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="ruleListTotal"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!-- 应用规则对话框结束 -->
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import ProductDetail from "./product-detail.vue";

export default {
  name: "ProductList",
  components: {
    ProductDetail,
  },
  filters: {
    typeFilter(type) {
      const typeMap = {
        direct_sale: "普通产品",
        reservation: "预订产品",
      };
      return typeMap[type] || "";
    },
    mealTypeFilter(mealType) {
      const mealTypeMap = {
        business: "商务餐",
        buffet: "自助餐",
        coupon: "优惠券",
      };
      return mealTypeMap[mealType] || "";
    },
    ruleTypeFilter(ruleType) {
      const ruleTypeMap = {
        rule: "规则",
        reservation: "预订规则",
        dining_reservation: "用餐预订规则",
      };
      return ruleTypeMap[ruleType] || "";
    },
  },
  data() {
    return {
      searchForm: {
        name: "",
        category_ids: [],
      },
      categoryOptions: [],
      listLoading: false,
      tableData: [],
      currentPage: 1,
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 3,
      dialogFormVisible: false,
      currentEditData: null,
      pricings: {
        name: [{ required: true, message: "请输入产品名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择产品类型", trigger: "change" },
        ],
        price: [{ required: true, message: "请输入产品价格", trigger: "blur" }],
        stock: [{ required: true, message: "请输入库存数量", trigger: "blur" }],
        reservation_fee: [
          { required: true, message: "请输入预订费用", trigger: "blur" },
        ],
        max_reservations: [
          { required: true, message: "请输入最大预订数量", trigger: "blur" },
        ],
        reservation_deadline: [
          { required: true, message: "请选择预订截止时间", trigger: "change" },
        ],
        cancellation_deadline: [
          { required: true, message: "请选择取消截止时间", trigger: "change" },
        ],
      },
      defaultImage:
        "https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",
      contentDialogVisible: false,
      contentSearchForm: {
        title: "",
        status: "",
      },
      contentLoading: false,
      contentData: [],
      contentPagination: {
        page: 1,
        pageSize: 10,
      },
      contentTotal: 0,
      addContentDialogVisible: false,
      contentListSearchForm: {
        title: "",
        content_type: "",
        status: "",
      },
      contentListLoading: false,
      contentListData: [],
      contentListPagination: {
        page: 1,
        pageSize: 10,
      },
      contentListTotal: 0,
      currentProductId: null,
      pricingDialogVisible: false,
      pricingSearchForm: {
        name: "",
        status: "",
      },
      pricingLoading: false,
      pricingData: [],
      pricingPagination: {
        page: 1,
        pageSize: 10,
      },
      pricingTotal: 0,
      addPricingDialogVisible: false,
      pricingListSearchForm: {
        name: "",
        type: "",
        status: "",
      },
      pricingListLoading: false,
      pricingListData: [],
      pricingListPagination: {
        page: 1,
        pageSize: 10,
      },
      pricingListTotal: 0,
      ruleDialogVisible: false,
      ruleSearchForm: {
        name: "",
        status: "",
      },
      ruleLoading: false,
      ruleData: [],
      rulePagination: {
        page: 1,
        pageSize: 10,
      },
      ruleTotal: 0,
      addRuleDialogVisible: false,
      ruleListSearchForm: {
        name: "",
        type: "",
        status: "",
      },
      ruleListLoading: false,
      ruleListData: [],
      ruleListPagination: {
        page: 1,
        pageSize: 10,
      },
      ruleListTotal: 0,
    };
  },
  created() {
    console.log("ProductList组件已创建");
    this.getProductList();
    this.getCategoryOptions();
  },
  methods: {
    formatStock(row, column, cellValue) {
      return cellValue === -1 ? "不限制" : cellValue;
    },
    // 获取分类选项
    getCategoryOptions() {
      requestApi({
        name: "getCategoryTree",
        data: {},
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.categoryOptions = this.flattenCategories(response.data || []);
          } else {
            console.error("获取分类列表失败:", response.message);
          }
        })
        .catch((error) => {
          console.error("获取分类列表失败", error);
        });
    },

    // 扁平化分类数据（用于分类选择）
    flattenCategories(categories, level = 0) {
      let result = [];
      categories.forEach((category) => {
        const prefix = "　".repeat(level);
        result.push({
          id: category.id,
          name: prefix + category.name,
        });
        if (category.children && category.children.length > 0) {
          result = result.concat(
            this.flattenCategories(category.children, level + 1)
          );
        }
      });
      return result;
    },

    // 获取产品列表
    getProductList() {
      this.loading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }
      if (this.searchForm.category_ids.length > 0) {
        params.category_ids = this.searchForm.category_ids;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getProductsBySearch",
        data: params,
      })
        .then((response) => {
          this.loading = false;
          console.log("API响应:", response.data.list);
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取产品列表失败");
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error("获取产品列表失败", error);
          this.$message.error("获取产品列表失败");
        });
    },
    handleSearch() {
      this.pagination.page = 1;
      this.getProductList();
    },
    resetSearch() {
      this.searchForm = {
        name: "",
        category_ids: [],
      };
      this.getProductList();
    },

    // 产看产品明细
    handleView(row) {
      requestApi({
        name: "getProductDetail",
        data: {
          product_id: Number(row.id),
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.formData = {
              id: response.data.id || "",
              name: response.data.name || "",
              type: response.data.type || "direct_sale",
              price: response.data.price || 0,
              stock: response.data.stock || 0,
              status: response.data.status || 0,
              description: response.data.description || "",
            };
            this.userInfoDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取产品信息失败");
          }
        })
        .catch((error) => {
          console.error("获取产品信息失败", error);
          this.$message.error("获取产品信息失败");
        });
    },

    handleAdd() {
      this.currentEditData = null;
      this.dialogFormVisible = true;
    },
    handleEdit(row) {
      this.currentEditData = { ...row };
      this.dialogFormVisible = true;
    },

    // 处理弹窗关闭
    handleDialogClose() {
      this.dialogFormVisible = false;
      this.currentEditData = null;
    },

    // 处理弹窗成功
    handleDialogSuccess() {
      this.dialogFormVisible = false;
      this.currentEditData = null;
      this.getProductList();
    },
    handleDelete(row) {
      this.$confirm("确认删除该产品?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "deleteProduct",
            data: {
              id: row.id,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getProductList(); // 刷新列表
              }
            })
            .catch((error) => {
              console.error("删除产品失败", error);
              this.$message.error("删除产品失败");
            })
            .finally(() => {
              this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? "上架" : "下架";

      this.$confirm(`确认${statusText}该产品?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          requestApi({
            name: "updateProductStatus",
            data: {
              id: row.id,
              status: newStatus,
            },
          })
            .then((response) => {
              this.listLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: `产品已${statusText}!`,
                });
                row.status = newStatus; // 更新本地状态
                this.getProductList(); // 刷新列表
              } else {
                this.$message.error(
                  response.message || `${statusText}产品失败`
                );
              }
            })
            .catch((error) => {
              this.listLoading = false;
              console.error(`${statusText}产品失败`, error);
              this.$message.error(`${statusText}产品失败`);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getProductList();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getProductList();
    },

    handleManageContent(row) {
      this.currentProductId = row.id;
      this.contentDialogVisible = true;
      this.contentPagination.page = 1;
      this.getProductContents();
    },

    handleContentSearch() {
      this.contentPagination.page = 1;
      this.getProductContents();
    },

    resetContentSearch() {
      this.contentSearchForm = {
        title: "",
        status: "",
      };
      this.getProductContents();
    },

    getProductContents() {
      this.contentLoading = true;

      const params = {
        product_id: this.currentProductId,
        page: this.contentPagination.page,
        pageSize: this.contentPagination.pageSize,
      };

      // 添加搜索参数
      if (this.contentSearchForm.title) {
        params.title = this.contentSearchForm.title;
      }
      if (this.contentSearchForm.status !== "") {
        params.status = this.contentSearchForm.status;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getProductContents",
        data: params,
      })
        .then((response) => {
          this.contentLoading = false;
          console.log("API响应:", response.data.list);
          if (response && response.code === 200) {
            this.contentData = response.data.list || [];
            this.contentTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取内容列表失败");
          }
        })
        .catch((error) => {
          this.contentLoading = false;
          console.error("获取内容列表失败", error);
          this.$message.error("获取内容列表失败");
        });
    },

    handleAddContent() {
      this.contentListPagination.page = 1;
      this.contentListSearchForm = {
        title: "",
        content_type: "",
        status: "",
      };
      this.addContentDialogVisible = true;
      this.getContentList();
    },

    handleRemoveContent(row) {
      this.$confirm("确认移除该内容?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.contentLoading = true;
          requestApi({
            name: "removeProductContent",
            data: {
              product_id: this.currentProductId,
              content_ids: [row.id],
            },
          })
            .then((response) => {
              this.contentLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "移除成功!",
                });
                this.getProductContents(); // 刷新列表
              } else {
                this.$message.error(response.message || "移除内容失败");
              }
            })
            .catch((error) => {
              this.contentLoading = false;
              console.error("移除内容失败", error);
              this.$message.error("移除内容失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    handleContentCurrentChange(val) {
      this.contentPagination.page = val;
      this.getProductContents();
    },

    handleContentSizeChange(val) {
      this.contentPagination.pageSize = val;
      this.getProductContents();
    },

    getContentList() {
      this.contentListLoading = true;

      const params = {
        page: this.contentListPagination.page,
        pageSize: this.contentListPagination.pageSize,
      };

      // 添加搜索参数
      if (this.contentListSearchForm.title) {
        params.title = this.contentListSearchForm.title;
      }
      if (this.contentListSearchForm.content_type !== "") {
        params.content_type = this.contentListSearchForm.content_type;
      }
      if (this.contentListSearchForm.status !== "") {
        params.status = this.contentListSearchForm.status;
      }

      // 打印请求参数便于调试
      console.log("内容列表请求参数:", params);

      requestApi({
        name: "getDishListBySearch",
        data: params,
      })
        .then((response) => {
          this.contentListLoading = false;
          if (response && response.code === 200) {
            this.contentListData = response.data.list || [];
            this.contentListTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取内容列表失败");
          }
        })
        .catch((error) => {
          this.contentListLoading = false;
          console.error("获取内容列表失败", error);
          this.$message.error("获取内容列表失败");
        });
    },

    handleContentListSearch() {
      this.contentListPagination.page = 1;
      this.getContentList();
    },

    resetContentListSearch() {
      this.contentListSearchForm = {
        title: "",
        content_type: "",
        status: "",
      };
      this.getContentList();
    },

    handleJoinContentAction(row) {
      requestApi({
        name: "addProductContent",
        data: {
          product_id: this.currentProductId,
          content_ids: [row.id],
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("添加内容成功");
            this.addContentDialogVisible = false;
            this.getProductContents(); // 刷新已关联的内容列表
          } else {
            this.$message.error(response.message || "添加内容失败");
          }
        })
        .catch((error) => {
          console.error("添加内容失败", error);
          this.$message.error("添加内容失败");
        });
    },

    handleContentListCurrentChange(val) {
      this.contentListPagination.page = val;
      this.getContentList();
    },

    handleContentListSizeChange(val) {
      this.contentListPagination.pageSize = val;
      this.getContentList();
    },

    handleManagePricing(row) {
      this.currentProductId = row.id;
      this.pricingDialogVisible = true;
      this.pricingPagination.page = 1;
      this.getProductPricings();
    },

    handlePricingSearch() {
      this.pricingPagination.page = 1;
      this.getProductPricings();
    },

    resetPricingSearch() {
      this.pricingSearchForm = {
        name: "",
        status: "",
      };
      this.getProductPricings();
    },

    getProductPricings() {
      this.pricingLoading = true;

      const params = {
        product_id: this.currentProductId,
        page: this.pricingPagination.page,
        pageSize: this.pricingPagination.pageSize,
      };

      // 添加搜索参数
      if (this.pricingSearchForm.name) {
        params.name = this.pricingSearchForm.name;
      }
      if (this.pricingSearchForm.status !== "") {
        params.status = this.pricingSearchForm.status;
      }

      // 打印请求参数便于调试
      console.log("价格策略请求参数:", params);

      requestApi({
        name: "getProductPricings",
        data: params,
      })
        .then((response) => {
          this.pricingLoading = false;
          if (response && response.code === 200) {
            this.pricingData = response.data.list || [];
            this.pricingTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取价格策略列表失败");
          }
        })
        .catch((error) => {
          this.pricingLoading = false;
          console.error("获取价格策略列表失败", error);
          this.$message.error("获取价格策略列表失败");
        });
    },

    handleAddPricing() {
      this.pricingListPagination.page = 1;
      this.pricingListSearchForm = {
        name: "",
        type: "",
        status: "",
      };
      this.addPricingDialogVisible = true;
      this.getPricingListData();
    },

    handleRemovePricing(row) {
      this.$confirm("确认取消该价格策略?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.pricingLoading = true;
          requestApi({
            name: "removeProductPricing",
            data: {
              product_id: this.currentProductId,
              pricing_strategy_ids: [row.id],
            },
          })
            .then((response) => {
              this.pricingLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "取消价格策略成功!",
                });
                this.getProductPricings(); // 刷新价格策略列表
              } else {
                this.$message.error(response.message || "取消价格策略失败");
              }
            })
            .catch((error) => {
              this.pricingLoading = false;
              console.error("取消价格策略失败", error);
              this.$message.error("取消价格策略失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    handlePricingCurrentChange(val) {
      this.pricingPagination.page = val;
      this.getProductPricings();
    },

    handlePricingSizeChange(val) {
      this.pricingPagination.pageSize = val;
      this.getProductPricings();
    },

    handlePricingListSearch() {
      this.pricingListPagination.page = 1;
      this.getPricingListData();
    },

    resetPricingListSearch() {
      this.pricingListSearchForm = {
        name: "",
        type: "",
        status: "",
      };
      this.getPricingListData();
    },

    getPricingListData() {
      this.pricingListLoading = true;

      const params = {
        page: this.pricingListPagination.page,
        pageSize: this.pricingListPagination.pageSize,
      };

      // 添加搜索参数
      if (this.pricingListSearchForm.name) {
        params.name = this.pricingListSearchForm.name;
      }
      if (this.pricingListSearchForm.type !== "") {
        params.type = this.pricingListSearchForm.type;
      }
      if (this.pricingListSearchForm.status !== "") {
        params.status = this.pricingListSearchForm.status;
      }

      // 打印请求参数便于调试
      console.log("价格策略列表数据请求参数:", params);

      requestApi({
        name: "getPricingStrategiesBySearch",
        data: params,
      })
        .then((response) => {
          this.pricingListLoading = false;
          if (response && response.code === 200) {
            this.pricingListData = response.data.list || [];
            this.pricingListTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取价格策略列表数据失败");
          }
        })
        .catch((error) => {
          this.pricingListLoading = false;
          console.error("获取价格策略列表数据失败", error);
          this.$message.error("获取价格策略列表数据失败");
        });
    },

    handleApplyPricingAction(row) {
      requestApi({
        name: "addProductPricing",
        data: {
          product_id: this.currentProductId,
          pricing_strategy_ids: [row.id],
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("应用价格策略成功");
            this.addPricingDialogVisible = false;
            this.getProductPricings(); // 刷新已关联的价格策略列表
          } else {
            this.$message.error(response.message || "应用价格策略失败");
          }
        })
        .catch((error) => {
          console.error("应用价格策略失败", error);
          this.$message.error("应用价格策略失败");
        });
    },

    handlePricingListCurrentChange(val) {
      this.pricingListPagination.page = val;
      this.getPricingListData();
    },

    handlePricingListSizeChange(val) {
      this.pricingListPagination.pageSize = val;
      this.getPricingListData();
    },

    handleManageRule(row) {
      this.currentProductId = row.id;
      this.ruleDialogVisible = true;
      this.rulePagination.page = 1;
      this.getProductRules();
    },

    getProductRules() {
      this.ruleLoading = true;

      const params = {
        product_id: this.currentProductId,
        page: this.rulePagination.page,
        pageSize: this.rulePagination.pageSize,
      };

      // 打印请求参数便于调试
      console.log("规则请求参数:", params);

      requestApi({
        name: "getProductRules",
        data: params,
      })
        .then((response) => {
          this.ruleLoading = false;
          if (response && response.code === 200) {
            this.ruleData = response.data.list || [];
            this.ruleTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取规则列表失败");
          }
        })
        .catch((error) => {
          this.ruleLoading = false;
          console.error("获取规则列表失败", error);
          this.$message.error("获取规则列表失败");
        });
    },

    handleAddRule() {
      this.ruleListPagination.page = 1;
      this.ruleListSearchForm = {
        name: "",
        type: "",
        status: "",
      };
      this.addRuleDialogVisible = true;
      this.getRuleListData();
    },

    handleRemoveRule(row) {
      this.$confirm("确认取消该规则?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.ruleLoading = true;
          requestApi({
            name: "removeProductRule",
            data: {
              product_id: this.currentProductId,
              rule_ids: [row.id],
            },
          })
            .then((response) => {
              this.ruleLoading = false;
              if (response && response.code === 200) {
                this.$message({
                  type: "success",
                  message: "取消规则成功!",
                });
                this.getProductRules(); // 刷新规则列表
              } else {
                this.$message.error(response.message || "取消规则失败");
              }
            })
            .catch((error) => {
              this.ruleLoading = false;
              console.error("取消规则失败", error);
              this.$message.error("取消规则失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    handleRuleSearch() {
      this.rulePagination.page = 1;
      this.getProductRules();
    },

    resetRuleSearch() {
      this.ruleSearchForm = {
        name: "",
        status: "",
      };
      this.getProductRules();
    },

    getRuleListData() {
      this.ruleListLoading = true;

      const params = {
        page: this.ruleListPagination.page,
        pageSize: this.ruleListPagination.pageSize,
      };

      // 打印请求参数便于调试
      console.log("规则列表数据请求参数:", params);

      requestApi({
        name: "getRulesBySearch",
        data: params,
      })
        .then((response) => {
          this.ruleListLoading = false;
          if (response && response.code === 200) {
            this.ruleListData = response.data.list || [];
            this.ruleListTotal = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取规则列表数据失败");
          }
        })
        .catch((error) => {
          this.ruleListLoading = false;
          console.error("获取规则列表数据失败", error);
          this.$message.error("获取规则列表数据失败");
        });
    },

    handleJoinRuleAction(row) {
      requestApi({
        name: "addProductRule",
        data: {
          product_id: this.currentProductId,
          rule_ids: [row.id],
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("应用规则成功");
            this.addRuleDialogVisible = false;
            this.getProductRules(); // 刷新已关联的规则列表
          } else {
            this.$message.error(response.message || "应用规则失败");
          }
        })
        .catch((error) => {
          console.error("应用规则失败", error);
          this.$message.error("应用规则失败");
        });
    },

    handleRuleCurrentChange(val) {
      this.rulePagination.page = val;
      this.getProductRules();
    },

    handleRuleSizeChange(val) {
      this.rulePagination.pageSize = val;
      this.getProductRules();
    },

    handleRuleListSearch() {
      this.ruleListPagination.page = 1;
      this.getRuleListData();
    },

    resetRuleListSearch() {
      this.ruleListSearchForm = {
        name: "",
        type: "",
        status: "",
      };
      this.getRuleListData();
    },

    handleRuleListCurrentChange(val) {
      this.ruleListPagination.page = val;
      this.getRuleListData();
    },

    handleRuleListSizeChange(val) {
      this.ruleListPagination.pageSize = val;
      this.getRuleListData();
    },
  },
};
</script>

<style lang="less" scoped>
.product-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .left-buttons {
      display: flex;
      gap: 10px;
    }

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .package-info {
    display: flex;
    align-items: center;

    .ml-10 {
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--small {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

.content-dialog-content {
  padding: 20px;

  .search-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .search-container {
      display: flex;
      gap: 10px;

      .search-input {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .content-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}

.status-normal {
  color: #67c23a;
}

.status-disabled {
  color: #f56c6c;
}

.pricing-dialog-content {
  padding: 20px;

  .search-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .search-container {
      display: flex;
      gap: 10px;

      .search-input {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .pricing-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}

.rule-dialog-content {
  padding: 20px;

  .search-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .search-container {
      display: flex;
      gap: 10px;

      .search-input {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .rule-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}
</style>
