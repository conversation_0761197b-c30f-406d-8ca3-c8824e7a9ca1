<template>
  <div class="permission-list">
    <h1>权限列表</h1>
    <div class="admin-type-container">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="权限名称/代码/描述"
          size="mini"
          class="search-input"
        ></el-input>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置</el-button
        >
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="showNewPermissionDialog"
          >新增权限
        </el-button>
        <el-button type="danger" size="mini" @click="showDeletePermissionDialog"
          >批量删除
        </el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        label="序号"
        width="60"
        align="center"
        prop="id"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="权限名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="code"
        label="权限代码"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="permission_type"
        label="权限类型"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="权限描述"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="status" label="状态" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.status === 1 ? "启用" : "禁用" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updated_at"
        label="更新时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editPermission(scope.row)"
            >编辑
          </el-button>
          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deletePermission(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <el-dialog
      :title="permissionForm.id ? '编辑权限' : '新增权限'"
      :visible.sync="permissionDialogVisible"
      width="650px"
    >
      <el-form
        :model="permissionForm"
        :rules="permissionRules"
        ref="permissionFormRef"
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input
            v-model="permissionForm.name"
            placeholder="请输入权限名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="权限代码" prop="code">
          <el-input
            v-model="permissionForm.code"
            placeholder="请输入权限代码，如：user:read"
          ></el-input>
        </el-form-item>
        <el-form-item label="权限类型" prop="permission_type">
          <el-select
            v-model="permissionForm.permission_type"
            placeholder="请选择权限类型"
            style="width: 100%"
          >
            <el-option label="读取" value="read"></el-option>
            <el-option label="写入" value="write"></el-option>
            <el-option label="删除" value="delete"></el-option>
            <el-option label="管理" value="manage"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="permissionForm.status" :label="1">启用</el-radio>
          <el-radio v-model="permissionForm.status" :label="0">禁用</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePermission">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "PermissionList",
  data() {
    return {
      title: "权限列表",
      searchKeyword: "",
      permissionDialogVisible: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      permissionForm: {
        name: "",
        code: "",
        permission_type: "",
        description: "",
        status: 1,
      },
      permissionRules: {
        name: [{ required: true, message: "请输入权限名称", trigger: "blur" }],
        code: [{ required: true, message: "请输入权限代码", trigger: "blur" }],
        permission_type: [
          { required: true, message: "请选择权限类型", trigger: "change" },
        ],
        description: [
          { required: true, message: "请输入权限描述", trigger: "blur" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
    };
  },
  created() {
    this.fetchPermissionList();
  },
  methods: {
    // 获取权限列表
    fetchPermissionList() {
      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }

      requestApi({
        name: "getPermissionList",
        data: params,
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取权限列表失败");
          }
        })
        .catch((error) => {
          console.error("获取权限列表失败", error);
          this.$message.error("获取权限列表失败");
        });
    },

    // 检索权限列表
    search() {
      this.pagination.page = 1;
      this.fetchPermissionList();
    },

    resetSearch() {
      this.searchKeyword = "";
      this.pagination.page = 1;
      this.fetchPermissionList();
    },

    // 多选操作
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 跳转到指定页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchPermissionList();
    },

    // 改变每页显示条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchPermissionList();
    },

    // 显示删除对话框
    showDeletePermissionDialog() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一条记录");
        return;
      }

      this.$confirm("确认删除选中的记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const ids = this.multipleSelection.map((item) => item.id);

          requestApi({
            name: "deletePermission",
            data: { ids },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchPermissionList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 编辑权限
    editPermission(row) {
      requestApi({
        name: "getPermissionDetail",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.permissionForm = { ...response.data };
            this.permissionDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取权限详情失败");
          }
        })
        .catch((error) => {
          console.error("获取权限详情失败", error);
          this.$message.error("获取权限详情失败");
        });
    },

    // 权限状态切换
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;

      requestApi({
        name: "updatePermissionStatus",
        data: {
          permission_ids: [row.id],
          status: newStatus,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            // 使用 this.$set 确保更新能被响应式系统检测到
            const index = this.tableData.findIndex(
              (item) => item.id === row.id
            );
            if (index !== -1) {
              this.$set(this.tableData[index], "status", newStatus);
            }
            let newStatusStr = newStatus === 1 ? "启用" : "禁用";
            this.$message.success(`状态修改为${newStatusStr}成功`);
          } else {
            this.$message.error(response.message || "状态修改失败");
          }
        })
        .catch((error) => {
          console.error("状态修改失败", error);
          this.$message.error("状态修改失败");
        });
    },

    // 删除权限
    deletePermission(row) {
      this.$confirm("确认删除该权限?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "deletePermission",
            data: {
              ids: [row.id],
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchPermissionList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 保存权限变更
    savePermission() {
      this.$refs.permissionFormRef.validate((valid) => {
        if (!valid) {
          return false;
        }

        const methodName = this.permissionForm.id
          ? "updatePermission"
          : "addPermission";

        // 保存完整的原始表单数据，防止提交失败后数据丢失
        const originalForm = { ...this.permissionForm };

        // 标记保存是否成功的变量
        let saveSuccess = false;

        // 发送请求
        requestApi({
          name: methodName,
          data: this.permissionForm,
        })
          .then((response) => {
            if (response && response.code === 200) {
              this.$message.success("保存成功");
              saveSuccess = true;
              this.fetchPermissionList();
            } else {
              this.$message.error(response.message || "保存失败");
              // 保留原始表单数据，防止失败后数据丢失
              this.permissionForm = originalForm;
            }
          })
          .catch((error) => {
            console.error("保存失败", error);
            this.$message.error("保存失败");
            // 保留原始表单数据，防止失败后数据丢失
            this.permissionForm = originalForm;
          })
          .finally(() => {
            // 只在保存成功时关闭对话框
            if (saveSuccess) {
              this.permissionDialogVisible = false;
            }
          });
      });
    },

    // 显示新增权限对话框
    showNewPermissionDialog() {
      this.permissionForm = {
        name: "",
        code: "",
        permission_type: "",
        description: "",
        status: 1,
      };
      this.permissionDialogVisible = true;
      // 清除验证状态
      this.$nextTick(() => {
        this.$refs.permissionFormRef &&
          this.$refs.permissionFormRef.clearValidate();
      });
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "-";
      const d = new Date(date);
      return d.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },
  },
};
</script>

<style scoped lang="less">
.permission-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.admin-type-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  gap: 15px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

h1 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
