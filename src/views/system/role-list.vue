<template>
  <div class="role-list">
    <h1>角色列表</h1>
    <div class="admin-type-container">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="角色名称/描述"
          size="mini"
          class="search-input"
        ></el-input>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置</el-button
        >
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="showNewRoleDialog"
          >新增角色
        </el-button>
        <el-button type="danger" size="mini" @click="showDeleteRoleDialog"
          >批量删除
        </el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        label="序号"
        width="60"
        align="center"
        prop="id"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="角色名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="角色描述"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="status" label="状态" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.status === 1 ? "启用" : "禁用" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updated_at"
        label="更新时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editRole(scope.row)"
            >编辑
          </el-button>
          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="managePermissions(scope.row)"
            >管理权限
          </el-button>
          <el-button type="text" size="small" @click="deleteRole(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <el-dialog
      :title="roleForm.id ? '编辑角色' : '新增角色'"
      :visible.sync="roleDialogVisible"
      width="650px"
    >
      <el-form
        :model="roleForm"
        :rules="roleRules"
        ref="roleFormRef"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="roleForm.name"
            placeholder="请输入角色名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="roleForm.status" :label="1">启用</el-radio>
          <el-radio v-model="roleForm.status" :label="0">禁用</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRole">确认</el-button>
      </div>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      title="管理权限"
      :visible.sync="permissionManageDialogVisible"
      width="90%"
    >
      <div class="permission-manage-container">
        <div class="current-role-info">
          <h3>角色：{{ currentRole.name }}</h3>
          <p v-if="currentRole.description">{{ currentRole.description }}</p>
          <p class="tips">
            <i class="el-icon-info"></i>
            可以通过点击"可分配权限"中的权限标签来添加权限，或通过点击"当前权限"中权限标签的删除按钮来移除权限
          </p>
        </div>

        <div class="permission-sections" v-loading="permissionLoading">
          <!-- 当前权限 -->
          <div class="current-permissions">
            <h4>当前权限</h4>
            <div class="permission-list">
              <el-tag
                v-for="permission in currentRolePermissions"
                :key="permission.id"
                closable
                @close="removePermission(permission)"
                class="permission-tag"
              >
                {{ permission.name }}
              </el-tag>
              <span
                v-if="currentRolePermissions.length === 0 && !permissionLoading"
                class="no-permissions"
                >暂无权限</span
              >
            </div>
          </div>

          <!-- 可分配权限 -->
          <div class="available-permissions">
            <h4>可分配权限</h4>
            <div class="permission-list">
              <el-tag
                v-for="permission in availablePermissions"
                :key="permission.id"
                @click="addPermission(permission)"
                class="permission-tag clickable"
                type="info"
              >
                {{ permission.name }}
                <i class="el-icon-plus"></i>
              </el-tag>
              <span
                v-if="availablePermissions.length === 0 && !permissionLoading"
                class="no-permissions"
                >暂无可分配权限</span
              >
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionManageDialogVisible = false"
          >关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "RoleList",
  data() {
    return {
      title: "角色列表",
      searchKeyword: "",
      roleDialogVisible: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      roleForm: {
        name: "",
        description: "",
        status: 1,
      },
      roleRules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
        description: [
          { required: true, message: "请输入角色描述", trigger: "blur" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
      permissionManageDialogVisible: false,
      currentRole: {},
      currentRolePermissions: [],
      availablePermissions: [],
      permissionLoading: false,
    };
  },
  created() {
    this.fetchRoleList();
  },
  methods: {
    // 获取角色列表
    fetchRoleList() {
      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }

      requestApi({
        name: "getRoleList",
        data: params,
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取角色列表失败");
          }
        })
        .catch((error) => {
          console.error("获取角色列表失败", error);
          this.$message.error("获取角色列表失败");
        });
    },

    // 检索角色列表
    search() {
      this.pagination.page = 1;
      this.fetchRoleList();
    },

    resetSearch() {
      this.searchKeyword = "";
      this.pagination.page = 1;
      this.fetchRoleList();
    },

    // 多选操作
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 跳转到指定页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchRoleList();
    },

    // 改变每页显示条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchRoleList();
    },

    // 显示删除对话框
    showDeleteRoleDialog() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一条记录");
        return;
      }

      this.$confirm("确认删除选中的记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const ids = this.multipleSelection.map((item) => item.id);

          requestApi({
            name: "deleteRole",
            data: { ids },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchRoleList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 编辑角色
    editRole(row) {
      requestApi({
        name: "getRoleDetail",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.roleForm = { ...response.data };
            this.roleDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取角色详情失败");
          }
        })
        .catch((error) => {
          console.error("获取角色详情失败", error);
          this.$message.error("获取角色详情失败");
        });
    },

    // 角色状态切换
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;

      requestApi({
        name: "updateRoleStatus",
        data: {
          role_ids: [row.id],
          status: newStatus,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            // 使用 this.$set 确保更新能被响应式系统检测到
            const index = this.tableData.findIndex(
              (item) => item.id === row.id
            );
            if (index !== -1) {
              this.$set(this.tableData[index], "status", newStatus);
            }
            let newStatusStr = newStatus === 1 ? "启用" : "禁用";
            this.$message.success(`状态修改为${newStatusStr}成功`);
          } else {
            this.$message.error(response.message || "状态修改失败");
          }
        })
        .catch((error) => {
          console.error("状态修改失败", error);
          this.$message.error("状态修改失败");
        });
    },

    // 删除角色
    deleteRole(row) {
      this.$confirm("确认删除该角色?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "deleteRole",
            data: {
              ids: [row.id],
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchRoleList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 保存角色变更
    saveRole() {
      this.$refs.roleFormRef.validate((valid) => {
        if (!valid) {
          return false;
        }

        const methodName = this.roleForm.id ? "updateRole" : "addRole";

        // 保存完整的原始表单数据，防止提交失败后数据丢失
        const originalForm = { ...this.roleForm };

        // 标记保存是否成功的变量
        let saveSuccess = false;

        // 发送请求
        requestApi({
          name: methodName,
          data: this.roleForm,
        })
          .then((response) => {
            if (response && response.code === 200) {
              this.$message.success("保存成功");
              saveSuccess = true;
              this.fetchRoleList();
            } else {
              this.$message.error(response.message || "保存失败");
              // 保留原始表单数据，防止失败后数据丢失
              this.roleForm = originalForm;
            }
          })
          .catch((error) => {
            console.error("保存失败", error);
            this.$message.error("保存失败");
            // 保留原始表单数据，防止失败后数据丢失
            this.roleForm = originalForm;
          })
          .finally(() => {
            // 只在保存成功时关闭对话框
            if (saveSuccess) {
              this.roleDialogVisible = false;
            }
          });
      });
    },

    // 显示新增角色对话框
    showNewRoleDialog() {
      this.roleForm = {
        name: "",
        description: "",
        status: 1,
      };
      this.roleDialogVisible = true;
      // 清除验证状态
      this.$nextTick(() => {
        this.$refs.roleFormRef && this.$refs.roleFormRef.clearValidate();
      });
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "-";
      const d = new Date(date);
      return d.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },

    // 管理权限
    managePermissions(row) {
      this.currentRole = row;
      this.permissionManageDialogVisible = true;
      // 先清空数据，避免显示上一次的数据
      this.currentRolePermissions = [];
      this.availablePermissions = [];
      this.permissionLoading = true;

      this.fetchCurrentRolePermissions().then(() => {
        this.fetchAvailablePermissions();
      });
    },

    // 获取当前角色的权限
    fetchCurrentRolePermissions() {
      return requestApi({
        name: "getRolePermissions",
        data: {
          roleId: this.currentRole.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.currentRolePermissions = response.data || [];
          } else {
            this.$message.error(response.message || "获取角色权限失败");
          }
        })
        .catch((error) => {
          console.error("获取角色权限失败", error);
          this.$message.error("获取角色权限失败");
        });
    },

    // 获取可分配的权限
    fetchAvailablePermissions() {
      requestApi({
        name: "getPermissionList",
        data: {
          page: 1,
          pageSize: 1000, // 设置较大的页面大小以获取所有权限
          keyword: "", // 不设置关键字以获取所有权限
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            const allPermissions = response.data.list || [];
            // 过滤掉当前角色已有的权限
            this.availablePermissions = allPermissions.filter(
              (permission) =>
                !this.currentRolePermissions.some((p) => p.id === permission.id)
            );
          } else {
            this.$message.error(response.message || "获取可分配权限失败");
          }
        })
        .catch((error) => {
          console.error("获取可分配权限失败", error);
          this.$message.error("获取可分配权限失败");
        })
        .finally(() => {
          this.permissionLoading = false;
        });
    },

    // 添加权限
    addPermission(permission) {
      requestApi({
        name: "addRolePermission",
        data: {
          roleId: this.currentRole.id,
          permissionId: permission.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("权限添加成功");
            // 直接更新本地数据，提高用户体验
            this.currentRolePermissions.push(permission);
            this.availablePermissions = this.availablePermissions.filter(
              (p) => p.id !== permission.id
            );
          } else {
            this.$message.error(response.message || "添加权限失败");
          }
        })
        .catch((error) => {
          console.error("添加权限失败", error);
          this.$message.error("添加权限失败");
        });
    },

    // 移除权限
    removePermission(permission) {
      this.$confirm("确认移除该权限?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "removeRolePermission",
            data: {
              roleId: this.currentRole.id,
              permissionId: permission.id,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("权限移除成功");
                // 直接更新本地数据，提高用户体验
                this.currentRolePermissions =
                  this.currentRolePermissions.filter(
                    (p) => p.id !== permission.id
                  );
                this.availablePermissions.push(permission);
              } else {
                this.$message.error(response.message || "移除权限失败");
              }
            })
            .catch((error) => {
              console.error("移除权限失败", error);
              this.$message.error("移除权限失败");
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="less">
.role-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.admin-type-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  gap: 15px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

h1 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

/* 权限管理对话框样式 */
.permission-manage-container {
  padding: 10px 0;
}

.current-role-info {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.current-role-info h3 {
  color: #303133;
  font-size: 16px;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.current-role-info p {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.current-role-info .tips {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.current-role-info .tips i {
  margin-right: 4px;
  color: #409eff;
}

.permission-sections {
  display: flex;
  gap: 40px;
}

.current-permissions,
.available-permissions {
  flex: 1;
}

.current-permissions h4,
.available-permissions h4 {
  color: #606266;
  font-size: 14px;
  margin: 0 0 15px 0;
  font-weight: 500;
}

.permission-list {
  min-height: 60px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
  align-content: flex-start;
}

.permission-tag {
  margin: 0 !important;
}

.permission-tag.clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.permission-tag.clickable:hover {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.permission-tag.clickable .el-icon-plus {
  margin-left: 4px;
}

.no-permissions {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

@media (max-width: 768px) {
  .permission-sections {
    flex-direction: column;
    gap: 20px;
  }
}
</style>
