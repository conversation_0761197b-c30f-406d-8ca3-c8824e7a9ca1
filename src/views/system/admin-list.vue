<template>
  <div class="admin-list">
    <h1>管理员列表</h1>
    <div class="admin-type-container">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="用户名/姓名/手机号"
          size="mini"
          class="search-input"
        ></el-input>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置</el-button
        >
      </div>
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="showNewAdminDialog"
          >新增管理员
        </el-button>
        <el-button type="danger" size="mini" @click="showDeleteAdminDialog"
          >批量删除
        </el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        label="序号"
        width="60"
        align="center"
        prop="id"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="姓名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="username"
        label="用户名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="phone"
        label="手机号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="email"
        label="邮箱"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="状态" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.status === 1 ? "启用" : "禁用" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="last_login_time"
        label="最后登录"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.last_login_time) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editAdmin(scope.row)"
            >编辑
          </el-button>
          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}
          </el-button>
          <el-button type="text" size="small" @click="manageRoles(scope.row)"
            >管理角色
          </el-button>
          <el-button type="text" size="small" @click="deleteAdmin(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <el-dialog
      :title="adminForm.id ? '编辑管理员' : '新增管理员'"
      :visible.sync="adminDialogVisible"
      width="650px"
    >
      <el-form
        :model="adminForm"
        :rules="adminRules"
        ref="adminFormRef"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="adminForm.name"
            placeholder="请输入姓名"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="adminForm.username"
            placeholder="请输入用户名"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="adminForm.phone"
            placeholder="请输入手机号"
          ></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="adminForm.email"
            placeholder="请输入邮箱"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!adminForm.id">
          <el-input
            v-model="adminForm.password"
            type="password"
            placeholder="请输入密码"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="adminForm.status" :label="1">启用</el-radio>
          <el-radio v-model="adminForm.status" :label="0">禁用</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adminDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAdmin">确认</el-button>
      </div>
    </el-dialog>

    <!-- 角色管理对话框 -->
    <el-dialog
      title="管理角色"
      :visible.sync="roleManageDialogVisible"
      width="800px"
    >
      <div class="role-manage-container">
        <div class="current-admin-info">
          <h3>管理员：{{ currentAdmin.name }} ({{ currentAdmin.username }})</h3>
        </div>

        <div class="role-sections">
          <!-- 当前角色 -->
          <div class="current-roles">
            <h4>当前角色</h4>
            <div class="role-list">
              <el-tag
                v-for="role in currentAdminRoles"
                :key="role.id"
                closable
                @close="removeRole(role)"
                class="role-tag"
              >
                {{ role.name }}
              </el-tag>
              <span v-if="currentAdminRoles.length === 0" class="no-roles"
                >暂无角色</span
              >
            </div>
          </div>

          <!-- 可分配角色 -->
          <div class="available-roles">
            <h4>可分配角色</h4>
            <div class="role-list">
              <el-tag
                v-for="role in availableRoles"
                :key="role.id"
                @click="addRole(role)"
                class="role-tag clickable"
                type="info"
              >
                {{ role.name }}
                <i class="el-icon-plus"></i>
              </el-tag>
              <span v-if="availableRoles.length === 0" class="no-roles"
                >暂无可分配角色</span
              >
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleManageDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "AdminList",
  data() {
    return {
      title: "管理员列表",
      searchKeyword: "",
      adminDialogVisible: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      adminForm: {
        name: "",
        username: "",
        phone: "",
        email: "",
        password: "",
        status: 1,
      },
      adminRules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号",
            trigger: "blur",
          },
        ],
        email: [
          { required: true, message: "请输入邮箱", trigger: "blur" },
          { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
      roleManageDialogVisible: false,
      currentAdmin: {},
      currentAdminRoles: [],
      availableRoles: [],
    };
  },
  created() {
    this.fetchAdminList();
  },
  methods: {
    // 获取管理员列表
    fetchAdminList() {
      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }

      requestApi({
        name: "getAdminList",
        data: params,
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取管理员列表失败");
          }
        })
        .catch((error) => {
          console.error("获取管理员列表失败", error);
          this.$message.error("获取管理员列表失败");
        });
    },

    // 检索管理员列表
    search() {
      this.pagination.page = 1;
      this.fetchAdminList();
    },

    resetSearch() {
      this.searchKeyword = "";
      this.pagination.page = 1;
      this.fetchAdminList();
    },

    // 多选操作
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 跳转到指定页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchAdminList();
    },

    // 改变每页显示条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchAdminList();
    },

    // 显示删除对话框
    showDeleteAdminDialog() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一条记录");
        return;
      }

      this.$confirm("确认删除选中的记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const ids = this.multipleSelection.map((item) => item.id);

          requestApi({
            name: "deleteAdmin",
            data: { ids },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchAdminList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 编辑管理员
    editAdmin(row) {
      requestApi({
        name: "getAdminDetail",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.adminForm = { ...response.data };
            // 编辑时不需要密码字段
            delete this.adminForm.password;
            this.adminDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取管理员详情失败");
          }
        })
        .catch((error) => {
          console.error("获取管理员详情失败", error);
          this.$message.error("获取管理员详情失败");
        });
    },

    // 管理员状态切换
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;

      requestApi({
        name: "updateAdminStatus",
        data: {
          admin_ids: [row.id],
          status: newStatus,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            // 使用 this.$set 确保更新能被响应式系统检测到
            const index = this.tableData.findIndex(
              (item) => item.id === row.id
            );
            if (index !== -1) {
              this.$set(this.tableData[index], "status", newStatus);
            }
            let newStatusStr = newStatus === 1 ? "启用" : "禁用";
            this.$message.success(`状态修改为${newStatusStr}成功`);
          } else {
            this.$message.error(response.message || "状态修改失败");
          }
        })
        .catch((error) => {
          console.error("状态修改失败", error);
          this.$message.error("状态修改失败");
        });
    },

    // 删除管理员
    deleteAdmin(row) {
      this.$confirm("确认删除该管理员?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "deleteAdmin",
            data: {
              ids: [row.id],
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchAdminList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 保存管理员变更
    saveAdmin() {
      this.$refs.adminFormRef.validate((valid) => {
        if (!valid) {
          return false;
        }

        const methodName = this.adminForm.id ? "updateAdmin" : "addAdmin";

        // 保存完整的原始表单数据，防止提交失败后数据丢失
        const originalForm = { ...this.adminForm };

        // 标记保存是否成功的变量
        let saveSuccess = false;

        // 发送请求
        requestApi({
          name: methodName,
          data: this.adminForm,
        })
          .then((response) => {
            if (response && response.code === 200) {
              this.$message.success("保存成功");
              saveSuccess = true;
              this.fetchAdminList();
            } else {
              this.$message.error(response.message || "保存失败");
              // 保留原始表单数据，防止失败后数据丢失
              this.adminForm = originalForm;
            }
          })
          .catch((error) => {
            console.error("保存失败", error);
            this.$message.error("保存失败");
            // 保留原始表单数据，防止失败后数据丢失
            this.adminForm = originalForm;
          })
          .finally(() => {
            // 只在保存成功时关闭对话框
            if (saveSuccess) {
              this.adminDialogVisible = false;
            }
          });
      });
    },

    // 显示新增管理员对话框
    showNewAdminDialog() {
      this.adminForm = {
        name: "",
        username: "",
        phone: "",
        email: "",
        password: "",
        status: 1,
      };
      this.adminDialogVisible = true;
      // 清除验证状态
      this.$nextTick(() => {
        this.$refs.adminFormRef && this.$refs.adminFormRef.clearValidate();
      });
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "-";
      const d = new Date(date);
      return d.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },

    // 管理角色
    manageRoles(row) {
      this.currentAdmin = row;
      this.fetchCurrentAdminRoles();
      this.fetchAvailableRoles();
      this.roleManageDialogVisible = true;
    },

    // 获取当前管理员的角色
    fetchCurrentAdminRoles() {
      requestApi({
        name: "getAdminRoles",
        data: {
          adminId: this.currentAdmin.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.currentAdminRoles = response.data || [];
          } else {
            this.$message.error(response.message || "获取管理员角色失败");
          }
        })
        .catch((error) => {
          console.error("获取管理员角色失败", error);
          this.$message.error("获取管理员角色失败");
        });
    },

    // 获取可分配的角色
    fetchAvailableRoles() {
      requestApi({
        name: "getRoleList",
        data: {},
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.availableRoles = response.data.list || [];
          } else {
            this.$message.error(response.message || "获取可分配角色失败");
          }
        })
        .catch((error) => {
          console.error("获取可分配角色失败", error);
          this.$message.error("获取可分配角色失败");
        });
    },

    // 添加角色
    addRole(role) {
      requestApi({
        name: "addAdminRole",
        data: {
          adminId: this.currentAdmin.id,
          roleId: role.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("角色添加成功");
            this.fetchCurrentAdminRoles();
            this.fetchAvailableRoles();
          } else {
            this.$message.error(response.message || "添加角色失败");
          }
        })
        .catch((error) => {
          console.error("添加角色失败", error);
          this.$message.error("添加角色失败");
        });
    },

    // 移除角色
    removeRole(role) {
      this.$confirm("确认移除该角色?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "removeAdminRole",
            data: {
              adminId: this.currentAdmin.id,
              roleId: role.id,
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("角色移除成功");
                this.fetchCurrentAdminRoles();
                this.fetchAvailableRoles();
              } else {
                this.$message.error(response.message || "移除角色失败");
              }
            })
            .catch((error) => {
              console.error("移除角色失败", error);
              this.$message.error("移除角色失败");
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="less">
.admin-list {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.admin-type-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  gap: 15px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

h1 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}

/* 角色管理对话框样式 */
.role-manage-container {
  padding: 10px 0;
}

.current-admin-info {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.current-admin-info h3 {
  color: #303133;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.role-sections {
  display: flex;
  gap: 40px;
}

.current-roles,
.available-roles {
  flex: 1;
}

.current-roles h4,
.available-roles h4 {
  color: #606266;
  font-size: 14px;
  margin: 0 0 15px 0;
  font-weight: 500;
}

.role-list {
  min-height: 60px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
  align-content: flex-start;
}

.role-tag {
  margin: 0 !important;
}

.role-tag.clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.role-tag.clickable:hover {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.role-tag.clickable .el-icon-plus {
  margin-left: 4px;
}

.no-roles {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

@media (max-width: 768px) {
  .role-sections {
    flex-direction: column;
    gap: 20px;
  }
}
</style>
