<template>
  <div class="menu-warehouse">
    <!-- 头部菜单 -->
    <h1>菜品库</h1>
    <div class="menu-type-container">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="菜品名称"
          size="mini"
          class="search-input"
        ></el-input>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <el-button type="primary" size="mini" @click="resetSearch"
          >重置</el-button
        >
      </div>
      <div class="left-buttons">
        <!--        <el-button type="primary" size="mini" @click="showNewTypeDialog"-->
        <!--        >新增分类-->
        <!--        </el-button>-->
        <el-button type="primary" size="mini" @click="showNewMenuDialog"
          >新增菜品
        </el-button>
        <el-button type="danger" size="mini" @click="showDeleteTypeDialog"
          >批量删除
        </el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 菜品列表 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        label="序号"
        width="60"
        align="center"
        prop="id"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="菜品名称"
        align="center"
      ></el-table-column>
      <el-table-column prop="content" label="菜品描述" align="center">
        <template slot-scope="scope">
          {{ scope.row.content | truncate }}
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--          prop="menuType"-->
      <!--          label="菜品分类"-->
      <!--          align="center"-->
      <!--          width="80"-->
      <!--      ></el-table-column>-->
      <el-table-column label="菜品图片" width="100" align="center">
        <template slot-scope="scope">
          <div class="menu-image">
            <img :src="scope.row.thumbnail" alt="菜品图片" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="菜品状态" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.status === 1 ? "上架" : "下架" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editMenu(scope.row)"
            >编辑
          </el-button>
          <el-button type="text" size="small" @click="toggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "下架" : "上架" }}
          </el-button>
          <el-button type="text" size="small" @click="deleteMenu(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 菜品列表结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 新增分类对话框 -->
    <!--    <el-dialog-->
    <!--      title="新增菜品分类"-->
    <!--      :visible.sync="typeDialogVisible"-->
    <!--      width="500px"-->
    <!--    >-->
    <!--      <el-form :model="typeForm" label-width="100px">-->
    <!--        <el-form-item label="菜品类型名称">-->
    <!--          <el-input-->
    <!--            v-model="typeForm.typeName"-->
    <!--            placeholder="请输入菜品类型名称"-->
    <!--          ></el-input>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="菜品类型图标">-->
    <!--          <div class="upload-container">-->
    <!--            <el-upload-->
    <!--              class="avatar-uploader"-->
    <!--              action="/upload"-->
    <!--              :show-file-list="false"-->
    <!--              :on-success="handleTypeAvatarSuccess"-->
    <!--              :before-upload="beforeUploadFile"-->
    <!--              :http-request="customTypeUploadRequest"-->
    <!--            >-->
    <!--              <img-->
    <!--                v-if="typeForm.image"-->
    <!--                :src="typeForm.image"-->
    <!--                class="avatar"-->
    <!--                alt="菜品类型图标"-->
    <!--              />-->
    <!--              <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
    <!--            </el-upload>-->
    <!--          </div>-->
    <!--        </el-form-item>-->
    <!--      </el-form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button @click="typeDialogVisible = false">取消</el-button>-->
    <!--        <el-button type="primary" @click="saveType">确认</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->
    <!-- 新增分类对话框结束 -->

    <!-- 新增菜品对话框 -->
    <el-dialog title="新增菜品" :visible.sync="menuDialogVisible" width="650px">
      <el-form :model="menuForm" label-width="100px">
        <el-form-item label="菜品名称">
          <el-input
            v-model="menuForm.name"
            placeholder="请输入菜品名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜品描述">
          <el-input
            v-model="menuForm.content"
            placeholder="请输入菜品描述"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
          ></el-input>
        </el-form-item>
        <!--        <el-form-item label="菜品类型">-->
        <!--          <el-select-->
        <!--              v-model="menuForm.menuType"-->
        <!--              placeholder="请选择菜品类型"-->
        <!--              style="width: 100%"-->
        <!--          >-->
        <!--            <el-option-->
        <!--                v-for="item in categoryOptions"-->
        <!--                :key="item.id"-->
        <!--                :label="item.typeName"-->
        <!--                :value="item.id"-->
        <!--            >-->
        <!--            </el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="菜品状态">
          <el-radio v-model="menuForm.status" :label="1">上架</el-radio>
          <el-radio v-model="menuForm.status" :label="0">下架</el-radio>
        </el-form-item>
        <el-form-item label="菜品图片">
          <div class="upload-container">
            <el-upload
              class="avatar-uploader"
              action="/upload"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeUploadFile"
              :http-request="customUploadRequest"
            >
              <img
                v-if="menuForm.image"
                :src="menuForm.image"
                class="avatar"
                alt="菜品图片"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="menuDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMenu">确认</el-button>
      </div>
    </el-dialog>
    <!-- 新增菜品对话框结束 -->
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";

export default {
  name: "MenuWarehouse",
  data() {
    return {
      title: "菜品库",
      searchKeyword: "",
      typeDialogVisible: false,
      menuDialogVisible: false,
      tableData: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      typeForm: {
        typeName: "",
        image: "",
      },
      menuForm: {
        name: "",
        content: "",
        status: 1,
        image: "",
      },
      categoryOptions: [],
    };
  },
  filters: {
    truncate(value) {
      if (!value) return "";
      if (value.length <= 20) {
        return value;
      }
      return value.slice(0, 30) + "...";
    },
  },
  created() {
    this.fetchDishList();
    // this.fetchCategoryList();
  },
  methods: {
    // 获取菜品列表
    fetchDishList() {
      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }

      requestApi({
        name: "getDishListBySearch",
        data: params,
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.$message.error(response.message || "获取菜品列表失败");
          }
        })
        .catch((error) => {
          console.error("获取菜品列表失败", error);
          this.$message.error("获取菜品列表失败");
        });
    },

    // 获取菜品分类列表
    fetchCategoryList() {
      requestApi({
        name: "getDishCategoryList",
        data: {},
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.categoryOptions = response.data || [];
          } else {
            this.$message.error(response.message || "获取菜品分类失败");
          }
        })
        .catch((error) => {
          console.error("获取菜品分类失败", error);
          this.$message.error("获取菜品分类失败");
        });
    },

    // 检索菜品列表
    search() {
      this.pagination.page = 1;
      this.fetchDishList();
    },
    resetSearch() {
      this.searchKeyword = "";
      this.pagination.page = 1;
      this.fetchDishList();
    },
    // 多选操作
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 跳转到指定页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchDishList();
    },

    // 改变每页显示条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchDishList();
    },

    showNewTypeDialog() {
      this.typeForm = {
        typeName: "",
        image: "",
      };
      this.typeDialogVisible = true;
    },

    // 显示删除对话框
    showDeleteTypeDialog() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请至少选择一条记录");
        return;
      }

      this.$confirm("确认删除选中的记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const ids = this.multipleSelection.map((item) => item.id);

          requestApi({
            name: "deleteDish",
            data: { ids },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchDishList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    // 编辑菜品
    editMenu(row) {
      requestApi({
        name: "getDishDetail",
        data: {
          id: row.id,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.menuForm = response.data || {};
            // 确保使用image字段
            if (!this.menuForm.image && this.menuForm.imageUrl) {
              this.menuForm.image = this.menuForm.imageUrl;
            }
            this.menuDialogVisible = true;
          } else {
            this.$message.error(response.message || "获取菜品详情失败");
          }
        })
        .catch((error) => {
          console.error("获取菜品详情失败", error);
          this.$message.error("获取菜品详情失败");
        });
    },

    // 菜品上下架
    toggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;

      requestApi({
        name: "updateDishStatus",
        data: {
          content_ids: [row.id],
          status: newStatus,
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            // 使用 this.$set 确保更新能被响应式系统检测到
            const index = this.tableData.findIndex(
              (item) => item.id === row.id
            );
            if (index !== -1) {
              this.$set(this.tableData[index], "status", newStatus);
            }
            let newStatusStr = newStatus === 1 ? "上架" : "下架";
            this.$message.success(`状态修改为${newStatusStr}成功`);
          } else {
            this.$message.error(response.message || "状态修改失败");
          }
        })
        .catch((error) => {
          console.error("状态修改失败", error);
          this.$message.error("状态修改失败");
        });
    },

    // 删除菜品
    deleteMenu(row) {
      this.$confirm("确认删除该菜品?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          requestApi({
            name: "deleteDish",
            data: {
              ids: [row.id],
            },
          })
            .then((response) => {
              if (response && response.code === 200) {
                this.$message.success("删除成功");
                this.fetchDishList();
              } else {
                this.$message.error(response.message || "删除失败");
              }
            })
            .catch((error) => {
              console.error("删除失败", error);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {});
    },

    handleTypeAvatarSuccess(res) {
      if (res && res.code === 200) {
        this.typeForm.image = res.data.url;
        // 保存实际上传的文件对象，以便在保存时直接使用
        this.typeForm.imageFile = res.data.originalFile;
      } else {
        this.$message.error(res.message || "上传图片失败");
      }
    },

    handleAvatarSuccess(res) {
      if (res && res.code === 200) {
        this.menuForm.image = res.data.url;
        // 保存实际上传的文件对象，以便在保存时直接使用
        this.menuForm.imageFile = res.data.originalFile;
      } else {
        this.$message.error(res.message || "上传图片失败");
      }
    },

    // saveType() {
    //   if (!this.typeForm.typeName) {
    //     this.$message.warning("请输入菜品类型名称");
    //     return;
    //   }
    //
    //   const methodName = this.typeForm.id
    //     ? "updateDishCategory"
    //     : "addDishCategory";
    //
    //   // 创建FormData对象
    //   const formData = new FormData();
    //
    //   // 添加基本信息
    //   formData.append("typeName", this.typeForm.typeName);
    //
    //   // 如果是编辑模式，添加ID
    //   if (this.typeForm.id) {
    //     formData.append("id", this.typeForm.id);
    //   }
    //
    //   // 图片处理
    //   if (this.typeForm.imageFile) {
    //     formData.append("image_file", this.typeForm.imageFile);
    //   } else if (this.typeForm.image) {
    //     formData.append("image", this.typeForm.image);
    //   }
    //
    //   requestApi({
    //     name: methodName,
    //     data: formData,
    //     headers: {
    //       "Content-Type": "multipart/form-data",
    //     },
    //   })
    //     .then((response) => {
    //       if (response && response.code === 200) {
    //         this.$message.success("保存成功");
    //         this.typeDialogVisible = false;
    //         this.fetchCategoryList();
    //       } else {
    //         this.$message.error(response.message || "保存失败");
    //       }
    //     })
    //     .catch((error) => {
    //       console.error("保存失败", error);
    //       this.$message.error("保存失败");
    //     });
    // },

    // 保存菜品变更
    saveMenu() {
      if (!this.menuForm.name) {
        this.$message.warning("请输入菜品名称");
        return;
      }

      const methodName = this.menuForm.id ? "updateDish" : "addDish";

      // 保存完整的原始表单数据，防止提交失败后数据丢失
      const originalForm = { ...this.menuForm };

      // 标记保存是否成功的变量
      let saveSuccess = false;

      // 创建FormData对象
      const formData = new FormData();
      if (this.menuForm.id) {
        // 创建一个普通对象，仅包含ID用于URL替换
        formData.append("id", this.menuForm.id);
      }
      formData.append("name", this.menuForm.name);
      formData.append("content", this.menuForm.content || "");
      formData.append("status", this.menuForm.status);


      // 图片处理
      if (this.menuForm.imageFile) {
        formData.append("image_file", this.menuForm.imageFile);
      } else if (this.menuForm.image) {
        formData.append("image", this.menuForm.image);
      }

      // 发送请求
      requestApi({
        name: methodName,
        data: formData,
        // data: this.menuForm.id ? reqData : formData,
        // formData: formData, // 传递表单数据
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
        .then((response) => {
          if (response && response.code === 200) {
            this.$message.success("保存成功");
            saveSuccess = true;
            if (methodName === "updateDish") {
              // 保留原有的表单数据，但更新从服务器返回的数据
              this.menuForm = { ...response.data, id: originalForm.id };
            } else {
              this.menuForm = response.data || {};
            }
            this.fetchDishList();
          } else {
            this.$message.error(response.message || "保存失败");
            // 保留原始表单数据，防止失败后数据丢失
            this.menuForm = originalForm;
          }
        })
        .catch((error) => {
          console.error("保存失败", error);
          this.$message.error("保存失败");
          // 保留原始表单数据，防止失败后数据丢失
          this.menuForm = originalForm;
        })
        .finally(() => {
          // 只在保存成功时关闭对话框
          if (saveSuccess) {
            this.menuDialogVisible = false;
          }
          // 刷新列表页
          this.fetchDishList();
        });
    },

    // 显示新增菜品对话框
    showNewMenuDialog() {
      this.menuForm = {
        name: "",
        content: "",
        status: 1,
        image: "",
      };
      this.menuDialogVisible = true;
    },

    beforeUploadFile(file) {
      // 检查文件大小和类型
      const isJPG =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png";
      const isLtSize = file.size / 1024 / 1024 < 5; // 限制5MB

      if (!isJPG) {
        this.$message.error("上传图片必须是JPG/JPEG/PNG格式!");
      }
      if (!isLtSize) {
        this.$message.error("上传图片大小不能超过5MB!");
      }

      return isJPG && isLtSize;
    },

    customUploadRequest(options) {
      const { file } = options;

      // 直接模拟上传成功，将文件保存到menuForm中
      // 实际场景中可能需要先上传到服务器然后获取URL
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        // 模拟成功的响应
        const response = {
          code: 200,
          data: {
            url: reader.result, // 使用Base64作为预览
            originalFile: file, // 保存原始文件对象
          },
        };
        // 调用成功回调
        this.handleAvatarSuccess(response);
      };
    },

    customTypeUploadRequest(options) {
      const { file } = options;

      // 直接模拟上传成功，将文件保存到typeForm中
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        // 模拟成功的响应
        const response = {
          code: 200,
          data: {
            url: reader.result, // 使用Base64作为预览
            originalFile: file, // 保存原始文件对象
          },
        };
        // 调用成功回调
        this.handleTypeAvatarSuccess(response);
      };
    },
  },
};
</script>

<style lang="less" scoped>
.menu-warehouse {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.menu-type-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  gap: 15px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 180px;
}

.menu-image img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

h1 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
