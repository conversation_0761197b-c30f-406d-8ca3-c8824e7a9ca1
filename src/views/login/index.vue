<template>
  <div class="login-container">
    <!-- <div class="img" style="text-align: center;">
      <h3 style="font-size:24px;">洋海电子后台管理系统</h3>
      <img src="../../assets/images/denglu-bg.png" style="width:500px;height:500px;" alt="">
    </div> -->
    <div class="login-box">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        auto-complete="on"
        label-position="left"
      >
        <div class="title-container">
          <h3 class="title">{{ title }}</h3>
        </div>
        <div class="tags">
          <span :class="{ active: type == 1 }" @click="type = 1">账号登录</span>
          <span :class="{ active: type == 2 }" @click="type = 2">短信登录</span>
          <i></i>
        </div>
        <el-form-item prop="phone">
          <!--          <span class="svg-container">-->
          <!--            <svg-icon icon-class="user" />-->
          <!--          </span>-->
          <el-input
            ref="phone"
            v-model="loginForm.phone"
            placeholder="手机号码"
            name="phone"
            type="text"
            tabindex="1"
            maxlength="11"
            :autocomplete="'off'"
          />
        </el-form-item>
        <el-form-item prop="valid_code" v-if="type == 2">
          <el-input
            type="text"
            maxlength="6"
            v-model.trim="loginForm.valid_code"
            placeholder="请输入验证码"
            @keyup.enter.native="handleLogin"
          >
          </el-input>
          <el-button
            type="text"
            :style="{ color: time != 60 ? '#333' : '#333' }"
            :disabled="time != 60"
            @click="getCode(loginForm.phone)"
            >{{ buttonName }}
          </el-button>
        </el-form-item>
        <el-form-item prop="passwd" v-else>
          <el-input
            type="password"
            maxlength="30"
            v-model.trim="loginForm.passwd"
            placeholder="密码"
            @keyup.enter.native="handleLogin"
          >
          </el-input>
        </el-form-item>
        <el-form-item prop="company_id" v-if="loginForm.company_type === 1">
          <el-select
            v-model="loginForm.company_id"
            filterable
            placeholder="请选择企业"
            :loading="loading"
            style="width: 100%"
            @change="handleCompanyChange"
          >
            <el-option
              v-for="item in companies"
              :key="item.company_id"
              :label="item.company_name"
              :value="item.company_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item prop="company_type" class="company-type-container">-->
        <!--          <div class="radio-container">-->
        <!--            <el-radio :label="1" v-model="loginForm.company_type" @change="handleCompanyTypeChange" class="radio-left">-->
        <!--              <span class="radio-label">签约公司</span>-->
        <!--            </el-radio>-->
        <!--            <el-radio :label="2" v-model="loginForm.company_type" @change="handleCompanyTypeChange" class="radio-right">-->
        <!--              <span class="radio-label">乙禾公司</span>-->
        <!--            </el-radio>-->
        <!--          </div>-->
        <!--        </el-form-item>-->
        <el-button
          :loading="loading"
          type="primary"
          size="medium"
          style="width: 100%; margin-bottom: 30px"
          @click.native.prevent="handleLogin"
          >Login</el-button
        >
      </el-form>
    </div>
  </div>
</template>

<script>
// import Verification from './components/Verification'
import { requestApi } from "@/utils/request";
import defaultSettings from "@/settings";
import { phone } from "@/utils/validate";
const errorCodeOjb = {
  4001: "Token过期或无效，请重新登录",
  4002: "Token过期或无效，请重新登录",
};
let interval = null;
export default {
  name: "Login",
  components: {
    // Verification
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入用户名"));
      }
      callback();
    };
    return {
      time: 60,
      buttonName: "获取验证码",
      title: defaultSettings.title,
      loginForm: {
        phone: "", // 18664610408
        valid_code: "",
        passwd: "",
        company_id: "",
        company_type: 2, // 默认选择签约公司
      },
      loginRules: {
        phone: [{ required: true, trigger: "blur", validator: validatePhone }],
        valid_code: [
          { required: true, trigger: "blur", message: "请输入验证码" },
        ],
        passwd: [{ required: true, trigger: "blur", message: "请输入密码" }],
        company_id: [
          { required: true, trigger: "blur", message: "请选择企业" },
        ],
        company_type: [
          { required: true, trigger: "change", message: "请选择公司类型" },
        ],
      },
      type: 1,
      loading: false,
      redirect: undefined,
      companies: [],
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
    "loginForm.company_type": {
      handler: function (newVal) {
        // 当选择乙禾公司时，将企业ID设为0
        if (newVal === 2) {
          this.loginForm.company_id = 0;
        }
      },
    },
  },
  mounted() {
    // console.log("NODE_ENV---", process.env.NODE_ENV);
    const errorCode = this.$route.query.error_code;
    if (errorCode && errorCodeOjb[errorCode]) {
      // 有带错误码
      this.$message.error(errorCodeOjb[errorCode]);
    }
    // 加载企业列表
    // this.fetchCompanies();
  },
  methods: {
    // 获取企业列表
    fetchCompanies() {
      this.loading = true;
      requestApi({
        name: "getEnterpriseList",
        data: {},
      }).then((res) => {
        this.loading = false;
        if (res.code === 200) {
          this.companies = res.results || [];
        }
      });
    },
    // 获取验证码
    getCode(name) {
      let self = this;
      if (!phone(name)) {
        this.$message({
          message: "请输入正确的手机号",
          type: "error",
        });
        return;
      }
      requestApi({
        name: "sendValidateCode",
        data: {
          phone: this.loginForm.phone,
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$message({
            message: "验证码发送成功",
            type: "success",
          });

          interval = window.setInterval(() => {
            self.buttonName = "重新发送 " + self.time + "s";
            --self.time;
            if (self.time < 0) {
              self.buttonName = "获取验证码";
              self.time = 60;
              window.clearInterval(interval);
            }
          }, 1000);
        } else {
          // this.$message({
          //   message: res.msg,
          //   type: "error",
          // });
        }
      });
    },
    // 登录
    handleLogin() {
      const self = this;
      console.log("登录表单数据:", JSON.stringify(self.loginForm));
      self.$refs.loginForm.validate((valid) => {
        if (valid) {
          self.loading = true;
          const data = JSON.parse(JSON.stringify(self.loginForm));
          if (self.type == 2) {
            // 短信登录
            self.$store
              .dispatch("user/login", data)
              .then((res) => {
                if (res.code === 200) {
                  self.$message({
                    message: res.msg,
                    type: "success",
                  });
                  self.$router.push({ path: self.redirect || "/" });
                  self.loading = false;
                }
              })
              .catch((err) => {
                self.loading = false;
              });
          } else {
            // 账号登录
            // 使用URLSearchParams来创建x-www-form-urlencoded格式的数据
            // const params = new URLSearchParams();
            // params.append("username", data.phone);
            // params.append("password", data.passwd);

            self.$store
              .dispatch("user/authLogin", {
                username: data.phone,
                password: data.passwd,
                company_id: data.company_id,
                company_type: data.company_type
              })
              .then((res) => {
                if (res.code === 200) {

                  self.$message({
                    message: res.msg,
                    type: "success",
                  });
                  self.$router.push({ path: self.redirect || "/" });
                  self.loading = false;
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              })
              .catch((err) => {
                self.loading = false;
              });
          }
        } else {
          console.log("表单验证失败，企业ID:", self.loginForm.company_id);
          return false;
        }
      });
    },
    handleCompanyChange(value) {
      console.log("企业已选择:", value);
      this.loginForm.company_id = value;
    },
    handleCompanyTypeChange(value) {
      console.log("公司类型已选择:", value);
      // 如果选择了乙禾公司，将企业ID设为0
      if (value === 2) {
        this.loginForm.company_id = 0;
      }
    },
  },
};
</script>

<style lang="less">
@bg: #283443;
@light_gray: #fff;
@cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: @cursor)) {
  .login-container .el-input input {
    color: @cursor;
  }
}

/* reset element-ui css */
.login-container {
  display: flex;
  align-self: center;
  .login-box {
    display: inline-block;
    box-shadow: 0 2px 10px 0 #e9e9e9;
    height: 500px;
    background-color: #fff;
    .tags {
      text-align: center;
      margin: 10px auto 30px;
      span {
        display: inline-block;
        width: 50%;
        font-size: 14px;
        cursor: pointer;
        position: relative;
        &.active {
          color: #1059f7;
          &:after {
            content: " ";
            height: 2px;
            background: #1059f7;
            display: block;
            z-index: 10;
            width: 37px;
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translate(-50%, 0);
          }
        }
      }
    }
  }
  .el-input {
    display: inline-block;
    height: 47px;
    width: 73%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: #333;
      height: 47px;
      // caret-color: @cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px @bg inset !important;
        -webkit-text-fill-color: @cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: #f3f4f9;
    border-radius: 5px;
    color: #333;
  }
}
</style>

<style lang="less" scoped>
@bg: #f0f5fd;
@dark_gray: #333;
@light_gray: #333;

.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  background: radial-gradient(
    circle at 10% 20%,
    rgb(239, 246, 249) 0%,
    rgb(206, 239, 253) 90%
  );

  .login-form {
    position: relative;
    width: 380px;
    max-width: 100%;
    padding: 20px 20px 20px;
    margin: 0 auto;
    overflow: hidden;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: @dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  /deep/.el-form-item__error {
    padding-top: 5px !important;
  }
}

.title-container {
  position: relative;

  .title {
    font-size: 26px;
    font-weight: 400;
    color: @light_gray;
    margin: 0px auto 40px auto;
    text-align: center;
    font-weight: bold;
    margin-top: 40px;
  }
}

.company-type-container {
  margin-bottom: 20px;
  background: transparent;
  border: none;

  .radio-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  /deep/ .el-radio {
    margin: 0;
    padding: 8px 15px;
    border-radius: 4px;
    transition: all 0.3s;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    width: 48%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &.radio-left {
      text-align: left;
    }

    &.radio-right {
      text-align: right;
    }

    &.is-checked {
      background-color: #e6f7ff;
      border-color: #1890ff;
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
    }

    .radio-label {
      font-size: 14px;
      margin-left: 4px;
    }

    .el-radio__input {
      .el-radio__inner {
        border-color: #1890ff;
        background-color: #fff;

        &::after {
          background-color: #1890ff;
        }
      }
    }
  }
}
</style>
