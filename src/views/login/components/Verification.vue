<template>
  <div class="clearfix">
    <el-form-item prop="captcha" class="verification-con fl">
      <el-input
        ref="captcha"
        v-model="areaCode"
        name="captcha"
        type="text"
        placeholder="请输入验证码"
        class="verification-input"
        @keyup.enter.native="enterLogin"
        @input="inputFun"
        maxlength="6"
      />
    </el-form-item>
    <div
      class="verification-code fr"
      :style="'background-image: url(' + imageUrl + ')'"
      @click="getCaptcha"
    />
  </div>
</template>
<script>
import { requestApi } from '@/utils/request'
import { getBase64FromArrayBuffer } from '@/utils/transform'
export default {
  name: 'Verification',
  data() {
    return {
      areaCode: '', // 验证码
      imageUrl: '', // 验证码图片
      loginRules: {
        captcha: [{ required: true, trigger: 'blur', message: '请输入验证码' }]
      }
    }
  },
  created() {
    this.getCaptcha()
  },
  methods: {
    // 获取图片验证
    getCaptcha() {
      this.areaCode = ''
      requestApi({
        name: 'getCaptcha'
      }).then((res) => {
        if (res) {
          this.imageUrl = getBase64FromArrayBuffer(res)
          console.log('imageUrl----', res, this.imageUrl)
        }
      })
    },
    inputFun(value) {
      this.areaCode = value
      this.$emit('updateAreaCode', this.areaCode)
    },
    enterLogin() {
      this.$emit('handleLogin', this.areaCode)
    }
  }
}
</script>
<style lang="less">
.verification-con {
  width: 240px;
  margin-bottom: 30px;
  height: 52px;
  .verification-input::placeholder {
    color: #c0c4cb;
    font-size: 14px;
  }
}
.verification-code {
  width: 200px;
  height: 52px;
  background-position: 0;
  background-size: 100% 100%;
}
</style>
