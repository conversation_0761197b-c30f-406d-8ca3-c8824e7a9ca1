<template>
  <div class="enterprise-finance">
    <h1>企业账户列表</h1>

    <!-- 头部菜单 -->
    <div class="search-bar">
      <div class="search-container">
        <div class="input-group">
          <el-input
              v-model="searchForm.name"
              placeholder="企业名称"
              size="mini"
          ></el-input>
        </div>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <el-button type="primary" size="mini" @click="resetSearch">重置</el-button>
      </div>
    </div>
    <!-- 头部菜单结束 -->

    <!-- 表格 -->
    <el-table :data="enterpriseList" border style="width: 100%">
      <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="id"
          label="企业ID"
          align="center"
          width="60"
      ></el-table-column>
      <el-table-column
          prop="company_name"
          label="企业名称"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="regular_balance"
          label="基本账户余额"
          align="center"
          width="100"
      ></el-table-column>
      <el-table-column
          prop="gift_balance"
          label="赠送账户余额"
          align="center"
          width="100"
      ></el-table-column>
      <el-table-column label="操作" width="240" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="openRecharge(scope.row)">账户充值</el-button>
          <el-button type="text" size="small" @click="withdraw(scope.row)">退款申请</el-button>
          <el-button
              type="text"
              size="small"
              @click="viewTransactions(scope.row)"
          >交易明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束 -->

    <!-- 列表分页 -->
    <div class="pagination-container">
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pagination.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
      >
      </el-pagination>
    </div>
    <!-- 列表分页结束 -->

    <!-- 充值申请对话框 -->
    <el-dialog
        title="充值申请"
        :visible.sync="rechargeDialogVisible"
        width="500px"
    >
      <div class="recharge-dialog">
        <div class="info-item">
          <span class="label">用户ID:</span>
          <span>{{ currentEnterprise.userId }}</span>
        </div>
        <div class="info-item">
          <span class="label">企业名称:</span>
          <span>{{ currentEnterprise.companyName }}</span>
        </div>
        <div class="info-item">
          <span class="label">帐号名:</span>
          <span>{{ currentEnterprise.userName }}</span>
        </div>
        <div class="info-item">
          <span class="label">账户余额:</span>
          <span>{{ currentEnterprise.regularAccountBalance }}</span>
        </div>
        <div class="info-item">
          <span class="label">赠送账户余额:</span>
          <span>{{ currentEnterprise.giftAccountBalance }}</span>
        </div>
        <div class="info-item">
          <span class="label">充值金额:</span>
          <el-input-number
              v-model="rechargeForm.amount"
              placeholder="充值金额"
              :precision="2"
              :min="0"
              :step="0.5"
          ></el-input-number>
          <span class="unit">元</span>
        </div>
        <div class="tips">
          <p>注意: 请确认充值金额正确无误!</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rechargeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="recharge">充值</el-button>
      </span>
    </el-dialog>

    <!-- 退款申请对话框 -->
    <el-dialog
        title="退款申请"
        :visible.sync="withdrawDialogVisible"
        width="500px"
    >
      <div class="withdraw-dialog">
        <div class="info-item">
          <span class="label">用户ID:</span>
          <span>{{ currentEnterprise.userId }}</span>
        </div>
        <div class="info-item">
          <span class="label">企业名称:</span>
          <span>{{ currentEnterprise.companyName }}</span>
        </div>
        <div class="info-item">
          <span class="label">帐号名:</span>
          <span>{{ currentEnterprise.userName }}</span>
        </div>
        <div class="info-item">
          <span class="label">账户余额:</span>
          <span>{{ currentEnterprise.regularAccountBalance }}</span>
        </div>
        <div class="info-item">
          <span class="label">赠送账户余额:</span>
          <span>{{ currentEnterprise.giftAccountBalance }}</span>
        </div>
        <div class="info-item">
          <span class="label">退款金额:</span>
          <el-input-number
              v-model="withdrawForm.amount"
              :min="1"
              :precision="0"
          ></el-input-number>
          <span class="unit">元</span>
        </div>
        <div class="tips">
          <p>注意: </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="withdrawDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitWithdraw">确认退款</el-button>
      </span>
    </el-dialog>

    <!-- 交易明细 -->
    <el-dialog
        title="交易明细"
        :visible.sync="transactionDialogVisible"
        width="90%"
    >
      <div class="transaction-list">
        <div class="operation-bar"></div>
        <el-table
            :data="transactionList"
            border
            style="width: 100%"
            v-loading="transactionLoading"
        >
          <!--          <el-table-column-->
          <!--            prop="id"-->
          <!--            label="序号"-->
          <!--            width="60"-->
          <!--            align="center"-->
          <!--          ></el-table-column>-->
          <el-table-column
              prop="order_no"
              label="订单编号"
              align="center"
              width="180"
          ></el-table-column>
          <el-table-column
              prop="username"
              label="用户名"
              width="120"
              align="center"
          ></el-table-column>
          <el-table-column
              prop="account_type"
              label="账户"
              align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.account_type | accountTypeFilter }}
            </template>
          </el-table-column>
          <el-table-column
              prop="transaction_type"
              label="交易类型"
              align="center"
              width="80"
          >
            <template #default="scope">
              {{ scope.row.transaction_type | transactionTypeFilter }}
            </template>
          </el-table-column>
          <el-table-column
              prop="payment_method"
              label="支付方式"
              align="center"
              width="80"
          >
            <template #default="scope">
              <!-- 使用过滤器转换支付方式 -->
              {{ scope.row.payment_method | paymentMethodFilter }}
            </template>
          </el-table-column>
          <el-table-column
              prop="amount"
              label="金额"
              align="center"
              width="100"
          ></el-table-column>
          <el-table-column
              prop="transaction_time"
              label="交易时间"
              align="center"
          ></el-table-column>
          <el-table-column
              prop="description"
              label="明细描述"
              align="center"
          ></el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
              @size-change="handleTransactionSizeChange"
              @current-change="handleTransactionCurrentChange"
              :current-page.sync="transactionPagination.page"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="transactionPagination.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="transactionPagination.total"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {requestApi} from "@/utils/request";

export default {
  name: "EnterpriseFinance",
  data() {
    return {
      title: "企业账户",
      searchForm: {
        name: "",
      },
      enterpriseList: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
      },
      total: 0,
      rechargeDialogVisible: false,
      withdrawDialogVisible: false,
      transactionDialogVisible: false,
      currentEnterprise: {
        userId: "",
        companyName: "",
        regularAccountBalance: 0,
        giftAccountBalance: 0,
      },
      rechargeForm: {
        userId: null,
        amount: 0,
      },
      withdrawForm: {
        amount: 10000,
      },
      transactionList: [],
      transactionPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      loading: false,
      transactionLoading: false,
    };
  },
  created() {
    this.getEnterpriseAccountList();
  },
  methods: {
    // 获取用户列表
    getEnterpriseAccountList() {
      this.loading = true;

      const params = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      };

      // 添加搜索参数
      if (this.searchForm.name) {
        params.name = this.searchForm.name;
      }

      // 打印请求参数便于调试
      console.log("请求参数:", params);

      requestApi({
        name: "getEnterpriseAccountsBySearch",
        data: params,
      })
          .then((response) => {
            this.loading = false;
            console.log("API响应:", response);
            if (response && response.code === 200) {
              console.log("response")
              console.log(response)
              console.log("response end")

              this.enterpriseList = response.data.list || [];
              this.total = response.data.total || 0;
            } else {
              this.$message.error(response.message || "获取企业列表失败");
            }
          })
          .catch((error) => {
            this.loading = false;
            console.error("获取企业列表失败", error);
            this.$message.error("获取企业列表失败");
          });
    },

    // 查询企业
    search() {
      this.pagination.page = 1;
      this.getEnterpriseAccountList();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        name: "",
      };
      this.getEnterpriseAccountList();
    },

    resetRechargeForm() {
      this.rechargeForm = {
        userId: null,
        amount: 0,
      };
    },

    // 查看账户
    openRecharge(row) {
      this.currentEnterprise = {
        userId: "",
        companyName: "",
        regularAccountBalance: 0,
        giftAccountBalance: 0,

      }
      requestApi({
        name: "getEnterpriseAccountDetail",
        data: {
          user_id: Number(row.id),
        },
      })
          .then((response) => {
            if (response && response.code === 200) {
              this.currentEnterprise = {
                userId: response.data.enterprise_info.id || "",
                userName: response.data.enterprise_info.username || "",
                companyName: response.data.enterprise_info.company_name || "",
                regularAccountBalance: response.data.account_info.regular_account.balance || 0,
                giftAccountBalance: response.data.account_info.gift_account.balance || 0,
              };
              this.rechargeDialogVisible = true;
            } else {
              this.$message.error(response.message || "获取账户信息失败");
            }
          })
          .catch((error) => {
            console.error("获取账户信息失败", error);
            this.$message.error("获取账户信息失败");
          });
    },

    // 用户充值
    recharge() {
      console.log("run")
      requestApi({
        name: "rechargeEnterpriseAccount",
        data: {
          user_id: this.currentEnterprise.userId,
          amount: this.rechargeForm.amount,
          payment_method: "bank_transfer",
        },
      })
          .then((response) => {
            if (response && response.code === 200) {
              this.$message.success("充值成功");
              this.rechargeDialogVisible = false;
              this.getEnterpriseAccountList();
            } else {
              this.$message.error(response.message || "充值失败");
            }
          })
          .catch((error) => {
            console.error("充值失败", error);
            this.$message.error("充值失败");
          });
    },

    // 提交充值
    submitRecharge() {
      // TODO: 提交充值
      this.$message.success("充值申请已提交");
      this.rechargeDialogVisible = false;
      this.getEnterpriseAccountList();
    },
    // 退款申请
    withdraw(row) {
      this.currentEnterprise = {
        ...row,
      };
      this.withdrawDialogVisible = true;
    },
    // 提交退款
    submitWithdraw() {
      // TODO: 提交退款
      this.$message.success("退款申请已提交");
      this.withdrawDialogVisible = false;
      this.getEnterpriseAccountList();
    },

    // 查看交易明细
    viewTransactions(row) {
      this.currentEnterprise = row;
      this.transactionDialogVisible = true;
      this.getTransactionList();
    },

    // 获取交易明细
    getTransactionList() {
      this.transactionLoading = true;

      const params = {
        user_id: this.currentEnterprise.id,
        page: this.transactionPagination.page,
        pageSize: this.transactionPagination.pageSize,
      };

      // 打印请求参数便于调试
      console.log("交易明细请求参数:", params);

      requestApi({
        name: "getEnterpriseAccountTranscations",
        data: params,
      })
          .then((response) => {
            this.transactionLoading = false;

            if (response && response.code === 200) {
              this.transactionList = response.data.list || [];
              this.transactionPagination.total = response.data.total || 0;
            } else {
              this.$message.error(response.message || "获取交易明细失败");
            }
          })
          .catch((error) => {
            this.transactionLoading = false;
            console.error("获取交易明细失败", error);
            this.$message.error("获取交易明细失败");
          });
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getEnterpriseAccountList();
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.getEnterpriseAccountList();
    },

    handleTransactionSizeChange(val) {
      this.transactionPagination.pageSize = val;
      this.getTransactionList();
    },

    handleTransactionCurrentChange(val) {
      this.transactionPagination.currentPage = val;
      this.getTransactionList();
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise-finance {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .search-bar {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;

    .search-container {
      display: flex;
      gap: 10px;

      .input-group {
        margin-right: 0;

        .el-input {
          width: 180px;
        }
      }
    }
  }

  .operation-bar {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }

  .enterprise-info-dialog,
  .recharge-dialog,
  .withdraw-dialog {
    .info-item {
      margin-bottom: 15px;
      display: flex;
      align-items: center;

      .label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
        color: #606266;
      }

      .unit {
        margin-left: 5px;
      }
    }

    .tips {
      margin-top: 15px;
      color: #f56c6c;
      font-size: 12px;

      p {
        margin: 5px 0;
      }
    }
  }

  .transaction-list {
    .income {
      color: #67c23a;
    }

    .expense {
      color: #f56c6c;
    }
  }
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
