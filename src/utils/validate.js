/**
 * 校验器
 * 当前文件下的方法名称皆以 [is/has/check] + [名词] 进行命名
 * 返回值为 boolean
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 是否为字符串
 * @param {any} str
 */
export function isString(str) {
  return Object.prototype.toString.call(str) === '[object String]'
}

/**
 * 是否为普通对象
 * @param {object} obj
 * 比如：isPlainObject(null) 为 false。
 * isPlainObject([]) 为 false。
 * isPlainObject({}) 为 true。
 */
export function isPlainObject(obj) {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

/**
 * 是否为function
 * @param {function} func
 */
export function isFunction(func) {
  // note: 异步函数用 Object.prototype.toString 的结果是 [Object AsyncFunction]
  return typeof func === 'function'
}

/**
 * 检验是否为合法链接（必须以http开头）
 * @param {function} func
 */
export function checkUrl(str) {
  // const regUrl =
  //   /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/
  const regUrl =
    /^(http(s)?:\/\/)[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/
  // const regUrl = /(http|https):\/\/\S*/
  return !!str.match(regUrl)
}

/**
 * 手机号校验
 * @param {any} str
 */

export function phone(value) {
  var tel =
    /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
  return tel.test(value)
}
