/**
 * Cron表达式解析工具
 * 支持解析标准的cron表达式，并返回可读的描述
 */

// 将cron表达式转换为人类可读的文本
export function parseCronExpression(cronExpression) {
  if (!cronExpression || cronExpression.trim() === '') return '';
  
  // 分割cron表达式，格式：分 时 日 月 周 秒 年
  const parts = cronExpression.trim().split(' ');
  if (parts.length < 5) return '无效的cron表达式';
  
  // 处理5位或7位cron表达式
  let minute, hour, day, month, weekday, second = '0', year = '*';
  
  if (parts.length >= 7) {
    [minute, hour, day, month, weekday, second, year] = parts;
  } else if (parts.length >= 6) {
    [minute, hour, day, month, weekday, second] = parts;
  } else {
    [minute, hour, day, month, weekday] = parts;
  }
  
  // 指定时间模式
  if (year && year !== '*') {
    // 返回完整的日期时间
    try {
      const date = new Date();
      date.setFullYear(parseInt(year));
      date.setMonth(parseInt(month) - 1);
      date.setDate(parseInt(day));
      date.setHours(parseInt(hour));
      date.setMinutes(parseInt(minute));
      date.setSeconds(parseInt(second));
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return '日期解析错误';
    }
  }
  
  // 重复时间模式
  let timeStr = '';
  
  // 解析时间部分
  if (hour !== '*' && minute !== '*') {
    timeStr = `${hour}:${minute.padStart(2, '0')}`;
  }
  
  // 每天模式
  if (day.includes('/') && weekday === '*' && month === '*') {
    const interval = day.split('/')[1];
    return `每${interval}天 ${timeStr}`;
  }
  
  // 每周模式
  if (weekday !== '*' && day === '*' && month === '*') {
    let weekdayStr = '';
    
    if (weekday.includes(',')) {
      const days = weekday.split(',');
      weekdayStr = days.map(d => getWeekdayName(d)).join('、');
    } else if (weekday.includes('-')) {
      const [start, end] = weekday.split('-');
      weekdayStr = `${getWeekdayName(start)}至${getWeekdayName(end)}`;
    } else {
      weekdayStr = getWeekdayName(weekday);
    }
    
    return `每周${weekdayStr} ${timeStr}`;
  }
  
  // 每月模式
  if (day !== '*' && weekday === '*' && month === '*') {
    let dayStr = '';
    
    if (day.includes(',')) {
      const days = day.split(',');
      dayStr = days.join('、');
    } else if (day.includes('-')) {
      const [start, end] = day.split('-');
      dayStr = `${start}至${end}`;
    } else if (!day.includes('/')) {
      dayStr = day;
    }
    
    return `每月${dayStr}日 ${timeStr}`;
  }
  
  // 每年模式
  if (day !== '*' && month !== '*' && weekday === '*') {
    return `每年${month}月${day}日 ${timeStr}`;
  }
  
  // 其他情况
  if (minute === '*' && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    return '每分钟';
  }
  
  if (minute !== '*' && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    return `每小时的第${minute}分钟`;
  }
  
  if (minute !== '*' && hour !== '*' && day === '*' && month === '*' && weekday === '*') {
    return `每天${hour}:${minute.padStart(2, '0')}`;
  }
  
  // 无法解析的表达式
  return '复杂的cron表达式';
}

// 获取星期几的中文名称
function getWeekdayName(day) {
  const weekdays = ['日', '一', '二', '三', '四', '五', '六', '日'];
  return `周${weekdays[parseInt(day)]}`;
}

/**
 * 计算下一次cron表达式执行的时间
 * @param {string} cronExpression cron表达式
 * @returns {Date|null} 下一次执行的时间，如果无法计算则返回null
 */
export function getNextCronTime(cronExpression) {
  if (!cronExpression || cronExpression.trim() === '') return null;
  
  try {
    const parts = cronExpression.trim().split(' ');
    if (parts.length < 5) return null;
    
    let minute, hour, day, month, weekday;
    
    if (parts.length >= 6) {
      [minute, hour, day, month, weekday] = parts;
    } else {
      [minute, hour, day, month, weekday] = parts;
    }
    
    // 如果是每天模式且有具体时间
    if ((day === '*' || day.includes('/')) && weekday === '*' && month === '*' && hour !== '*' && minute !== '*') {
      const now = new Date();
      const nextTime = new Date();
      
      nextTime.setHours(parseInt(hour));
      nextTime.setMinutes(parseInt(minute));
      nextTime.setSeconds(0);
      
      // 如果下一次执行时间已经过了今天的时间，则设置为明天
      if (nextTime <= now) {
        nextTime.setDate(nextTime.getDate() + 1);
      }
      
      return nextTime;
    }
    
    // 如果是每周模式且有具体时间
    if (weekday !== '*' && day === '*' && month === '*' && hour !== '*' && minute !== '*') {
      const now = new Date();
      const currentDay = now.getDay(); // 0是周日，1-6是周一到周六
      let targetDay;
      
      if (weekday.includes(',')) {
        // 多个星期几的情况
        const days = weekday.split(',').map(Number);
        // 找到下一个最近的星期几
        const futureDays = days.filter(d => d > currentDay);
        targetDay = futureDays.length > 0 ? Math.min(...futureDays) : Math.min(...days) + 7;
      } else {
        // 单个星期几的情况
        targetDay = parseInt(weekday);
        if (targetDay < currentDay) {
          targetDay += 7; // 下一周
        }
      }
      
      const daysToAdd = (targetDay - currentDay + 7) % 7;
      const nextTime = new Date();
      nextTime.setDate(now.getDate() + (daysToAdd === 0 ? 7 : daysToAdd));
      nextTime.setHours(parseInt(hour));
      nextTime.setMinutes(parseInt(minute));
      nextTime.setSeconds(0);
      
      return nextTime;
    }
    
    // 如果是每月模式且有具体时间
    if (day !== '*' && !day.includes('/') && weekday === '*' && month === '*' && hour !== '*' && minute !== '*') {
      const now = new Date();
      const currentDate = now.getDate();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      
      let targetDate;
      if (day.includes(',')) {
        // 多个日期的情况
        const dates = day.split(',').map(Number);
        // 找到当月剩余的日期
        const futureDates = dates.filter(d => d > currentDate);
        targetDate = futureDates.length > 0 ? Math.min(...futureDates) : Math.min(...dates);
      } else {
        targetDate = parseInt(day);
      }
      
      const nextTime = new Date(currentYear, currentMonth, targetDate, parseInt(hour), parseInt(minute), 0);
      
      // 如果下一次执行时间已经过了这个月的时间，则设置为下个月
      if (nextTime <= now) {
        nextTime.setMonth(nextTime.getMonth() + 1);
      }
      
      return nextTime;
    }
    
    // 简单情况：每天特定时间
    if (minute !== '*' && hour !== '*' && day === '*' && month === '*' && weekday === '*') {
      const now = new Date();
      const nextTime = new Date();
      
      nextTime.setHours(parseInt(hour));
      nextTime.setMinutes(parseInt(minute));
      nextTime.setSeconds(0);
      
      // 如果下一次执行时间已经过了今天的时间，则设置为明天
      if (nextTime <= now) {
        nextTime.setDate(nextTime.getDate() + 1);
      }
      
      return nextTime;
    }
    
    // 更复杂的情况可能需要更复杂的逻辑或使用第三方库
    return null;
  } catch (e) {
    console.error('计算下一次cron执行时间出错:', e);
    return null;
  }
}

/**
 * 计算截止时间（根据开始时间减去指定分钟数）
 * @param {Date} startTime 开始时间
 * @param {number} minutesBefore 提前的分钟数
 * @returns {Date} 截止时间
 */
export function calculateDeadlineTime(startTime, minutesBefore) {
  if (!startTime || isNaN(minutesBefore)) return null;
  
  const deadlineTime = new Date(startTime);
  deadlineTime.setMinutes(deadlineTime.getMinutes() - parseInt(minutesBefore));
  
  return deadlineTime;
}

/**
 * 生成多个连续的执行时间
 * @param {string} cronExpression cron表达式
 * @param {number} count 要生成的时间数量
 * @returns {Array<Date>} 连续的执行时间数组
 */
export function generateMultipleExecutionTimes(cronExpression, count) {
  if (!cronExpression || !count || count <= 0) return [];
  
  const times = [];
  let currentDate = new Date(); // 从当前时间开始
  
  for (let i = 0; i < count; i++) {
    // 获取下一次执行时间
    const nextTime = getNextCronTimeFromDate(cronExpression, currentDate);
    if (!nextTime) break;
    
    times.push(nextTime);
    
    // 设置为下一次查找的起点（加1分钟避免获取到相同时间）
    currentDate = new Date(nextTime.getTime() + 60000);
  }
  
  return times;
}

/**
 * 从指定日期开始计算下一次cron表达式执行的时间
 * @param {string} cronExpression cron表达式
 * @param {Date} startFrom 开始计算的日期时间
 * @returns {Date|null} 下一次执行的时间，如果无法计算则返回null
 */
function getNextCronTimeFromDate(cronExpression, startFrom) {
  if (!cronExpression || cronExpression.trim() === '' || !startFrom) return null;
  
  try {
    const parts = cronExpression.trim().split(' ');
    if (parts.length < 5) return null;
    
    let minute, hour, day, month, weekday;
    
    if (parts.length >= 6) {
      [minute, hour, day, month, weekday] = parts;
    } else {
      [minute, hour, day, month, weekday] = parts;
    }
    
    // 创建一个副本，避免修改原始日期
    const now = new Date(startFrom);
    
    // 如果是每天模式且有具体时间
    if ((day === '*' || day.includes('/')) && weekday === '*' && month === '*' && hour !== '*' && minute !== '*') {
      const nextTime = new Date(now);
      
      nextTime.setHours(parseInt(hour));
      nextTime.setMinutes(parseInt(minute));
      nextTime.setSeconds(0);
      
      // 如果下一次执行时间已经过了当前时间，则设置为明天
      if (nextTime <= now) {
        nextTime.setDate(nextTime.getDate() + 1);
      }
      
      return nextTime;
    }
    
    // 如果是每周模式且有具体时间
    if (weekday !== '*' && day === '*' && month === '*' && hour !== '*' && minute !== '*') {
      const currentDay = now.getDay(); // 0是周日，1-6是周一到周六
      let targetDay;
      
      if (weekday.includes(',')) {
        // 多个星期几的情况
        const days = weekday.split(',').map(Number);
        // 找到下一个最近的星期几
        const futureDays = days.filter(d => d > currentDay);
        targetDay = futureDays.length > 0 ? Math.min(...futureDays) : Math.min(...days) + 7;
      } else {
        // 单个星期几的情况
        targetDay = parseInt(weekday);
        if (targetDay < currentDay) {
          targetDay += 7; // 下一周
        }
      }
      
      const daysToAdd = (targetDay - currentDay + 7) % 7;
      const nextTime = new Date(now);
      nextTime.setDate(now.getDate() + (daysToAdd === 0 ? 7 : daysToAdd));
      nextTime.setHours(parseInt(hour));
      nextTime.setMinutes(parseInt(minute));
      nextTime.setSeconds(0);
      
      return nextTime;
    }
    
    // 如果是每月模式且有具体时间
    if (day !== '*' && !day.includes('/') && weekday === '*' && month === '*' && hour !== '*' && minute !== '*') {
      const currentDate = now.getDate();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      
      let targetDate;
      if (day.includes(',')) {
        // 多个日期的情况
        const dates = day.split(',').map(Number);
        // 找到当月剩余的日期
        const futureDates = dates.filter(d => d > currentDate);
        targetDate = futureDates.length > 0 ? Math.min(...futureDates) : Math.min(...dates);
      } else {
        targetDate = parseInt(day);
      }
      
      const nextTime = new Date(currentYear, currentMonth, targetDate, parseInt(hour), parseInt(minute), 0);
      
      // 如果下一次执行时间已经过了当前时间，则设置为下个月
      if (nextTime <= now) {
        nextTime.setMonth(nextTime.getMonth() + 1);
      }
      
      return nextTime;
    }
    
    // 简单情况：每天特定时间
    if (minute !== '*' && hour !== '*' && day === '*' && month === '*' && weekday === '*') {
      const nextTime = new Date(now);
      
      nextTime.setHours(parseInt(hour));
      nextTime.setMinutes(parseInt(minute));
      nextTime.setSeconds(0);
      
      // 如果下一次执行时间已经过了当前时间，则设置为明天
      if (nextTime <= now) {
        nextTime.setDate(nextTime.getDate() + 1);
      }
      
      return nextTime;
    }
    
    // 更复杂的情况可能需要更复杂的逻辑或使用第三方库
    return null;
  } catch (e) {
    console.error('计算下一次cron执行时间出错:', e);
    return null;
  }
}

export default {
  parseCronExpression,
  getNextCronTime,
  calculateDeadlineTime,
  generateMultipleExecutionTimes
};
