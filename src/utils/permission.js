import store from '@/store'

/**
 * 检查是否拥有指定角色权限
 * @param {Array} roles 需要的角色数组
 * @returns {Boolean}
 */
export function checkRole(roles) {
  const userRoles = store.getters && store.getters.roles
  if (!userRoles || userRoles.length === 0) {
    return false
  }
  
  if (Array.isArray(roles)) {
    return roles.some(role => userRoles.includes(role))
  } else {
    return userRoles.includes(roles)
  }
}

/**
 * 检查是否拥有指定权限码
 * @param {Array|String} permissions 需要的权限码
 * @returns {Boolean}
 */
export function checkPermission(permissions) {
  const userPermissions = store.getters && store.getters.permissions
  if (!userPermissions || userPermissions.length === 0) {
    return false
  }
  
  if (Array.isArray(permissions)) {
    return permissions.some(permission => userPermissions.includes(permission))
  } else {
    return userPermissions.includes(permissions)
  }
}

/**
 * 检查是否拥有访问路由的权限
 * @param {Object} route 路由对象
 * @returns {Boolean}
 */
export function checkRoutePermission(route) {
  if (!route.meta) {
    return true
  }
  
  const { role, permission } = route.meta
  
  // 检查角色权限
  if (role && role.length > 0) {
    if (!checkRole(role)) {
      return false
    }
  }
  
  // 检查权限码
  if (permission && permission.length > 0) {
    if (!checkPermission(permission)) {
      return false
    }
  }
  
  return true
}

/**
 * 检查当前用户是否为超级管理员
 * @returns {Boolean}
 */
export function isSuperAdmin() {
  return checkRole(['super_admin'])
}

/**
 * 检查当前用户是否为管理员（包括超级管理员）
 * @returns {Boolean}
 */
export function isAdmin() {
  return checkRole(['admin', 'super_admin'])
} 