import {
  Message
} from 'element-ui'

/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|String|number)} time
 * @param {String} cFormat
 * @returns {String | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {String} option
 * @returns {String}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * 获取倒计时
 * @param {number} sec
 * @returns {String}
 */
export function getCountdown(sec) {
  if (!sec) {
    return 0
  }
  var min = Math.floor(sec % 3600) // 分钟
  return Math.floor(sec / 3600) + '时' + Math.floor(min / 60) + '分'
}

/**
 * 获取链接参数对象
 * @param {String} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * 数组对象去重
 * @param {Array} arr
 * @returns {Object}
 */
export function unique(arr, key) {
  var hash = {}
  var result = []
  var type = ''
  var item
  for (let i = 0; i < arr.length; i++) {
    item = arr[i]
    let tmpItem
    if (typeof item === 'object' && isNaN(item.length)) {
      tmpItem = arr[i][key]
    } else {
      tmpItem = arr[i]
    }
    type = Object.prototype.toString.call(tmpItem)
    if (!hash[tmpItem + type]) {
      hash[tmpItem + type] = true
      result.push(item)
    }
  }
  return result
}

/**
 * 数组去重
 * @param {Array} arr
 * @returns {Array}
 */
export function arrUnique(arr) {
  var res = []
  var json = {}
  for (var i = 0; i < arr.length; i++) {
    if (!json[arr[i]]) {
      res.push(arr[i])
      json[arr[i]] = 1
    }
  }
  return res
}

/**
 * 防抖
 * @param {Function} func 要执行的回调函数
 * @param {Number} wait 延时的时间
 * @param {Boolean} immediate 是否立即执行
 * @return {*}
 */
export function debounce(func, wait = 500, immediate = false) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * 节流
 * @param fn {Function}   实际要执行的函数
 * @param threshhold {Number}  执行间隔，单位是毫秒（ms）
 *
 * @return {Function}     返回一个“节流”函数
 */

export function throttle(fn, threshhold) {
  // 记录上次执行的时间
  var last

  // 定时器
  var timer

  // 默认间隔为 250ms
  threshhold || (threshhold = 250)

  // 返回的函数，每过 threshhold 毫秒就执行一次 fn 函数
  return function () {
    // 保存函数调用时的上下文和参数，传递给 fn
    var context = this
    var args = arguments

    var now = +new Date()

    // 如果距离上次执行 fn 函数的时间小于 threshhold，那么就放弃
    // 执行 fn，并重新计时
    if (last && now < last + threshhold) {
      clearTimeout(timer)

      // 保证在当前时间区间结束后，再执行一次 fn
      timer = setTimeout(function () {
        last = now
        fn.apply(context, args)
      }, threshhold)

      // 在时间区间的最开始和到达指定间隔的时候执行一次 fn
    } else {
      last = now
      fn.apply(context, args)
    }
  }
}

/**
 * 复制内容
 * @param {String} data
 */
export function copyText(data) {
  const elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  // 选择对象
  elInput.select()
  this.$message({
    dangerouslyUseHTMLString: false,
    message: '复制内容:' + data,
    type: 'success'
  })
  document.execCommand('Copy')
  elInput.remove()
}

/**
 * 图片上传前限制
 * @param {Object} file
 * @param {Number} size 限制文件大小 单位MB
 *
 */
export function beforeUploadImage(file, size) {
  const isJPG =
    file.type === 'image/jpeg' ||
    file.type === 'image/jpg' ||
    file.type === 'image/png'
  const isLtSize = file.size / 1024 / 1024 < size
  if (!isJPG) {
    Message.error('上传图片必须是 JPG,JPEG,PNG 格式!')
  }
  if (!isLtSize) {
    Message.error(`上传失败，图片大小不能超过${size}M`)
  }
  return isJPG && isLtSize
}

/**
 * 倒计时
 *
 */
export function haveTime(s, e) {
  console.log(Date.parse(new Date(s)))
  let stime = Date.parse(new Date(s));
  let etime = Date.parse(new Date(e));
  // 两个时间戳相差的毫秒数
  let usedTime = etime - stime;
  // 计算相差的天数
  let days = Math.floor(usedTime / (24 * 3600000));
  // 计算天数后剩余的毫秒数
  let leave1 = usedTime % (24 * 3600000);
  // 计算出小时数
  let hours = Math.floor(leave1 / 3600000);
  console.log(hours)
  // 计算小时数后剩余的毫秒数
  let leave2 = leave1 % 3600000;
  // 计算相差分钟数
  var minutes = Math.floor(leave2 / 60000);
  let time = days + '天' + hours + '时' + minutes + '分';
  return time;
}
/**
 * convertCurrencyToChinese - 数字转成汉字
 * @params num === 要转换的数字
 * @return 汉字
 * */
export function convertCurrencyToChinese(num) {
  if (!num) {
    return '零';
  }
  // Constants:
  const MAXIMUM_NUMBER = 99999999999.99;
  // Predefine the radix characters and currency symbols for output:
  const CN_ZERO = "零";
  const CN_ONE = "壹";
  const CN_TWO = "贰";
  const CN_THREE = "叁";
  const CN_FOUR = "肆";
  const CN_FIVE = "伍";
  const CN_SIX = "陆";
  const CN_SEVEN = "柒";
  const CN_EIGHT = "捌";
  const CN_NINE = "玖";
  const CN_TEN = "拾";
  const CN_HUNDRED = "佰";
  const CN_THOUSAND = "仟";
  const CN_TEN_THOUSAND = "万";
  const CN_HUNDRED_MILLION = "亿";
  // const CN_SYMBOL = "人民币";
  const CN_DOLLAR = "元";
  const CN_TEN_CENT = "角";
  const CN_CENT = "分";
  const CN_INTEGER = "整";
  // Variables:
  // let integral; // Represent integral part of digit number.
  // let decimal; // Represent decimal part of digit number.
  let outputCharacters; // The output result.
  // let parts;
  // let digits;
  // let radices;
  // let bigRadices;
  // let decimals;
  let zeroCount;
  let i;
  let p;
  let d;
  let quotient;
  let modulus;
  let currencyDigits = num;
  // Validate input string:
  currencyDigits = currencyDigits.toString();
  if (currencyDigits === "") {
    // alert("Empty input!");
    return "";
  }
  if (currencyDigits.match(/[^,.\d]/) != null) {
    // alert("Invalid characters in the input string!");
    return "";
  }
  if ((currencyDigits).match(/^((\d{1,3}(,\d{3})*(.((\d{3},)*\d{1,3}))?)|(\d+(.\d+)?))$/) == null) {
    // alert("Illegal format of digit number!");
    return "";
  }
  // Normalize the format of input digits:
  currencyDigits = currencyDigits.replace(/,/g, ""); // Remove comma delimiters.
  currencyDigits = currencyDigits.replace(/^0+/, ""); // Trim zeros at the beginning.
  // Assert the number is not greater than the maximum number.
  if (Number(currencyDigits) > MAXIMUM_NUMBER) {
    // eslint-disable-next-line no-console
    console.warn("输入的金额太大，请重新输入!");
    return "";
  }
  // Process the coversion from currency digits to characters:
  // Separate integral and decimal parts before processing coversion:
  const parts = currencyDigits.split(".");
  // eslint-disable-next-line prefer-const
  let [integral, decimal = ''] = parts;
  if (parts.length > 1) {
    // Cut down redundant decimal digits that are after the second.
    decimal = decimal.substr(0, 2);
  }
  // Prepare the characters corresponding to the digits:
  const digits = [CN_ZERO, CN_ONE, CN_TWO, CN_THREE, CN_FOUR, CN_FIVE, CN_SIX, CN_SEVEN, CN_EIGHT, CN_NINE];
  const radices = ["", CN_TEN, CN_HUNDRED, CN_THOUSAND];
  const bigRadices = ["", CN_TEN_THOUSAND, CN_HUNDRED_MILLION];
  const decimals = [CN_TEN_CENT, CN_CENT];
  // Start processing:
  outputCharacters = "";
  // Process integral part if it is larger than 0:
  if (Number(integral) > 0) {
    zeroCount = 0;
    for (i = 0; i < integral.length; i++) {
      p = integral.length - i - 1;
      d = integral.substr(i, 1);
      quotient = p / 4;
      modulus = p % 4;
      if (d === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          outputCharacters += digits[0];
        }
        zeroCount = 0;
        outputCharacters += digits[Number(d)] + radices[modulus];
      }
      if (modulus === 0 && zeroCount < 4) {
        outputCharacters += bigRadices[quotient];
      }
    }
    outputCharacters += CN_DOLLAR;
  }
  // Process decimal part if there is:
  if (decimal !== "") {
    for (i = 0; i < decimal.length; i++) {
      d = decimal.substr(i, 1);
      if (d !== "0") {
        outputCharacters += digits[Number(d)] + decimals[i];
      }
    }
  }
  // Confirm and return the final output string:
  if (outputCharacters === "") {
    outputCharacters = CN_ZERO + CN_DOLLAR;
  }
  if (decimal === "") {
    outputCharacters += CN_INTEGER;
  }
  return outputCharacters || '';
}

export function moneyFormat(num, decimal = 2, split = ',') {
  /*
    parameter：
    num：格式化目标数字
    decimal：保留几位小数，默认2位
    split：千分位分隔符，默认为,
    moneyFormat(123456789.87654321, 2, ',') // 123,456,789.88
  */
  if (isFinite(num)) { // num是数字
    if (num === 0) { // 为0
      return num.toFixed(decimal)
    } else { // 非0
      var res = ''
      var dotIndex = String(num).indexOf('.')
      if (dotIndex === -1) { // 整数
        if (decimal === 0) {
          res = String(num).replace(/(\d)(?=(?:\d{3})+$)/g, `$1${split}`)
        } else {
          res = String(num).replace(/(\d)(?=(?:\d{3})+$)/g, `$1${split}`) + '.' + '0'.repeat(decimal)
        }
      } else { // 非整数
        // js四舍五入 Math.round()：正数时4舍5入，负数时5舍6入
        // Math.round(1.5) = 2
        // Math.round(-1.5) = -1
        // Math.round(-1.6) = -2
        // 保留decimals位小数
        const numStr = String((Math.round(num * Math.pow(10, decimal)) / Math.pow(10, decimal)).toFixed(decimal)) // 四舍五入，然后固定保留2位小数
        const decimals = numStr.slice(dotIndex, dotIndex + decimal + 1) // 截取小数位
        res = String(numStr.slice(0, dotIndex)).replace(/(\d)(?=(?:\d{3})+$)/g, `$1${split}`) + decimals
      }
      return res
    }
  } else {
    return '--'
  }
}

// 1%， 10%， 5%， 免税等 转为 0.01， 0.1， 0.5， 免税
export function convertPercentage(percent) {
  // const percentageMapping = {
  //   '免税': '免税',
  // };

  if (percent.includes('%')) {
    percent = percent.replace('%', '');
    return parseFloat(percent) / 100;
  }
  if (percent.includes('0')) {
    return parseFloat(percent)
  }

  return percent;
}

// 0.01， 0.1， 0.5， 免税等 转为 1%， 10%， 5%，免税
export function convertToPercentage(value) {
  if (typeof value === 'number') {
    return (value * 100).toFixed(0) + "%";
  }

  if (typeof value === 'string') {
    if (value.endsWith("%")) {
      return value;
    }

    const number = parseFloat(value);
    if (!isNaN(number)) {
      return (number * 100).toFixed(0) + "%";
    }
  }

  return value;
}
//四舍五入保留2位小数（若第二位小数为0，则保留一位小数）
export function keepTwoDecimal(num) {
  var result = parseFloat(num);
  if (isNaN(result)) {
    console.error('参数非数值，无法四舍五入保留两位小数！');
    return false;
  }
  result = Math.round(num * 100) / 100;
  return result;
}
//四舍五入保留2位小数（不够位数，则用0替补）
export function keepTwoDecimalFull(num) {
  var result = parseFloat(num);
  if (isNaN(result)) {
    console.error('参数非数值，无法四舍五入保留两位小数！');
    return false;
  }
  result = Math.round(num * 100) / 100;
  var s_x = result.toString();
  var pos_decimal = s_x.indexOf('.');
  if (pos_decimal < 0) {
    pos_decimal = s_x.length;
    s_x += '.';
  }
  while (s_x.length <= pos_decimal + 2) {
    s_x += '0';
  }
  return s_x;
}
//四舍五入保留10位小数（不够位数，则用0替补）
export function keepTenDecimalFull(num) {
  var result = parseFloat(num);
  if (isNaN(result)) {
    console.error('参数非数值，无法四舍五入保留十位小数！');
    return false;
  }
  result = Math.round(num * 1000000000000) / 1000000000000;
  return result;
}
