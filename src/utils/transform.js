/**
 * transform 转换器
 * 当前文件下的方法名称皆以 [动词(get/replace..)] + [名词] 进行命名
 */

/**
 * ArrayBuffer 二进制流转图片base64
 * @param {object} buffer
 */
export function getBase64FromArrayBuffer(buffer) {
  var binary = ''
  var bytes = new Uint8Array(buffer)
  var len = bytes.byteLength
  for (var i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return 'data:image/png;base64,' + window.btoa(binary)
}
