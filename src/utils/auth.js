import Cookies from 'js-cookie'

const TokenKey = 'token'
const TypesKey = 'user_types'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getTypes() {
  return Cookies.get(TypesKey)
}

export function setTypes(types) {
  return Cookies.set(TypesKey, types)
}

export function removeTypes() {
  return Cookies.remove(TypesKey)
}
