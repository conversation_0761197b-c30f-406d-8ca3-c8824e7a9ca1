<template>
  <div class="meal-setting">
    <el-tabs type="card">
      
        <div class="setting-section">
          <div class="section-header">
            <i class="el-icon-date"></i> 开餐日期
          </div>
          
          <div class="open-date-config">
            <div class="config-item">
              <span class="item-label">开餐模式：</span>
              <el-radio-group v-model="openMode">
                <el-radio :label="1">自由开餐</el-radio>
                <el-radio :label="2">每日开餐</el-radio>
                <el-radio :label="3">周一到周五开餐</el-radio>
                <el-radio :label="4">使用国家法定节假日规则</el-radio>
              </el-radio-group>
            </div>
            
            <div class="config-item">
              <span class="item-label">选择开餐日期：</span>
              <el-checkbox-group v-model="openDays">
                <el-checkbox label="1">周一</el-checkbox>
                <el-checkbox label="2">周二</el-checkbox>
                <el-checkbox label="3">周三</el-checkbox>
                <el-checkbox label="4">周四</el-checkbox>
                <el-checkbox label="5">周五</el-checkbox>
                <el-checkbox label="6">周六</el-checkbox>
                <el-checkbox label="0">周日</el-checkbox>
              </el-checkbox-group>
            </div>
            
            <div class="config-item">
              <span class="item-label">停餐日期：</span>
              <el-switch v-model="closeEnable" active-color="#13ce66"></el-switch>
              <el-button size="mini" type="text" v-if="closeEnable">查看列表</el-button>
            </div>
            
            <div class="config-item">
              <span class="item-label">报餐周期：</span>
              <el-input-number v-model="orderDays" :min="1" :max="30" size="small"></el-input-number>
              <span class="unit">天报餐</span>
            </div>
            
            <div class="config-item text-center">
              <el-button type="primary">保存更新</el-button>
            </div>
          </div>
        </div>
        
        <div class="setting-section">
          <div class="section-header">
            <i class="el-icon-time"></i> 报餐时段
          </div>
          
          <el-tabs type="border-card" class="time-tabs">
            <el-tab-pane label="早餐"></el-tab-pane>
            <el-tab-pane label="午餐" class="active-tab"></el-tab-pane>
            <el-tab-pane label="晚餐"></el-tab-pane>
          </el-tabs>
          
          <div class="action-bar">
            <el-button type="success" size="small">添加时段</el-button>
            <span class="tip">最多支持6个时段</span>
          </div>
          
          <el-table :data="timeRanges" border style="width: 100%">
            <el-table-column prop="id" label="序号" width="60" align="center"></el-table-column>
            <el-table-column prop="mealType" label="餐餐类型" width="100" align="center"></el-table-column>
            <el-table-column prop="settingTime" label="报餐截止时间" align="center"></el-table-column>
            <el-table-column prop="maxCount" label="可报餐数" width="100" align="center"></el-table-column>
            <el-table-column prop="timeRange" label="报餐时段" align="center"></el-table-column>
            <el-table-column prop="startTime" label="就餐开始时间" align="center"></el-table-column>
            <el-table-column prop="endTime" label="就餐结束时间" align="center"></el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button type="primary" size="mini">编辑</el-button>
                <el-button type="danger" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="setting-section">
          <div class="section-header">
            <i class="el-icon-set-up"></i> 报餐提醒
          </div>
          
          <div class="action-bar">
            <el-button type="success" size="small">添加提醒</el-button>
          </div>
          
          <div class="reminder-list">
            <div class="reminder-card">
              <div class="card-header">
                <span>提醒1</span>
                <div class="operations">
                  <el-button type="text" size="mini">保存</el-button>
                  <el-button type="text" size="mini" class="danger">删除</el-button>
                </div>
              </div>
              <div class="card-content">
                <div class="reminder-item">
                  <span>提醒周期：</span>
                  <el-checkbox-group v-model="reminder1Days">
                    <el-checkbox label="1">周一</el-checkbox>
                    <el-checkbox label="2">周二</el-checkbox>
                    <el-checkbox label="3">周三</el-checkbox>
                    <el-checkbox label="4">周四</el-checkbox>
                    <el-checkbox label="5">周五</el-checkbox>
                    <el-checkbox label="6">周六</el-checkbox>
                    <el-checkbox label="0">周日</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="reminder-item">
                  <span>提醒时间：</span>
                  <el-time-picker v-model="reminder1Time" placeholder="选择时间" format="HH:mm"></el-time-picker>
                </div>
              </div>
            </div>
            
            <div class="reminder-card">
              <div class="card-header">
                <span>提醒2</span>
                <div class="operations">
                  <el-button type="text" size="mini">保存</el-button>
                  <el-button type="text" size="mini" class="danger">删除</el-button>
                </div>
              </div>
              <div class="card-content">
                <div class="reminder-item">
                  <span>提醒周期：</span>
                  <el-checkbox-group v-model="reminder2Days">
                    <el-checkbox label="1">周一</el-checkbox>
                    <el-checkbox label="2">周二</el-checkbox>
                    <el-checkbox label="3">周三</el-checkbox>
                    <el-checkbox label="4">周四</el-checkbox>
                    <el-checkbox label="5">周五</el-checkbox>
                    <el-checkbox label="6">周六</el-checkbox>
                    <el-checkbox label="0">周日</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="reminder-item">
                  <span>提醒时间：</span>
                  <el-time-picker v-model="reminder2Time" placeholder="选择时间" format="HH:mm"></el-time-picker>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="setting-section">
          <div class="section-header">
            <i class="el-icon-s-tools"></i> 规则限制
          </div>
          
          <el-tabs type="border-card" class="rule-tabs">
            <el-tab-pane label="个人自助餐" class="active-tab"></el-tab-pane>
            <el-tab-pane label="员工自助餐"></el-tab-pane>
          </el-tabs>
          
          <div class="rule-config">
            <div class="rule-item">
              <span class="rule-label">报餐份数限制：</span>
              <el-radio-group v-model="limitType">
                <el-radio :label="1">默认每餐只允许报1份</el-radio>
                <el-radio :label="2">不限制报餐份数</el-radio>
              </el-radio-group>
            </div>
            
            <div class="rule-item">
              <span class="rule-label">取消报餐截止时间：</span>
              <el-select v-model="cancelDay" placeholder="请选择">
                <el-option label="当天" value="today"></el-option>
                <el-option label="前一天" value="yesterday"></el-option>
              </el-select>
              <el-time-picker v-model="cancelTime" placeholder="选择时间" format="HH:mm"></el-time-picker>
            </div>
            
            <div class="rule-item">
              <span class="rule-label">就餐结束自动关闭时间：</span>
              <el-radio-group v-model="autoCloseType">
                <el-radio :label="1">按就餐结束时间自动关闭，未就餐的自动失效</el-radio>
                <el-radio :label="2">指定当天关闭时间</el-radio>
              </el-radio-group>
              <el-time-picker v-model="closeTime" placeholder="选择时间" format="HH:mm" v-if="autoCloseType === 2"></el-time-picker>
            </div>
            
            <div class="rule-item text-center">
              <el-button type="primary">保存更新</el-button>
            </div>
          </div>
        </div>
      
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'MealSetting',
  data() {
    return {
      openMode: 1,
      openDays: ['1', '2', '3', '4', '5', '6'],
      closeEnable: false,
      orderDays: 7,
      
      timeRanges: [
        { id: 1, mealType: '午餐', settingTime: '1天前22:00', maxCount: 30, timeRange: '11:45-12:00', startTime: '11:00', endTime: '15:00' },
        { id: 2, mealType: '午餐', settingTime: '1天前22:00', maxCount: 30, timeRange: '12:00-12:15', startTime: '11:00', endTime: '15:00' },
        { id: 3, mealType: '午餐', settingTime: '0天前9:00', maxCount: 30, timeRange: '12:15-12:30', startTime: '11:00', endTime: '15:00' },
        { id: 4, mealType: '午餐', settingTime: '0天前12:30', maxCount: 30, timeRange: '12:30-12:45', startTime: '11:00', endTime: '15:00' },
        { id: 5, mealType: '午餐', settingTime: '0天前12:30', maxCount: 30, timeRange: '12:45-13:00', startTime: '11:00', endTime: '15:00' },
        { id: 6, mealType: '午餐', settingTime: '0天前12:30', maxCount: 30, timeRange: '13:00-13:15', startTime: '11:00', endTime: '15:00' }
      ],
      
      reminder1Days: ['1', '2', '3', '4', '5', '6', '0'],
      reminder1Time: new Date(2023, 1, 1, 17, 0),
      reminder2Days: ['1', '2', '3', '4', '5'],
      reminder2Time: new Date(2023, 1, 1, 9, 0),
      
      limitType: 1,
      cancelDay: 'today',
      cancelTime: new Date(2023, 1, 1, 9, 0),
      autoCloseType: 1,
      closeTime: new Date(2023, 1, 1, 18, 0)
    }
  }
}
</script>

<style lang="less" scoped>
.meal-setting {
  background-color: #fff;
  padding: 20px;
  
  .setting-section {
    margin-bottom: 30px;
    
    .section-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      
      i {
        margin-right: 6px;
        color: #409EFF;
      }
    }
  }
  
  .open-date-config {
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 20px;
    
    .config-item {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      .item-label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
      }
      
      .unit {
        margin-left: 10px;
      }
      
      &.text-center {
        justify-content: center;
        margin-top: 20px;
      }
    }
  }
  
  .time-tabs, .rule-tabs {
    margin-bottom: 15px;
  }
  
  .action-bar {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    
    .tip {
      color: #909399;
      font-size: 12px;
      margin-left: 10px;
    }
  }
  
  .reminder-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    .reminder-card {
      width: 48%;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      overflow: hidden;
      
      .card-header {
        background-color: #F5F7FA;
        padding: 10px 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .operations {
          .danger {
            color: #F56C6C;
          }
        }
      }
      
      .card-content {
        padding: 15px;
        
        .reminder-item {
          margin-bottom: 15px;
          display: flex;
          align-items: center;
          
          span {
            width: 80px;
            margin-right: 10px;
          }
        }
      }
    }
  }
  
  .rule-config {
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 20px;
    
    .rule-item {
      margin-bottom: 20px;
      
      .rule-label {
        display: inline-block;
        width: 160px;
        text-align: right;
        margin-right: 10px;
      }
      
      &.text-center {
        text-align: center;
        margin-top: 20px;
      }
    }
  }
}
</style>