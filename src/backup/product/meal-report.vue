<template>
  <div class="meal-report">
    <h1>自助套餐</h1>

    <!-- 展示报餐产品 -->
    <div class="report-product-list">
      <div class="search-bar">
        <el-input v-model="searchParams.productName" placeholder="产品名称" clearable></el-input>
        <el-input v-model="searchParams.productId" placeholder="产品编号" clearable></el-input>
        <el-input v-model="searchParams.productCode" placeholder="产品编码" clearable></el-input>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <Table
        :data="tableData"
        :columns="tableColumns"
        :loading="tableLoading"
        :pagination="pagination"
        :total="total"
        @pagination="getProductList"
      />
    </div>

    <!-- 新增自助报餐 -->
    <div class="add-report-product">
      <div class="section-title">新增自助报餐</div>
      <div class="product-select">
        <div class="form-item">
          <label>产品名称：</label>
          <el-select v-model="selectedProduct" placeholder="选择产品">
            <el-option
              v-for="item in productOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <div class="product-option">
                <i class="el-icon-folder folder-icon"></i>
                <span>{{ item.label }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>

      <div class="selected-product-list">
        <div class="table-header">
          <span>序号</span>
          <span>产品</span>
          <span>查看文件</span>
          <span>供应商</span>
          <span>价格</span>
          <span>下架</span>
          <span>上架</span>
          <span>操作</span>
        </div>
        <div v-for="(item, index) in selectedProductList" :key="index" class="table-row">
          <span>{{ index + 1 }}</span>
          <span>
            <i class="el-icon-folder folder-icon"></i>
            {{ item.name }}
          </span>
          <span>{{ item.file }}</span>
          <span>{{ item.supplier }}</span>
          <span>{{ item.price }}</span>
          <span>{{ item.offShelf }}</span>
          <span>{{ item.onShelf }}</span>
          <span class="operation">
            <el-button type="text" class="delete-btn">删除</el-button>
          </span>
        </div>
      </div>

      <div class="button-group">
        <el-button type="primary" @click="saveProductList">保存</el-button>
        <el-button @click="cancelAdd">取消</el-button>
      </div>
    </div>

    <!-- 预定报餐 -->
    <div class="meal-booking">
      <div class="section-title">预定报餐</div>
      <div class="calendar-wrapper">
        <div class="calendar-container">
          <div class="calendar-header">
            <span class="prev-month" @click="prevMonth">&lt;</span>
            <span class="current-date">{{ currentYear }}-{{ currentMonth }}</span>
            <span class="next-month" @click="nextMonth">&gt;</span>
          </div>
          <div class="calendar-days">
            <div class="weekday-header">
              <span>日</span>
              <span>一</span>
              <span>二</span>
              <span>三</span>
              <span>四</span>
              <span>五</span>
              <span>六</span>
            </div>
            <div class="days-grid">
              <div
                v-for="day in calendarDays"
                :key="day.date"
                class="day-cell"
                :class="{ 'is-today': day.isToday, 'is-selected': isDateSelected(day) }"
                @click="selectDate(day)"
              >
                <span class="day-number">{{ day.day }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="booking-info">
          <div class="selected-date">
            <div>已选择日期: {{ formattedSelectedDate }}</div>
            <div>报餐用户: <el-input v-model="bookingInfo.username" placeholder="用户名" /></div>
          </div>
          <div class="booking-products">
            <div class="product-list-header">
              <span>选择</span>
              <span>产品</span>
              <span>文件类型</span>
              <span>供应商</span>
              <span>价格</span>
            </div>
            <div v-for="(item, index) in bookingProductList" :key="index" class="product-list-row">
              <el-checkbox v-model="item.selected"></el-checkbox>
              <div class="product-cell">
                <i class="el-icon-folder folder-icon"></i>
                <span>{{ item.name }}</span>
              </div>
              <span>{{ item.fileType }}</span>
              <span>{{ item.supplier }}</span>
              <span>{{ item.price }}</span>
            </div>
          </div>
          <div class="button-group">
            <el-button type="primary" @click="submitBooking">确认</el-button>
            <el-button @click="cancelBooking">取消</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史报餐记录 -->
    <div class="historical-records">
      <div class="section-title">历史报餐记录</div>
      <div class="calendar-wrapper">
        <div class="calendar-container">
          <div class="calendar-header">
            <span class="prev-month" @click="prevMonthHistory">&lt;</span>
            <span class="current-date">{{ historyYear }}-{{ historyMonth }}</span>
            <span class="next-month" @click="nextMonthHistory">&gt;</span>
          </div>
          <div class="calendar-days">
            <div class="weekday-header">
              <span>日</span>
              <span>一</span>
              <span>二</span>
              <span>三</span>
              <span>四</span>
              <span>五</span>
              <span>六</span>
            </div>
            <div class="days-grid">
              <div
                v-for="day in historyCalendarDays"
                :key="day.date"
                class="day-cell"
                :class="{ 'is-today': day.isToday, 'is-selected': day.date === selectedHistoryDate }"
                @click="selectHistoryDate(day)"
              >
                <span class="day-number">{{ day.day }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="history-info">
          <div class="selected-date">
            <div>选择日期: {{ formattedHistoryDate }}</div>
            <div>查询用户: <el-input v-model="historyInfo.username" placeholder="用户名" /></div>
          </div>
          <div class="history-products">
            <div class="product-list-header">
              <span>选择</span>
              <span>产品</span>
              <span>文件类型</span>
              <span>日期</span>
              <span>操作</span>
            </div>
            <div v-for="(item, index) in historyProductList" :key="index" class="product-list-row">
              <el-checkbox v-model="item.selected"></el-checkbox>
              <div class="product-cell">
                <i class="el-icon-folder folder-icon"></i>
                <span>{{ item.name }}</span>
              </div>
              <span>{{ item.fileType }}</span>
              <span>{{ item.date }}</span>
              <span class="operation">
                <el-button type="text" class="delete-btn">删除</el-button>
              </span>
            </div>
          </div>
          <div class="button-group">
            <el-button type="primary" @click="searchHistory">查询</el-button>
            <el-button @click="confirmDelete" type="danger">删除</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table.vue'

export default {
  name: 'MealReport',
  components: {
    Table
  },
  data() {
    return {
      title: '我要报餐',
      searchParams: {
        productName: '',
        productId: '',
        productCode: ''
      },
      tableData: [],
      tableColumns: [
        { hasSelection: true, width: 50 },
        { hasIndex: true, width: 60 },
        { prop: 'productId', label: '产品编号', minWidth: 100 },
        { prop: 'productName', label: '产品名称', minWidth: 120 },
        { prop: 'productCode', label: '产品编码', minWidth: 120 },
        { prop: 'spec', label: '规格', minWidth: 100 },
        { prop: 'price', label: '价格', minWidth: 100 },
        { prop: 'createTime', label: '创建时间', minWidth: 150 }
      ],
      tableLoading: false,
      pagination: {
        page: 1,
        page_size: 10
      },
      total: 0,

      // 新增自助报餐
      selectedProduct: '',
      productOptions: [
        { value: '1', label: '产品1' },
        { value: '2', label: '产品2' },
        { value: '3', label: '产品3' }
      ],
      selectedProductList: [
        { name: '测试产品', file: '预定产品', supplier: '供应商A', price: '￥25.00', offShelf: '下架', onShelf: '上架' },
        { name: '测试产品2', file: '预定产品', supplier: '供应商B', price: '￥30.00', offShelf: '下架', onShelf: '上架' }
      ],

      // 日历相关
      currentDate: new Date(),
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      selectedDate: '',
      bookingInfo: {
        username: ''
      },
      bookingProductList: [
        { name: '餐品A', fileType: '餐饮类', supplier: '供应商A', price: '￥20.00', selected: false },
        { name: '餐品B', fileType: '餐饮类', supplier: '供应商B', price: '￥25.00', selected: false },
        { name: '餐品C', fileType: '餐饮类', supplier: '供应商C', price: '￥30.00', selected: false }
      ],

      // 历史记录
      historyYear: new Date().getFullYear(),
      historyMonth: new Date().getMonth() + 1,
      selectedHistoryDate: '',
      historyInfo: {
        username: ''
      },
      historyProductList: [
        { name: '历史餐品A', fileType: '餐饮类', date: '2023-04-05', selected: false },
        { name: '历史餐品B', fileType: '餐饮类', date: '2023-04-06', selected: false }
      ]
    }
  },
  computed: {
    calendarDays() {
      return this.generateCalendarDays(this.currentYear, this.currentMonth - 1)
    },
    historyCalendarDays() {
      return this.generateCalendarDays(this.historyYear, this.historyMonth - 1)
    },
    formattedSelectedDate() {
      return this.selectedDate ? this.formatDate(new Date(this.selectedDate)) : '未选择'
    },
    formattedHistoryDate() {
      return this.selectedHistoryDate ? this.formatDate(new Date(this.selectedHistoryDate)) : '未选择'
    }
  },
  mounted() {
    this.getProductList()
  },
  methods: {
    handleSearch() {
      this.pagination.page = 1
      this.getProductList()
    },
    resetSearch() {
      this.searchParams = {
        productName: '',
        productId: '',
        productCode: ''
      }
      this.handleSearch()
    },
    getProductList() {
      this.tableLoading = true
      // 模拟API请求
      setTimeout(() => {
        this.tableData = Array(10).fill(0).map((_, index) => ({
          id: index + 1,
          productId: `P${100 + index}`,
          productName: `产品${index + 1}`,
          productCode: `CODE${200 + index}`,
          spec: `${10 + index}kg/${20 + index}kg`,
          price: `￥${20 + index * 5}.00`,
          createTime: '2023-04-05 12:00:00'
        }))
        this.total = 100
        this.tableLoading = false
      }, 500)
    },

    // 新增自助报餐
    saveProductList() {
      this.$message.success('保存成功')
    },
    cancelAdd() {
      this.selectedProduct = ''
    },

    // 日历相关方法
    generateCalendarDays(year, month) {
      const daysInMonth = new Date(year, month + 1, 0).getDate()
      const firstDayOfMonth = new Date(year, month, 1).getDay()

      const days = []
      // 填充月初的空白
      for (let i = 0; i < firstDayOfMonth; i++) {
        days.push({ day: '', date: null, isEmpty: true })
      }

      // 填充日期
      const today = new Date().toDateString()
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i)
        days.push({
          day: i,
          date: date.toISOString().split('T')[0],
          isToday: date.toDateString() === today,
          isEmpty: false
        })
      }

      return days
    },
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--
        this.currentMonth = 12
      } else {
        this.currentMonth--
      }
    },
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++
        this.currentMonth = 1
      } else {
        this.currentMonth++
      }
    },
    selectDate(day) {
      if (!day.isEmpty) {
        this.selectedDate = day.date
      }
    },
    isDateSelected(day) {
      return day.date === this.selectedDate
    },
    formatDate(date) {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },
    submitBooking() {
      const selectedProducts = this.bookingProductList.filter(item => item.selected)
      if (selectedProducts.length === 0) {
        this.$message.warning('请至少选择一个产品')
        return
      }

      if (!this.selectedDate) {
        this.$message.warning('请选择日期')
        return
      }

      this.$message.success('报餐成功')
    },
    cancelBooking() {
      this.bookingProductList.forEach(item => {
        item.selected = false
      })
      this.selectedDate = ''
      this.bookingInfo.username = ''
    },

    // 历史记录相关方法
    prevMonthHistory() {
      if (this.historyMonth === 1) {
        this.historyYear--
        this.historyMonth = 12
      } else {
        this.historyMonth--
      }
    },
    nextMonthHistory() {
      if (this.historyMonth === 12) {
        this.historyYear++
        this.historyMonth = 1
      } else {
        this.historyMonth++
      }
    },
    selectHistoryDate(day) {
      if (!day.isEmpty) {
        this.selectedHistoryDate = day.date
      }
    },
    searchHistory() {
      if (!this.selectedHistoryDate) {
        this.$message.warning('请选择日期')
        return
      }

      // 模拟API请求
      this.$message.success('查询成功')
    },
    confirmDelete() {
      const selectedProducts = this.historyProductList.filter(item => item.selected)
      if (selectedProducts.length === 0) {
        this.$message.warning('请至少选择一个产品')
        return
      }

      this.$confirm('确认删除选中的产品记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="less" scoped>
.meal-report {
  background-color: #fff;
  padding: 20px;

  h1 {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
    padding-left: 10px;
    border-left: 4px solid #409EFF;
  }

  .search-bar {
    display: flex;
    margin-bottom: 20px;

    .el-input {
      width: 200px;
      margin-right: 15px;
    }
  }

  .report-product-list,
  .add-report-product,
  .meal-booking,
  .historical-records {
    margin-bottom: 20px;
    padding-bottom: 15px;
  }

  .product-select {
    margin-bottom: 15px;

    .form-item {
      display: flex;
      align-items: center;

      label {
        width: 80px;
      }

      .el-select {
        width: 300px;
      }
    }
  }

  .product-option {
    display: flex;
    align-items: center;

    .folder-icon {
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }
  }

  .selected-product-list,
  .booking-products,
  .history-products {
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 20px;

    .table-header,
    .product-list-header {
      display: grid;
      grid-template-columns: 60px 1fr 1fr 1fr 1fr 1fr 1fr 80px;
      background-color: #f5f7fa;
      padding: 10px 0;
      font-weight: bold;
      text-align: center;

      &.product-list-header {
        grid-template-columns: 60px 1fr 1fr 1fr 1fr;
      }
    }

    .table-row,
    .product-list-row {
      display: grid;
      grid-template-columns: 60px 1fr 1fr 1fr 1fr 1fr 1fr 80px;
      padding: 10px 0;
      border-top: 1px solid #eee;
      text-align: center;
      align-items: center;

      &.product-list-row {
        grid-template-columns: 60px 1fr 1fr 1fr 1fr;
      }

      .folder-icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }

      .product-cell {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .operation {
        .delete-btn {
          color: #F56C6C;
        }
      }
    }
  }

  .button-group {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }

  .calendar-wrapper {
    display: flex;

    .calendar-container {
      width: 350px;
      border: 1px solid #eee;
      border-radius: 4px;
      margin-right: 20px;

      .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #eee;

        .prev-month, .next-month {
          cursor: pointer;
          font-size: 18px;
        }

        .current-date {
          font-weight: bold;
        }
      }

      .weekday-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        text-align: center;
        padding: 10px 0;
        background-color: #f5f7fa;
      }

      .days-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        grid-gap: 2px;
        padding: 10px;

        .day-cell {
          height: 35px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }

          &.is-today {
            color: #409EFF;
            font-weight: bold;
          }

          &.is-selected {
            background-color: #409EFF;
            color: white;
            border-radius: 4px;
          }
        }
      }
    }

    .booking-info,
    .history-info {
      flex: 1;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 15px;

      .selected-date {
        margin-bottom: 15px;

        div {
          margin-bottom: 10px;
          display: flex;
          align-items: center;

          .el-input {
            width: 200px;
            margin-left: 10px;
          }
        }
      }
    }
  }
}
</style>
