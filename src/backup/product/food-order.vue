<template>
  <div class="food-order">
    <div class="header">
      <h1>我要报餐</h1>
      <div class="tabs">
        <div class="tab active">自助餐</div>
        <div class="tab">服务餐</div>
        <div class="tab">菜品点餐</div>
        <div class="tab">现场点餐</div>
      </div>
    </div>

    <div class="search-area">
      <div class="search-form">
        <div class="form-item">
          <span>产品名称：</span>
          <el-input v-model="searchForm.productName" placeholder=""></el-input>
        </div>
        <div class="form-item">
          <span>产品编码：</span>
          <el-input v-model="searchForm.productCode" placeholder=""></el-input>
        </div>
        <div class="form-item">
          <span>产品类型：</span>
          <el-select v-model="searchForm.productType" placeholder="全部">
            <el-option label="全部" value=""></el-option>
          </el-select>
        </div>
        <div class="form-item">
          <span>产品状态：</span>
          <el-select v-model="searchForm.productStatus" placeholder="全部">
            <el-option label="全部" value=""></el-option>
          </el-select>
        </div>
        <div class="form-action">
          <el-button type="primary">查询</el-button>
          <el-button>重置</el-button>
        </div>
      </div>

      <div class="operate-buttons">
        <el-button type="success" icon="el-icon-plus">新增产品</el-button>
        <el-button type="danger" icon="el-icon-delete">批量删除</el-button>
        <el-button type="warning" icon="el-icon-upload2">批量上架</el-button>
        <el-button type="info" icon="el-icon-download">批量下架</el-button>
      </div>
    </div>

    <div class="product-table">
      <Table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :total="total"
        :loading="loading"
        @pagination="handlePagination"
      >
        <el-table-column slot="productImage" label="产品图片" align="center" width="100">
          <template slot-scope="scope">
            <img class="product-img" :src="scope.row.image" alt="产品图片" />
          </template>
        </el-table-column>

        <el-table-column slot="actions" label="操作" width="240" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
            <el-button v-if="scope.row.status === '已上架'" type="text" size="small" class="error-text" @click="handleOffShelve(scope.row)">取消发布</el-button>
            <el-button v-else type="text" size="small" class="success-text" @click="handlePublish(scope.row)">产品发布</el-button>
          </template>
        </el-table-column>
      </Table>
    </div>

    <!-- 菜品仓库部分 -->
    <div class="food-repository">
      <div class="header">
        <h1>菜品库</h1>
        <div class="food-types">
          <div class="type-item active">
            <i class="el-icon-dish"></i>
            <span>主食</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>蜜饯</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>汤类</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>水果</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>主菜</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>饮料</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>下午茶</span>
          </div>
          <div class="type-item">
            <i class="el-icon-dish"></i>
            <span>点心</span>
          </div>
        </div>
      </div>

      <div class="repository-search">
        <el-button type="primary" icon="el-icon-plus">批量审核</el-button>
        <div class="search-right">
          <span>菜品名称：</span>
          <el-input v-model="repositorySearch.name" placeholder=""></el-input>
          <span>菜品编码：</span>
          <el-input v-model="repositorySearch.code" placeholder=""></el-input>
          <el-button type="primary">搜索</el-button>
        </div>
      </div>

      <div class="repository-table">
        <Table
          :columns="repositoryColumns"
          :data="repositoryData"
          :pagination="repositoryPagination"
          :total="repositoryTotal"
          :loading="repositoryLoading"
          @pagination="handleRepositoryPagination"
        >
          <el-table-column slot="foodImage" label="菜品图片" align="center" width="100">
            <template slot-scope="scope">
              <img class="food-img" :src="scope.row.image" alt="菜品图片" />
            </template>
          </el-table-column>

          <el-table-column slot="repositoryActions" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button v-if="scope.row.status === '已审定'" type="text" size="small" class="success-text">已审定</el-button>
              <el-button v-else type="text" size="small" @click="handleApprove(scope.row)">审定</el-button>
            </template>
          </el-table-column>
        </Table>
      </div>
    </div>

    <!-- 确认审定菜品部分 -->
    <div class="confirm-food">
      <div class="header">
        <h1>确认审定菜品</h1>
      </div>

      <div class="confirm-table">
        <Table
          :columns="confirmColumns"
          :data="confirmData"
          :pagination="confirmPagination"
          :total="confirmTotal"
          :loading="confirmLoading"
          @pagination="handleConfirmPagination"
        >
          <el-table-column slot="confirmFoodImage" label="菜品图片" align="center" width="100">
            <template slot-scope="scope">
              <img class="food-img" :src="scope.row.image" alt="菜品图片" />
            </template>
          </el-table-column>

          <el-table-column slot="confirmPrice" label="菜品价格" width="150" align="center">
            <template slot-scope="scope">
              <div class="price-input">
                <span>{{ scope.row.price }}</span>
                <span>元</span>
              </div>
            </template>
          </el-table-column>
        </Table>
      </div>

      <div class="confirm-buttons">
        <el-button>取消</el-button>
        <el-button type="primary">确认审定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table.vue'

export default {
  name: 'FoodOrder',
  components: {
    Table
  },
  data() {
    return {
      title: '菜品点餐',
      loading: false,
      searchForm: {
        productName: '',
        productCode: '',
        productType: '',
        productStatus: ''
      },
      total: 0,
      pagination: {
        page: 1,
        page_size: 10
      },
      columns: [
        { hasSelection: true },
        { hasIndex: true },
        { slot: 'productImage' },
        { prop: 'productName', label: '产品名称', minWidth: 120 },
        { prop: 'price', label: '产品价格', minWidth: 80 },
        { prop: 'type', label: '产品类型', minWidth: 80 },
        { prop: 'ingredients', label: '产品成分', minWidth: 120 },
        { prop: 'calorie', label: '产品热量', minWidth: 80 },
        { prop: 'status', label: '产品状态', minWidth: 80 },
        { prop: 'updateTime', label: '产品更新时间', minWidth: 150 },
        { slot: 'actions' }
      ],
      tableData: [
        { id: 1, productCode: '20250611', productName: '汗蒸豆腐', price: '20元/份', type: '主菜', ingredients: '鲜豆腐, 可能', calorie: '20', status: '已上架', updateTime: '按规定节假日', image: '' },
        { id: 2, productCode: '20250612', productName: '剁椒炒南瓜', price: '18元/份', type: '主菜', ingredients: '南瓜, 剁椒', calorie: '18', status: '已上架', updateTime: '按规定节假日', image: '' },
        { id: 3, productCode: '20250613', productName: '虎跃炒南山', price: '20元/份', type: '主菜', ingredients: '青椒, 南山', calorie: '20', status: '已上架', updateTime: '按规定节假日', image: '' },
        { id: 4, productCode: '20250614', productName: '条瓜炒', price: '15元/份', type: '炒菜', ingredients: '冬瓜, 胡萝卜, 葱姜', calorie: '15', status: '已上架', updateTime: '按规定节假日', image: '' },
        { id: 5, productCode: '20250615', productName: '米饭', price: '3元/碗', type: '主食', ingredients: '--', calorie: '3', status: '未上架', updateTime: '--', image: '' },
        { id: 6, productCode: '20250616', productName: '苹果', price: '3元/份', type: '水果', ingredients: '--', calorie: '3', status: '未上架', updateTime: '--', image: '' }
      ],

      // 菜品仓库数据
      repositoryLoading: false,
      repositorySearch: {
        name: '',
        code: ''
      },
      repositoryTotal: 0,
      repositoryPagination: {
        page: 1,
        page_size: 10
      },
      repositoryColumns: [
        { hasSelection: true },
        { prop: 'index', label: '菜品序号', width: 80 },
        { prop: 'code', label: '菜品编码', width: 100 },
        { slot: 'foodImage' },
        { prop: 'name', label: '菜品名称', minWidth: 120 },
        { prop: 'ingredients', label: '菜品成分', minWidth: 120 },
        { prop: 'type', label: '菜品类型', minWidth: 80 },
        { prop: 'status', label: '菜品状态', minWidth: 80 },
        { slot: 'repositoryActions' }
      ],
      repositoryData: [
        { id: 1, index: 1, code: '0025', name: '汗蒸豆腐', ingredients: '鲜豆腐, 可能', type: '主菜', status: '上架', image: '' },
        { id: 2, index: 2, code: '0023', name: '剁椒炒南瓜', ingredients: '南瓜, 剁椒', type: '主菜', status: '上架', image: '' },
        { id: 3, index: 3, code: '0005', name: '虎跃炒南山', ingredients: '青椒, 南山', type: '主菜', status: '上架', image: '' },
        { id: 4, index: 4, code: '0008', name: '炸藕丝', ingredients: '藕粉, 调味', type: '主菜', status: '上架', image: '' },
        { id: 5, index: 5, code: '0045', name: '炒杏心', ingredients: '杏心', type: '主菜', status: '上架', image: '' },
        { id: 6, index: 6, code: '0036', name: '炒芹菜', ingredients: '芹菜', type: '主菜', status: '上架', image: '' }
      ],

      // 确认审定菜品数据
      confirmLoading: false,
      confirmTotal: 0,
      confirmPagination: {
        page: 1,
        page_size: 10
      },
      confirmColumns: [
        { prop: 'index', label: '菜品序号', width: 80 },
        { prop: 'code', label: '菜品编码', width: 100 },
        { slot: 'confirmFoodImage' },
        { prop: 'name', label: '菜品名称', minWidth: 120 },
        { prop: 'ingredients', label: '菜品成分', minWidth: 120 },
        { prop: 'type', label: '菜品类型', minWidth: 80 },
        { prop: 'status', label: '菜品状态', minWidth: 80 },
        { slot: 'confirmPrice' }
      ],
      confirmData: [
        { id: 1, index: 1, code: '0025', name: '汗蒸豆腐', ingredients: '鲜豆腐, 可能', type: '主菜', status: '上架', price: '25', image: '' },
        { id: 2, index: 2, code: '0023', name: '剁椒炒南瓜', ingredients: '南瓜, 剁椒', type: '主菜', status: '上架', price: '', image: '' },
        { id: 3, index: 3, code: '0005', name: '虎跃炒南山', ingredients: '青椒, 南山', type: '主菜', status: '上架', price: '', image: '' },
        { id: 4, index: 4, code: '0008', name: '炸藕丝', ingredients: '藕粉, 调味', type: '主菜', status: '上架', price: '', image: '' }
      ]
    }
  },
  methods: {
    handlePagination(pagination) {
      this.pagination = pagination
      // 这里应该调用获取数据的方法
    },
    handleRepositoryPagination(pagination) {
      this.repositoryPagination = pagination
      // 这里应该调用获取数据的方法
    },
    handleConfirmPagination(pagination) {
      this.confirmPagination = pagination
      // 这里应该调用获取数据的方法
    },
    handleEdit(row) {
      console.log('编辑', row)
    },
    handleDelete(row) {
      console.log('删除', row)
    },
    handleOffShelve(row) {
      console.log('下架', row)
    },
    handlePublish(row) {
      console.log('发布', row)
    },
    handleApprove(row) {
      console.log('审定', row)
    }
  }
}
</script>

<style lang="less" scoped>
.food-order {
  background-color: #fff;
  padding: 20px;

  .header {
    margin-bottom: 20px;

    h1 {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .tabs {
      display: flex;
      border-bottom: 1px solid #ddd;

      .tab {
        padding: 10px 20px;
        cursor: pointer;

        &.active {
          color: #409EFF;
          border-bottom: 2px solid #409EFF;
        }
      }
    }
  }

  .search-area {
    margin-bottom: 20px;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 15px;

      .form-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
        margin-bottom: 10px;

        span {
          margin-right: 5px;
          white-space: nowrap;
        }

        .el-input, .el-select {
          width: 180px;
        }
      }

      .form-action {
        margin-left: auto;
      }
    }

    .operate-buttons {
      margin-bottom: 15px;

      .el-button {
        margin-right: 10px;
      }
    }
  }

  .product-table, .repository-table, .confirm-table {
    margin-bottom: 30px;

    .product-img, .food-img {
      width: 50px;
      height: 50px;
      object-fit: cover;
      border-radius: 4px;
    }

    .error-text {
      color: #F56C6C;
    }

    .success-text {
      color: #67C23A;
    }
  }

  .food-repository {
    margin-top: 30px;

    .food-types {
      display: flex;
      flex-wrap: wrap;
      margin-top: 15px;

      .type-item {
        width: 100px;
        height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-right: 15px;
        margin-bottom: 15px;
        cursor: pointer;

        i {
          font-size: 30px;
          margin-bottom: 10px;
        }

        &.active {
          border-color: #409EFF;
          color: #409EFF;
        }
      }
    }

    .repository-search {
      display: flex;
      align-items: center;
      margin: 20px 0;

      .search-right {
        display: flex;
        align-items: center;
        margin-left: auto;

        span {
          margin-right: 5px;
          margin-left: 15px;
        }

        .el-input {
          width: 180px;
          margin-right: 10px;
        }
      }
    }
  }

  .confirm-food {
    margin-top: 30px;

    .price-input {
      display: flex;
      align-items: center;
      justify-content: center;

      span:last-child {
        margin-left: 5px;
      }
    }

    .confirm-buttons {
      display: flex;
      justify-content: center;
      margin-top: 20px;

      .el-button {
        margin: 0 10px;
      }
    }
  }
}
</style>
