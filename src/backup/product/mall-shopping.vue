<template>
  <div class="mall-shopping">
    <h1>商城购物</h1>
    
    <!-- 商城物品表格 -->
    <div class="product-section">
      <div class="operation-bar">
        <el-button type="success" icon="el-icon-plus">新增商品</el-button>
        <el-button type="danger" icon="el-icon-delete">批量删除</el-button>
        
        <div class="search-area">
          <span>商品名称：</span>
          <el-input v-model="searchForm.productName" placeholder="" class="input-field"></el-input>
          
          <span>商品编码：</span>
          <el-input v-model="searchForm.productCode" placeholder="" class="input-field"></el-input>
          
          <el-button type="primary">检索</el-button>
        </div>
      </div>
      
      <el-table
        :data="productList"
        border
        style="width: 100%">
        <el-table-column
          type="index"
          label="商品序号"
          width="80"
          align="center">
        </el-table-column>
        <el-table-column
          prop="productCode"
          label="商品编码"
          width="100">
        </el-table-column>
        <el-table-column
          prop="productImage"
          label="商品图片"
          width="100">
          <template slot-scope="scope">
            <img :src="scope.row.productImage" class="product-image" />
          </template>
        </el-table-column>
        <el-table-column
          prop="productName"
          label="商品名称">
        </el-table-column>
        <el-table-column
          prop="productSpec"
          label="商品规格">
        </el-table-column>
        <el-table-column
          prop="productIntro"
          label="商品简介">
        </el-table-column>
        <el-table-column
          prop="productCategory"
          label="商品类型">
        </el-table-column>
        <el-table-column
          prop="productPrice"
          label="商品价格(元)">
        </el-table-column>
        <el-table-column
          prop="productStatus"
          label="商品状态">
        </el-table-column>
        <el-table-column
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" class="edit-btn">编辑</el-button>
            <el-button type="text" size="small" class="delete-btn">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
    
    <!-- 商品库表格 -->
    <div class="product-storage">
      <h1>商品库</h1>
      
      <div class="operation-bar">
        <el-button type="success" icon="el-icon-plus">新增商品</el-button>
        
        <div class="search-area">
          <span>商品名称：</span>
          <el-input v-model="storageSearchForm.productName" placeholder="" class="input-field"></el-input>
          
          <span>商品编码：</span>
          <el-input v-model="storageSearchForm.productCode" placeholder="" class="input-field"></el-input>
          
          <el-button type="primary">检索</el-button>
        </div>
      </div>
      
      <el-table
        :data="storageList"
        border
        style="width: 100%">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          type="index"
          label="序号"
          width="60">
        </el-table-column>
        <el-table-column
          prop="productCode"
          label="商品编码">
        </el-table-column>
        <el-table-column
          prop="productImage"
          label="商品图片">
          <template slot-scope="scope">
            <img :src="scope.row.productImage" class="product-image" />
          </template>
        </el-table-column>
        <el-table-column
          prop="productName"
          label="商品名称">
        </el-table-column>
        <el-table-column
          prop="productSpec"
          label="商品规格">
        </el-table-column>
        <el-table-column
          prop="productIntro"
          label="商品简介">
        </el-table-column>
        <el-table-column
          prop="productCategory"
          label="商品类型">
        </el-table-column>
        <el-table-column
          prop="productStatus"
          label="商品状态">
        </el-table-column>
        <el-table-column
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" class="edit-btn">已修改</el-button>
            <el-button type="text" size="small" class="remove-btn">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          @size-change="handleStorageSizeChange"
          @current-change="handleStorageCurrentChange"
          :current-page="storagePagination.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="storagePagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="storageTotal">
        </el-pagination>
      </div>
    </div>
    
    <!-- 商品绑定确认 -->
    <div class="confirm-products">
      <h1>确认绑定商品</h1>
      
      <el-table
        :data="confirmList"
        border
        style="width: 100%">
        <el-table-column
          type="index"
          label="商品序号"
          width="80">
        </el-table-column>
        <el-table-column
          prop="productCode"
          label="商品编码">
        </el-table-column>
        <el-table-column
          prop="productImage"
          label="商品图片">
          <template slot-scope="scope">
            <img :src="scope.row.productImage" class="product-image" />
          </template>
        </el-table-column>
        <el-table-column
          prop="productName"
          label="商品名称">
        </el-table-column>
        <el-table-column
          prop="productSpec"
          label="商品规格">
        </el-table-column>
        <el-table-column
          prop="productIntro"
          label="商品简介">
        </el-table-column>
        <el-table-column
          prop="productCategory"
          label="商品类型">
        </el-table-column>
        <el-table-column
          prop="productStatus"
          label="商品状态">
        </el-table-column>
        <el-table-column
          prop="productPrice"
          label="商品价格">
          <template slot-scope="scope">
            <el-input v-model="scope.row.productPrice" size="mini" class="price-input"></el-input>
            <span>元</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          @size-change="handleConfirmSizeChange"
          @current-change="handleConfirmCurrentChange"
          :current-page="confirmPagination.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="confirmPagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="confirmTotal">
        </el-pagination>
      </div>
      
      <div class="confirm-buttons">
        <el-button>取消</el-button>
        <el-button type="primary">确认绑定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MallShopping',
  data() {
    return {
      title: '商城购物',
      searchForm: {
        productName: '',
        productCode: ''
      },
      storageSearchForm: {
        productName: '',
        productCode: ''
      },
      productList: [
        {
          productCode: '0023',
          productImage: require('@/assets/images/38.png'),
          productName: '李鸡饺子',
          productSpec: '500g/盒',
          productIntro: '香葱、口感十足',
          productCategory: '食材类',
          productPrice: '18',
          productStatus: '上架'
        },
        {
          productCode: '0045',
          productImage: require('@/assets/images/33.png'),
          productName: '脆皮饺子',
          productSpec: '500g/盒',
          productIntro: '香脆、酸辣可口',
          productCategory: '食材类',
          productPrice: '15',
          productStatus: '上架'
        }
      ],
      pagination: {
        page: 1,
        pageSize: 10
      },
      total: 2,
      
      storageList: [
        {
          productCode: '0025',
          productImage: require('@/assets/images/38.png'),
          productName: '李鸡饺子',
          productSpec: '500g/盒',
          productIntro: '香葱、口感十足',
          productCategory: '食材类',
          productStatus: '上架'
        },
        {
          productCode: '0023',
          productImage: require('@/assets/images/33.png'),
          productName: '脆皮饺子',
          productSpec: '500g/盒',
          productIntro: '香脆、酸辣可口',
          productCategory: '食材类',
          productStatus: '上架'
        },
        {
          productCode: '0005',
          productImage: require('@/assets/images/18.png'),
          productName: '辣椒酱',
          productSpec: '300ml/瓶',
          productIntro: '辣椒、芝麻',
          productCategory: '食材类',
          productStatus: '上架'
        },
        {
          productCode: '0008',
          productImage: require('@/assets/images/9.png'),
          productName: '辣椒油',
          productSpec: '600ml/瓶',
          productIntro: '',
          productCategory: '食材类',
          productStatus: '上架'
        }
      ],
      storagePagination: {
        page: 1,
        pageSize: 10
      },
      storageTotal: 4,
      
      confirmList: [
        {
          productCode: '0005',
          productImage: require('@/assets/images/18.png'),
          productName: '辣椒酱',
          productSpec: '300ml/瓶',
          productIntro: '辣椒、芝麻',
          productCategory: '食材类',
          productStatus: '上架',
          productPrice: '25'
        },
        {
          productCode: '0008',
          productImage: require('@/assets/images/9.png'),
          productName: '辣椒油',
          productSpec: '600ml/瓶',
          productIntro: '',
          productCategory: '食材类',
          productStatus: '上架',
          productPrice: ''
        }
      ],
      confirmPagination: {
        page: 1,
        pageSize: 10
      },
      confirmTotal: 2
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getProductList();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getProductList();
    },
    handleStorageSizeChange(val) {
      this.storagePagination.pageSize = val;
      this.getStorageList();
    },
    handleStorageCurrentChange(val) {
      this.storagePagination.page = val;
      this.getStorageList();
    },
    handleConfirmSizeChange(val) {
      this.confirmPagination.pageSize = val;
      this.getConfirmList();
    },
    handleConfirmCurrentChange(val) {
      this.confirmPagination.page = val;
      this.getConfirmList();
    },
    getProductList() {
      // 调用API获取商品列表
    },
    getStorageList() {
      // 调用API获取商品库列表
    },
    getConfirmList() {
      // 调用API获取待确认商品列表
    }
  }
}
</script>

<style lang="less" scoped>
.mall-shopping {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: bold;
  }
  
  .product-section, .product-storage, .confirm-products {
    margin-bottom: 30px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }
  
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 15px;
    
    .search-area {
      display: flex;
      align-items: center;
      
      span {
        margin-right: 5px;
        white-space: nowrap;
      }
      
      .input-field {
        width: 150px;
        margin-right: 15px;
      }
    }
  }
  
  .product-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
  }
  
  .pagination-container {
    padding: 10px;
    text-align: right;
  }
  
  .edit-btn {
    color: #409EFF;
  }
  
  .delete-btn, .remove-btn {
    color: #F56C6C;
  }
  
  .price-input {
    width: 80px;
    margin-right: 5px;
  }
  
  .confirm-buttons {
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
</style>
