<template>
  <div class="business-package">
    <el-card>
      <div slot="header" class="clearfix">
        <span>商务套餐</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">新增套餐</el-button>
      </div>
      
      <div class="filter-container">
        <el-input v-model="searchForm.keyword" placeholder="套餐名称" style="width: 200px;" class="filter-item" />
        <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
        <el-button class="filter-item" @click="resetSearch">重置</el-button>
      </div>
      
      <el-table
        v-loading="listLoading"
        :data="tableData"
        border
        fit
        highlight-current-row
        style="width: 100%;">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="序号" width="65" align="center" />
        <el-table-column prop="packageName" label="套餐名称" align="center">
          <template slot-scope="{row}">
            <div class="package-info">
              <el-avatar shape="square" size="medium" :src="row.imageUrl || defaultImage"></el-avatar>
              <span class="ml-10">{{ row.packageName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="packageType" label="套餐类型" width="120" align="center" />
        <el-table-column prop="packageSpec" label="套餐规格" width="120" align="center" />
        <el-table-column prop="price" label="套餐单价" width="120" align="center">
          <template slot-scope="{row}">
            {{ row.price }}元/份
          </template>
        </el-table-column>
        <el-table-column prop="inventory" label="套餐库存" width="100" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="{row}">
            <el-tag :type="row.status === '已上架' ? 'success' : 'info'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleEdit(row)">编辑</el-button>
            <el-button v-if="row.status === '已下架'" type="success" size="mini" @click="handlePublish(row)">上架</el-button>
            <el-button v-else type="info" size="mini" @click="handleUnpublish(row)">下架</el-button>
            <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
    
    <!-- 新增/编辑套餐弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="formData" label-position="right" label-width="120px">
        <el-form-item label="套餐名称" prop="packageName">
          <el-input v-model="formData.packageName" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="套餐图片" prop="imageUrl">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess">
            <img v-if="formData.imageUrl" :src="formData.imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="套餐类型" prop="packageType">
          <el-select v-model="formData.packageType" placeholder="请选择套餐类型">
            <el-option label="标准套餐" value="标准套餐" />
            <el-option label="特色套餐" value="特色套餐" />
            <el-option label="会议套餐" value="会议套餐" />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐规格" prop="packageSpec">
          <el-input v-model="formData.packageSpec" placeholder="例如：500ml/1人份" />
        </el-form-item>
        <el-form-item label="套餐价格" prop="price">
          <el-input-number v-model="formData.price" :precision="2" :min="0" :step="0.5" />
        </el-form-item>
        <el-form-item label="库存数量" prop="inventory">
          <el-input-number v-model="formData.inventory" :min="0" :step="1" />
        </el-form-item>
        <el-form-item label="上架状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="已上架">上架</el-radio>
            <el-radio label="已下架">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="套餐描述">
          <el-input type="textarea" v-model="formData.description" rows="4" placeholder="请输入套餐描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BusinessPackage',
  data() {
    return {
      searchForm: {
        keyword: ''
      },
      listLoading: false,
      tableData: [
        {
          id: 1,
          packageName: '商务套餐A',
          imageUrl: '',
          packageType: '标准套餐',
          packageSpec: '500ml/1人份',
          price: 45.00,
          inventory: 50,
          status: '已上架',
          description: ''
        },
        {
          id: 2,
          packageName: '精品套餐B',
          imageUrl: '',
          packageType: '特色套餐',
          packageSpec: '1L/2-3人份',
          price: 79.00,
          inventory: 30,
          status: '已上架',
          description: ''
        },
        {
          id: 3,
          packageName: '会议套餐C',
          imageUrl: '',
          packageType: '会议套餐',
          packageSpec: '2L/4-6人份',
          price: 149.00,
          inventory: 20,
          status: '已下架',
          description: ''
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogFormVisible: false,
      dialogTitle: '',
      formData: {
        id: undefined,
        packageName: '',
        imageUrl: '',
        packageType: '',
        packageSpec: '',
        price: 0,
        inventory: 0,
        status: '已下架',
        description: ''
      },
      rules: {
        packageName: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
        packageType: [{ required: true, message: '请选择套餐类型', trigger: 'change' }],
        packageSpec: [{ required: true, message: '请输入套餐规格', trigger: 'blur' }],
        price: [{ required: true, message: '请输入套餐价格', trigger: 'blur' }],
        inventory: [{ required: true, message: '请输入库存数量', trigger: 'blur' }]
      },
      defaultImage: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
    }
  },
  methods: {
    handleSearch() {
      // 搜索逻辑
      console.log('搜索关键词:', this.searchForm.keyword)
    },
    resetSearch() {
      this.searchForm.keyword = ''
      this.handleSearch()
    },
    handleSizeChange(val) {
      this.pageSize = val
      // 重新加载数据
    },
    handleCurrentChange(val) {
      this.currentPage = val
      // 重新加载数据
    },
    handleAdd() {
      this.dialogTitle = '新增套餐'
      this.formData = {
        id: undefined,
        packageName: '',
        imageUrl: '',
        packageType: '',
        packageSpec: '',
        price: 0,
        inventory: 0,
        status: '已下架',
        description: ''
      }
      this.dialogFormVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑套餐'
      this.formData = Object.assign({}, row)
      this.dialogFormVisible = true
    },
    handleDelete(row) {
      this.$confirm('确认删除该套餐?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除逻辑
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handlePublish(row) {
      // 上架逻辑
      row.status = '已上架'
      this.$message({
        type: 'success',
        message: '套餐已上架!'
      })
    },
    handleUnpublish(row) {
      // 下架逻辑
      row.status = '已下架'
      this.$message({
        type: 'info',
        message: '套餐已下架'
      })
    },
    handleImageSuccess(res, file) {
      // 图片上传成功回调
      this.formData.imageUrl = URL.createObjectURL(file.raw)
    },
    submitForm() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (this.formData.id !== undefined) {
            // 编辑套餐
            const index = this.tableData.findIndex(item => item.id === this.formData.id)
            this.tableData.splice(index, 1, Object.assign({}, this.formData))
            this.$message({
              type: 'success',
              message: '套餐信息已更新!'
            })
          } else {
            // 新增套餐
            this.formData.id = this.tableData.length + 1
            this.tableData.push(Object.assign({}, this.formData))
            this.total += 1
            this.$message({
              type: 'success',
              message: '新增套餐成功!'
            })
          }
          this.dialogFormVisible = false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.business-package {
  padding: 20px;
  
  .filter-container {
    margin-bottom: 20px;
    
    .filter-item {
      margin-right: 10px;
    }
  }
  
  .package-info {
    display: flex;
    align-items: center;
    
    .ml-10 {
      margin-left: 10px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .avatar-uploader {
    display: block;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;
    
    &:hover {
      border-color: #409EFF;
    }
    
    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
    
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }
  }
}
</style>
