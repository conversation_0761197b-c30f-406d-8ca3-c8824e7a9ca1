<template>
  <div class="business-setting">
    <div class="setting-section">
      <div class="section-header">
        <i class="el-icon-date"></i> 开餐日期
      </div>
      
      <div class="open-date-config">
        <div class="config-item">
          <span class="item-label">开餐模式：</span>
          <el-radio-group v-model="openMode">
            <el-radio :label="1">自选开餐</el-radio>
            <el-radio :label="2">每日开餐</el-radio>
            <el-radio :label="3">周一到周五开餐</el-radio>
            <el-radio :label="4">使用国家法定节假日规则</el-radio>
          </el-radio-group>
        </div>
        
        <div class="config-item">
          <span class="item-label">选择开餐日期：</span>
          <el-checkbox-group v-model="openDays">
            <el-checkbox label="1">周一</el-checkbox>
            <el-checkbox label="2">周二</el-checkbox>
            <el-checkbox label="3">周三</el-checkbox>
            <el-checkbox label="4">周四</el-checkbox>
            <el-checkbox label="5">周五</el-checkbox>
            <el-checkbox label="6">周六</el-checkbox>
            <el-checkbox label="0">周日</el-checkbox>
          </el-checkbox-group>
        </div>
        
        <div class="config-item">
          <span class="item-label">停餐日期：</span>
          <el-switch v-model="closeEnable" active-color="#13ce66"></el-switch>
          <el-button size="mini" type="text" v-if="closeEnable">查看列表</el-button>
        </div>
        
        <div class="config-item">
          <span class="item-label">报餐周期：</span>
          <span>可提前</span>
          <el-input-number v-model="orderDays" :min="1" :max="30" size="small" controls-position="right"></el-input-number>
          <span class="unit">天报餐</span>
        </div>
        
        <div class="config-item text-center">
          <el-button type="primary">保存更新</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BusinessSetting',
  data() {
    return {
      title: '商务餐设置',
      openMode: 4,
      openDays: [],
      closeEnable: false,
      orderDays: 7
    }
  }
}
</script>

<style lang="less" scoped>
.business-setting {
  background-color: #fff;
  padding: 20px;
  
  .setting-section {
    margin-bottom: 30px;
    
    .section-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      
      i {
        margin-right: 6px;
        color: #409EFF;
      }
    }
  }
  
  .open-date-config {
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 20px;
    
    .config-item {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      .item-label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
      }
      
      .unit {
        margin-left: 10px;
      }
      
      &.text-center {
        justify-content: center;
        margin-top: 20px;
      }
    }
  }
}
</style>