<template>
  <div class="on-site-order">
    <!-- 订单明细 -->
    <div class="order-section">
      <div class="section-title">
        <el-tabs v-model="activeOrderTab">
          现场点餐
        </el-tabs>
      </div>

      <div class="order-table">
        <Table
          :columns="orderColumns"
          :data="orderData"
          :pagination="pagination"
          :total="total"
          :loading="loading"
          @pagination="handlePagination"
        >
          <el-table-column slot="actions" label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="blue-btn">查看</el-button>
              <el-button type="text" size="small" class="green-btn">下单</el-button>
              <el-button type="text" size="small" class="red-btn">删除</el-button>
            </template>
          </el-table-column>
        </Table>
      </div>
    </div>

    <!-- 创建工单 -->
    <div class="create-order-section">
      <div class="section-title">
        <h3>创建工单</h3>
      </div>

      <div class="create-form">
        <div class="form-row">
          <div class="form-item">
            <span>工单号：</span>
            <el-input v-model="createForm.orderNo" placeholder="自动生成"></el-input>
          </div>
          <div class="form-item">
            <span>客户公司：</span>
            <el-select v-model="createForm.company" placeholder="请选择">
              <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
          <div class="form-item">
            <span>工单日期：</span>
            <el-date-picker v-model="createForm.date" type="date" placeholder="选择日期"></el-date-picker>
          </div>
        </div>
      </div>

      <div class="food-table">
        <div class="table-header">
          <div class="header-item">序号</div>
          <div class="header-item">类别</div>
          <div class="header-item">菜品名称</div>
          <div class="header-item">单价</div>
          <div class="header-item">数量</div>
          <div class="header-item">金额</div>
          <div class="header-item">备注</div>
          <div class="header-item">操作</div>
        </div>

        <div v-for="(item, index) in foodList" :key="index" class="table-row">
          <div class="row-item">{{ index + 1 }}</div>
          <div class="row-item">
            <span class="food-type">{{ item.category }}</span>
          </div>
          <div class="row-item">{{ item.name }}</div>
          <div class="row-item">{{ item.price }}</div>
          <div class="row-item">
            <el-input-number v-model="item.quantity" :min="1" :max="100" size="mini"></el-input-number>
          </div>
          <div class="row-item">{{ (item.price * item.quantity).toFixed(2) }}</div>
          <div class="row-item">{{ item.remark }}</div>
          <div class="row-item">
            <el-button type="text" size="small" class="red-btn" @click="removeFood(index)">删除</el-button>
          </div>
        </div>

        <div class="total-row">
          <div class="total-label">订单总计金额：</div>
          <div class="total-amount">¥ {{ getTotalAmount() }}</div>
        </div>
      </div>

      <div class="operation-buttons">
        <el-button>取消</el-button>
        <el-button type="primary" @click="saveOrder">保存</el-button>
      </div>
    </div>

    <!-- 菜品库 -->
    <div class="food-library-section">
      <div class="section-title">
        <h3>菜品库</h3>
      </div>

      <div class="category-filter">
        <div v-for="(category, index) in foodCategories" :key="index"
             :class="['category-item', selectedCategory === category.value ? 'active' : '']"
             @click="selectCategory(category.value)">
          <div class="category-icon">
            <i class="el-icon-folder"></i>
          </div>
          <div class="category-name">{{ category.label }}</div>
        </div>
      </div>

      <div class="food-search">
        <div class="search-item">
          <span>菜品名称：</span>
          <el-input v-model="foodSearch.name" placeholder="输入菜品名称"></el-input>
        </div>
        <div class="search-item">
          <span>菜品编码：</span>
          <el-input v-model="foodSearch.code" placeholder="输入菜品编码"></el-input>
        </div>
        <el-button type="primary" @click="searchFood">搜索</el-button>
      </div>

      <div class="food-list-table">
        <Table
          :columns="foodColumns"
          :data="filteredFoodData"
          :pagination="foodPagination"
          :total="foodTotal"
          :loading="foodLoading"
          @pagination="handleFoodPagination"
        >
          <el-table-column slot="foodImage" label="菜品图片" align="center" width="80">
            <template slot-scope="scope">
              <div class="food-image">
                <i class="el-icon-folder"></i>
              </div>
            </template>
          </el-table-column>

          <el-table-column slot="foodActions" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="blue-btn" @click="addToOrder(scope.row)">添加</el-button>
            </template>
          </el-table-column>
        </Table>
      </div>
    </div>

    <!-- 支付确认 -->
    <div class="payment-section" v-if="showPayment">
      <div class="payment-content">
        <div class="payment-title">支付订单</div>
        <div class="payment-info">
          <div class="payment-amount">¥ {{ getTotalAmount() }}</div>
          <div class="qrcode">
            <div class="qrcode-placeholder">
              <i class="el-icon-picture-outline" style="font-size: 50px;"></i>
              <p>支付二维码</p>
            </div>
          </div>
          <div class="payment-tips">请使用微信扫码支付</div>
        </div>
        <div class="payment-actions">
          <el-button @click="cancelPayment">取消</el-button>
          <el-button type="primary" @click="confirmPayment">确认支付</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table.vue'

export default {
  name: 'OnSiteOrder',
  components: {
    Table
  },
  data() {
    return {
      title: '现场点餐',
      activeOrderTab: 'orderDetail',
      loading: false,
      total: 0,
      pagination: {
        page: 1,
        page_size: 10
      },
      orderColumns: [
        { hasIndex: true },
        { prop: 'orderNo', label: '订单号', minWidth: 120 },
        { prop: 'customer', label: '客户公司', minWidth: 150 },
        { prop: 'totalAmount', label: '总金额', minWidth: 100 },
        { prop: 'status', label: '状态', minWidth: 80 },
        { prop: 'createDate', label: '创建日期', minWidth: 150 },
        { prop: 'creator', label: '创建人', minWidth: 100 },
        { slot: 'actions' }
      ],
      orderData: [
        { id: 1, orderNo: 'DD202307120001', customer: '上海优禾互联网科技有限公司', totalAmount: '128.00', status: '待支付', createDate: '2023-07-12 12:30:45', creator: '张三' },
        { id: 2, orderNo: 'DD202307120002', customer: '优禾科技公司', totalAmount: '98.50', status: '已完成', createDate: '2023-07-12 14:20:15', creator: '李四' }
      ],

      createForm: {
        orderNo: 'DD' + new Date().getTime(),
        company: '',
        date: new Date()
      },
      companyOptions: [
        { value: '1', label: '上海优禾互联网科技有限公司' },
        { value: '2', label: '优禾科技公司' }
      ],

      foodList: [
        { id: 1, category: '主食', name: '米饭', price: 3.00, quantity: 1, remark: '' },
        { id: 2, category: '炒菜', name: '西红柿炒鸡蛋', price: 18.00, quantity: 1, remark: '' }
      ],

      selectedCategory: 'all',
      foodCategories: [
        { value: 'all', label: '全部' },
        { value: 'main', label: '主食' },
        { value: 'dish', label: '炒菜' },
        { value: 'soup', label: '汤类' },
        { value: 'fruit', label: '水果' }
      ],

      foodSearch: {
        name: '',
        code: ''
      },

      foodLoading: false,
      foodTotal: 0,
      foodPagination: {
        page: 1,
        page_size: 10
      },
      foodColumns: [
        { hasSelection: true },
        { slot: 'foodImage' },
        { prop: 'name', label: '菜品名称', minWidth: 120 },
        { prop: 'code', label: '菜品编码', minWidth: 120 },
        { prop: 'category', label: '类别', minWidth: 80 },
        { prop: 'price', label: '价格', minWidth: 80 },
        { slot: 'foodActions' }
      ],
      foodData: [
        { id: 1, name: '米饭', code: 'MS001', category: '主食', price: 3.00 },
        { id: 2, name: '馒头', code: 'MS002', category: '主食', price: 2.00 },
        { id: 3, name: '西红柿炒鸡蛋', code: 'CC001', category: '炒菜', price: 18.00 },
        { id: 4, name: '青椒土豆丝', code: 'CC002', category: '炒菜', price: 16.00 },
        { id: 5, name: '紫菜蛋花汤', code: 'TC001', category: '汤类', price: 12.00 },
        { id: 6, name: '西瓜', code: 'SG001', category: '水果', price: 5.00 }
      ],

      showPayment: false
    }
  },
  computed: {
    filteredFoodData() {
      let result = this.foodData

      if (this.selectedCategory !== 'all') {
        const categoryMap = {
          'main': '主食',
          'dish': '炒菜',
          'soup': '汤类',
          'fruit': '水果'
        }
        result = result.filter(item => item.category === categoryMap[this.selectedCategory])
      }

      if (this.foodSearch.name) {
        result = result.filter(item => item.name.includes(this.foodSearch.name))
      }

      if (this.foodSearch.code) {
        result = result.filter(item => item.code.includes(this.foodSearch.code))
      }

      return result
    }
  },
  methods: {
    handlePagination(pagination) {
      this.pagination = pagination
      // 这里应该调用API获取订单数据
    },

    handleFoodPagination(pagination) {
      this.foodPagination = pagination
      // 这里应该调用API获取菜品数据
    },

    selectCategory(category) {
      this.selectedCategory = category
    },

    searchFood() {
      // 这里应该调用API获取搜索结果
    },

    addToOrder(food) {
      const existingFood = this.foodList.find(item => item.id === food.id)

      if (existingFood) {
        existingFood.quantity++
      } else {
        this.foodList.push({
          id: food.id,
          category: food.category,
          name: food.name,
          price: food.price,
          quantity: 1,
          remark: ''
        })
      }
    },

    removeFood(index) {
      this.foodList.splice(index, 1)
    },

    getTotalAmount() {
      return this.foodList.reduce((sum, item) => {
        return sum + (item.price * item.quantity)
      }, 0).toFixed(2)
    },

    saveOrder() {
      // 这里应该调用API保存订单
      this.showPayment = true
    },

    cancelPayment() {
      this.showPayment = false
    },

    confirmPayment() {
      // 这里应该调用API确认支付
      this.showPayment = false
      this.$message.success('支付成功')

      // 清空当前订单
      this.foodList = []
      this.createForm = {
        orderNo: 'DD' + new Date().getTime(),
        company: '',
        date: new Date()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.on-site-order {
  background-color: #fff;
  padding: 20px;

  .order-section, .create-order-section, .food-library-section {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
  }

  .section-title {
    padding: 10px 15px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .create-form {
    padding: 15px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15px;
    }

    .form-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-bottom: 10px;

      span {
        width: 80px;
        text-align: right;
        margin-right: 10px;
      }

      .el-input, .el-select, .el-date-picker {
        width: 200px;
      }
    }
  }

  .food-table {
    padding: 0 15px 15px;

    .table-header {
      display: flex;
      background-color: #f5f7fa;
      padding: 10px 0;
      font-weight: bold;
    }

    .header-item, .row-item {
      flex: 1;
      text-align: center;
      padding: 0 5px;

      &:first-child {
        flex: 0 0 60px;
      }

      &:nth-child(2) {
        flex: 0 0 120px;
      }

      &:nth-child(3) {
        flex: 0 0 150px;
      }

      &:last-child {
        flex: 0 0 100px;
      }
    }

    .table-row {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #ebeef5;

      &:hover {
        background-color: #f5f7fa;
      }

      .food-type {
        display: inline-block;
        background-color: #409EFF;
        color: #fff;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }

    .total-row {
      display: flex;
      justify-content: flex-end;
      padding: 15px 0;
      font-size: 16px;

      .total-label {
        margin-right: 20px;
      }

      .total-amount {
        font-weight: bold;
        color: #f56c6c;
      }
    }
  }

  .operation-buttons {
    text-align: center;
    padding: 20px 0;
  }

  .category-filter {
    display: flex;
    padding: 15px;
    flex-wrap: wrap;

    .category-item {
      cursor: pointer;
      text-align: center;
      width: 80px;
      margin-right: 15px;
      margin-bottom: 15px;

      &.active {
        .category-icon {
          background-color: #409EFF;
          color: #fff;
        }

        .category-name {
          color: #409EFF;
        }
      }

      .category-icon {
        width: 60px;
        height: 60px;
        line-height: 60px;
        margin: 0 auto;
        border-radius: 4px;
        background-color: #f0f2f5;

        i {
          font-size: 24px;
        }
      }

      .category-name {
        margin-top: 8px;
        font-size: 14px;
      }
    }
  }

  .food-search {
    display: flex;
    padding: 0 15px 15px;
    align-items: center;

    .search-item {
      display: flex;
      align-items: center;
      margin-right: 20px;

      span {
        margin-right: 10px;
      }

      .el-input {
        width: 180px;
      }
    }
  }

  .food-image {
    width: 40px;
    height: 40px;
    line-height: 40px;
    margin: 0 auto;
    border-radius: 4px;
    background-color: #f0f2f5;

    i {
      font-size: 20px;
    }
  }

  .blue-btn {
    color: #409EFF;
  }

  .green-btn {
    color: #67C23A;
  }

  .red-btn {
    color: #F56C6C;
  }

  .payment-section {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;

    .payment-content {
      width: 400px;
      padding: 20px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .payment-title {
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
      }

      .payment-info {
        text-align: center;

        .payment-amount {
          font-size: 24px;
          color: #f56c6c;
          font-weight: bold;
          margin-bottom: 20px;
        }

        .qrcode {
          width: 200px;
          height: 200px;
          margin: 0 auto 20px;
          border: 1px dashed #dcdfe6;
          border-radius: 4px;

          .qrcode-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #909399;

            p {
              margin-top: 10px;
              font-size: 14px;
            }
          }
        }

        .payment-tips {
          font-size: 14px;
          color: #606266;
          margin-bottom: 20px;
        }
      }

      .payment-actions {
        text-align: center;
      }
    }
  }
}
</style>
