<template>
  <div class="recharge-setting">
    <el-card>
      <div slot="header" class="card-header">
        <i class="el-icon-setting"></i> 账户设置
      </div>

      <div class="charge-config">
        <h3>充值套餐设置</h3>
        
        <div class="action-bar">
          <el-button type="success" size="small" @click="showAddDialog">新增套餐</el-button>
        </div>
        
        <el-table :data="chargePackages" border style="width: 100%">
          <el-table-column prop="id" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="chargeAmount" label="充值额度" width="120" align="center">
            <template slot-scope="scope">{{ scope.row.chargeAmount }}</template>
          </el-table-column>
          <el-table-column prop="giftAmount" label="赠送额度" width="120" align="center">
            <template slot-scope="scope">{{ scope.row.giftAmount }}</template>
          </el-table-column>
          <el-table-column prop="insideGift" label="赠送园内餐品" align="center">
            <template slot-scope="scope">{{ scope.row.insideGift }}</template>
          </el-table-column>
          <el-table-column prop="outsideGift" label="赠送外部礼品" align="center">
            <template slot-scope="scope">{{ scope.row.outsideGift }}</template>
          </el-table-column>
          <el-table-column prop="status" label="套餐状态" width="120" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '已下架' ? 'danger' : 'success'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" align="center">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 新增/编辑套餐弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="packageForm" label-width="100px">
        <el-form-item label="充值金额:" prop="chargeAmount">
          <el-input-number v-model="form.chargeAmount" :min="0" :step="100" controls-position="right"></el-input-number>
          <span class="unit">元</span>
        </el-form-item>
        <el-form-item label="赠送额度:" prop="giftAmount">
          <el-input-number v-model="form.giftAmount" :min="0" :step="10" controls-position="right"></el-input-number>
          <span class="unit">元</span>
        </el-form-item>
        <el-form-item label="自制礼品:" prop="insideGift">
          <el-input v-model="form.insideGift" placeholder="例如：4瓶鲜榨橙汁 + 2盒素饺子"></el-input>
        </el-form-item>
        <el-form-item label="外购礼品:" prop="outsideGift">
          <el-input v-model="form.outsideGift" placeholder="例如：1个保温杯"></el-input>
        </el-form-item>
        <el-form-item label="套餐状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="上架">上架</el-radio>
            <el-radio label="下架">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="gift-preview" v-if="form.chargeAmount && form.giftAmount">
        <h4>赠送套餐预览</h4>
        <div class="preview-content">
          <p>赠送额度：{{ form.giftAmount }}元</p>
          <p v-if="form.insideGift">赠送自制品套装：{{ form.insideGift }}</p>
          <p v-if="form.outsideGift">赠送外部礼品：{{ form.outsideGift }}</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RechargeSetting',
  data() {
    return {
      // 充值套餐列表
      chargePackages: [
        { id: 1, chargeAmount: 500, giftAmount: 25, insideGift: '1瓶鲜榨橙汁', outsideGift: '', status: '使用中' },
        { id: 2, chargeAmount: 1000, giftAmount: 50, insideGift: '2瓶鲜榨橙汁+1盒素饺子', outsideGift: '', status: '使用中' },
        { id: 3, chargeAmount: 2000, giftAmount: 100, insideGift: '4瓶鲜榨橙汁+2盒素饺子', outsideGift: '', status: '使用中' },
        { id: 4, chargeAmount: 10000, giftAmount: 500, insideGift: '4瓶鲜榨橙汁+2盒素饺子', outsideGift: '1个充电宝', status: '使用中' },
        { id: 5, chargeAmount: 20000, giftAmount: 1000, insideGift: '4瓶鲜榨橙汁+2盒素饺子', outsideGift: '1个吹风机', status: '已下架' }
      ],
      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '新增赠送套餐',
      isEdit: false,
      editIndex: -1,
      // 表单数据
      form: {
        chargeAmount: 5000,
        giftAmount: 250,
        insideGift: '4瓶鲜榨橙汁 + 2盒素饺子',
        outsideGift: '1个保温杯',
        status: '上架'
      },
      // 表单验证规则
      rules: {
        chargeAmount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' }
        ],
        giftAmount: [
          { required: true, message: '请输入赠送额度', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 显示新增套餐弹窗
    showAddDialog() {
      this.dialogTitle = '新增赠送套餐'
      this.isEdit = false
      this.form = {
        chargeAmount: 5000,
        giftAmount: 250,
        insideGift: '4瓶鲜榨橙汁 + 2盒素饺子',
        outsideGift: '1个保温杯',
        status: '上架'
      }
      this.dialogVisible = true
    },
    // 编辑套餐
    handleEdit(row) {
      this.dialogTitle = '编辑赠送套餐'
      this.isEdit = true
      this.editIndex = this.chargePackages.findIndex(item => item.id === row.id)
      this.form = JSON.parse(JSON.stringify(row))
      if (this.form.status === '使用中') {
        this.form.status = '上架'
      }
      this.dialogVisible = true
    },
    // 删除套餐
    handleDelete(row) {
      this.$confirm('确认删除该充值套餐吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.chargePackages.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.chargePackages.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    // 提交表单
    submitForm() {
      this.$refs.packageForm.validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form))
          if (formData.status === '上架') {
            formData.status = '使用中'
          }
          
          if (this.isEdit) {
            // 编辑模式
            this.chargePackages.splice(this.editIndex, 1, {
              ...formData,
              id: this.chargePackages[this.editIndex].id
            })
            this.$message.success('更新成功')
          } else {
            // 新增模式
            const maxId = Math.max(...this.chargePackages.map(item => item.id), 0)
            this.chargePackages.push({
              ...formData,
              id: maxId + 1
            })
            this.$message.success('添加成功')
          }
          
          this.dialogVisible = false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.recharge-setting {
  background-color: #fff;
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  
  i {
    margin-right: 6px;
  }
}

.charge-config {
  h3 {
    margin-top: 10px;
    margin-bottom: 20px;
    font-weight: normal;
  }
  
  .action-bar {
    margin-bottom: 15px;
  }
}

.unit {
  margin-left: 10px;
}

.gift-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f7f7f7;
  border-radius: 4px;
  
  h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
  }
  
  .preview-content {
    padding: 10px;
    border: 1px dashed #ddd;
    background-color: #fff;
    
    p {
      margin: 5px 0;
      color: #666;
    }
  }
}
</style>

