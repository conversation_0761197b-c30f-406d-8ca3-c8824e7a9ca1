<template>
  <div class="meal-record">
    <div class="page-header">
      <i class="el-icon-tickets"></i> 报餐就餐记录
    </div>
    
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <el-form-item label="报餐人员:">
          <el-input v-model="filterForm.person" placeholder="输入报餐人员" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机:">
          <el-input v-model="filterForm.phone" placeholder="输入手机号" clearable></el-input>
        </el-form-item>
        <el-form-item label="订单类型:">
          <el-select v-model="filterForm.orderType" placeholder="请选择" clearable>
            <el-option label="员工自助餐" value="worker"></el-option>
            <el-option label="企业商务餐" value="business"></el-option>
            <el-option label="企业重点餐" value="important"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="就餐状态:">
          <el-select v-model="filterForm.status" placeholder="请选择" clearable>
            <el-option label="未就餐" value="not_eaten"></el-option>
            <el-option label="已就餐" value="eaten"></el-option>
            <el-option label="部分就餐" value="partial"></el-option>
            <el-option label="取消报餐" value="canceled"></el-option>
            <el-option label="报餐不通过" value="rejected"></el-option>
            <el-option label="超时未用餐" value="timeout"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="就餐时间:">
          <el-date-picker
            v-model="filterForm.startDate"
            type="date"
            placeholder="开始日期"
            value-format="yyyy-MM-dd"
            style="width: 140px">
          </el-date-picker>
          <span style="margin: 0 5px;">-</span>
          <el-date-picker
            v-model="filterForm.endDate"
            type="date"
            placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 140px">
          </el-date-picker>
        </el-form-item>
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form>
    </div>
    
    <div class="action-container">
      <el-button type="success" icon="el-icon-download" @click="exportData">导出清单</el-button>
    </div>
    
    <el-table
      :data="mealRecords"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      max-height="600"
    >
      <el-table-column
        prop="index"
        label="序号"
        width="60"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="person"
        label="报餐人员"
        width="80"
        align="center"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="phone"
        label="手机"
        width="110"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="company"
        label="所属公司"
        width="130"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orderType"
        label="订单类型"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mealTime"
        label="就餐时间"
        width="140"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mealPeriod"
        label="就餐类别"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="people"
        label="就餐人数"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="amount"
        label="金额合计"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="payType"
        label="报餐状态"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="payTime"
        label="报餐时间"
        width="140"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mealStatus"
        label="就餐状态"
        width="90"
        align="center"
      >
        <template slot-scope="scope">
          <span :class="getMealStatusClass(scope.row.mealStatus)">{{ scope.row.mealStatus }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="bookingTime"
        label="就餐时间段前/起止时间"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mealActualTime"
        label="取消报餐/就餐时间"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="actualEndTime"
        label="预订报餐用餐时间"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reserveEndTime"
        label="预订报餐结束时间"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reviewPerson"
        label="报餐审核人员"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="reviewTime"
        label="报餐审核时间"
        width="140"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        width="180"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button 
            type="text" 
            class="primary-btn"
            @click="viewOrderDetail(scope.row)"
          >
            订单详情
          </el-button>
          <el-button 
            type="text" 
            class="warning-btn"
            @click="cancelMeal(scope.row)"
          >
            取消订单
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    
    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="orderDetailVisible" width="500px" class="order-detail-dialog">
      <div class="detail-content">
        <div class="order-items">
          <div class="order-item">
            <span class="item-label">麻婆豆腐</span>
            <span class="item-value">35 元</span>
            <span class="item-calc">(35×2=70)</span>
          </div>
          <div class="order-item">
            <span class="item-label">糯行炒芽菜</span>
            <span class="item-value">25 元</span>
            <span class="item-calc">(25×3=75)</span>
          </div>
          <div class="order-item">
            <span class="item-label">茄子炒豆角</span>
            <span class="item-value">25 元</span>
            <span class="item-calc">(25×3=84)</span>
          </div>
          <div class="order-item">
            <span class="item-label">房间</span>
            <span class="item-value">200 元</span>
            <span class="item-calc">(200×1=200)</span>
          </div>
        </div>
        
        <div class="order-total">
          <span class="total-label">订单原价:</span>
          <span class="total-value">429元</span>
        </div>
        
        <div class="order-discount">
          <div class="discount-header">
            <span>减免详情</span>
          </div>
          <div class="discount-item">
            <span class="item-label">商家抵付</span>
            <span class="item-value discount-value">-200 元</span>
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>
        
        <div class="order-total final-total">
          <span class="total-label">订单总价:</span>
          <span class="total-value">229元</span>
        </div>
        
        <div class="actual-payment">
          <span class="payment-title">实付金额</span>
          <span class="payment-amount">¥ 229.00</span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailVisible = false">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MealRecord',
  data() {
    return {
      title: '报餐就餐记录',
      
      // 筛选表单
      filterForm: {
        person: '',
        phone: '',
        orderType: '',
        status: '',
        startDate: '',
        endDate: ''
      },
      
      // 表格加载状态
      listLoading: false,
      
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 14,
      
      // 对话框
      orderDetailVisible: false,
      currentOrder: null,
      
      // 报餐记录数据
      mealRecords: [
        {
          index: 1,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-03 11:45-12:00',
          mealPeriod: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:10',
          mealStatus: '未就餐',
          bookingTime: '2025-03-03 18:00:00',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '',
          reviewTime: ''
        },
        {
          index: 2,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:10',
          mealStatus: '已就餐',
          bookingTime: '2025-03-02 18:00:00',
          mealActualTime: '2025-03-02 18:00:00',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '',
          reviewTime: ''
        },
        {
          index: 3,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '取消报餐',
          bookingTime: '2025-03-02 18:00:00',
          mealActualTime: '2025-03-02 18:00:00',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '',
          reviewTime: ''
        },
        {
          index: 4,
          person: '王五',
          phone: '159xxxx9584',
          company: '广州市XXX有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '未就餐',
          bookingTime: '2025-03-02 18:00:00',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '',
          reviewTime: ''
        },
        {
          index: 5,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预购',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '2025-02-28 11:45:25',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '公司报销签'
        },
        {
          index: 6,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '未就餐',
          bookingTime: '2025-03-02 18:00:00',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '',
          reviewTime: ''
        },
        {
          index: 7,
          person: '李四',
          phone: '159xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '企业商务餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 5,
          amount: 390,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '未就餐',
          bookingTime: '2025-03-02 18:00:00',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '',
          reviewTime: ''
        },
        {
          index: 8,
          person: '王五',
          phone: '159xxxx9584',
          company: '广州市XXX有限公司',
          orderType: '企业商务餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 2,
          amount: 50,
          payType: '待审核',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '2025-02-28 11:45:25',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '公司报销费'
        },
        {
          index: 9,
          person: '王五',
          phone: '159xxxx9584',
          company: '广州市XXX有限公司',
          orderType: '企业商务餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 2,
          amount: 50,
          payType: '报餐失败',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '报餐不通过'
        },
        {
          index: 10,
          person: '王五',
          phone: '159xxxx9584',
          company: '广州市XXX有限公司',
          orderType: '企业商务餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 2,
          amount: 50,
          payType: '报餐失败',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '超时未用餐'
        },
        {
          index: 11,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '企业重点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 3,
          amount: 229,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '未就餐',
          bookingTime: '2025-03-02 18:00:00',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '公司报销费'
        },
        {
          index: 12,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '企业重点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 2,
          amount: 200,
          payType: '待审核',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '公司报销费'
        },
        {
          index: 13,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '企业重点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 2,
          amount: 200,
          payType: '报餐失败',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '报餐不通过'
        },
        {
          index: 14,
          person: '李四',
          phone: '158xxxx1234',
          company: '广州市XXX有限公司',
          orderType: '企业重点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          people: 2,
          amount: 200,
          payType: '报餐失败',
          payTime: '2025-02-28 11:45:25',
          mealStatus: '',
          bookingTime: '',
          mealActualTime: '',
          actualEndTime: '',
          reserveEndTime: '',
          reviewPerson: '张三',
          reviewTime: '超时未用餐'
        }
      ]
    }
  },
  
  methods: {
    // 获取就餐状态样式类
    getMealStatusClass(status) {
      if (status === '已就餐') {
        return 'status-eaten';
      } else if (status === '未就餐') {
        return 'status-not-eaten';
      } else if (status === '取消报餐') {
        return 'status-canceled';
      } else if (status === '报餐不通过') {
        return 'status-rejected';
      } else if (status === '超时未用餐') {
        return 'status-timeout';
      }
      return '';
    },
    
    // 处理筛选
    handleFilter() {
      this.currentPage = 1;
      this.getMealRecords();
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        person: '',
        phone: '',
        orderType: '',
        status: '',
        startDate: '',
        endDate: ''
      };
      this.handleFilter();
    },
    
    // 获取报餐记录
    getMealRecords() {
      this.listLoading = true;
      
      // 模拟API请求
      setTimeout(() => {
        // 实际应用中应该调用接口
        const { person, phone, orderType, status, startDate, endDate } = this.filterForm;
        
        const filteredRecords = this.mealRecords.filter(item => {
          const personMatch = !person || item.person.includes(person);
          const phoneMatch = !phone || item.phone.includes(phone);
          const typeMatch = !orderType || 
            (orderType === 'worker' && item.orderType === '员工自助餐') ||
            (orderType === 'business' && item.orderType === '企业商务餐') ||
            (orderType === 'important' && item.orderType === '企业重点餐');
          
          let statusMatch = true;
          if (status) {
            if (status === 'not_eaten' && item.mealStatus === '未就餐') statusMatch = true;
            else if (status === 'eaten' && item.mealStatus === '已就餐') statusMatch = true;
            else if (status === 'canceled' && item.mealStatus === '取消报餐') statusMatch = true;
            else if (status === 'rejected' && item.reviewTime === '报餐不通过') statusMatch = true;
            else if (status === 'timeout' && item.reviewTime === '超时未用餐') statusMatch = true;
            else statusMatch = false;
          }
          
          // 日期过滤简化处理
          let dateMatch = true;
          if (startDate && endDate) {
            const itemDate = item.mealTime.split(' ')[0];
            dateMatch = itemDate >= startDate && itemDate <= endDate;
          }
          
          return personMatch && phoneMatch && typeMatch && statusMatch && dateMatch;
        });
        
        this.total = filteredRecords.length;
        this.listLoading = false;
      }, 500);
    },
    
    // 页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMealRecords();
    },
    
    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMealRecords();
    },
    
    // 导出数据
    exportData() {
      this.$message({
        message: '报餐就餐记录导出成功',
        type: 'success'
      });
    },
    
    // 查看订单详情
    viewOrderDetail(row) {
      this.currentOrder = row;
      this.orderDetailVisible = true;
    },
    
    // 取消订单
    cancelMeal(row) {
      if (row.mealStatus === '已就餐' || row.mealStatus === '取消报餐') {
        this.$message({
          message: `该订单已${row.mealStatus}，不能取消`,
          type: 'warning'
        });
        return;
      }
      
      this.$confirm('确定要取消该订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应用中应该调用接口
        row.mealStatus = '取消报餐';
        row.mealActualTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
        
        this.$message({
          type: 'success',
          message: '订单已取消'
        });
      }).catch(() => {});
    }
  },
  
  created() {
    this.getMealRecords();
  }
}
</script>

<style lang="less" scoped>
.meal-record {
  background-color: #fff;
  padding: 20px;
  
  .page-header {
    font-size: 18px;
    color: #303133;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
  
  .filter-container {
    margin-bottom: 15px;
  }
  
  .action-container {
    margin-bottom: 15px;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .status-eaten {
    color: #67C23A;
  }
  
  .status-not-eaten {
    color: #F56C6C;
  }
  
  .status-canceled {
    color: #E6A23C;
  }
  
  .status-rejected {
    color: #F56C6C;
  }
  
  .status-timeout {
    color: #909399;
  }
  
  .primary-btn {
    color: #409EFF;
  }
  
  .warning-btn {
    color: #E6A23C;
    margin-left: 5px;
  }
  
  /deep/ .el-select {
    width: 150px;
  }
  
  .order-detail-dialog {
    .detail-content {
      padding: 10px;
      
      .order-items {
        .order-item {
          display: flex;
          margin-bottom: 10px;
          
          .item-label {
            flex: 1;
            text-align: left;
          }
          
          .item-value {
            width: 100px;
            text-align: right;
          }
          
          .item-calc {
            color: #909399;
            font-size: 12px;
            margin-left: 10px;
            width: 100px;
          }
        }
      }
      
      .order-total {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-top: 1px solid #EBEEF5;
        
        .total-value {
          font-weight: bold;
        }
      }
      
      .order-discount {
        margin: 20px 0;
        border-radius: 4px;
        border: 1px solid #EBEEF5;
        
        .discount-header {
          padding: 10px;
          background-color: #F5F7FA;
          border-bottom: 1px solid #EBEEF5;
          font-weight: bold;
        }
        
        .discount-item {
          padding: 10px;
          display: flex;
          align-items: center;
          
          .item-label {
            flex: 1;
            text-align: left;
          }
          
          .discount-value {
            color: #F56C6C;
            margin-right: 10px;
          }
        }
      }
      
      .order-total final-total {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-top: 1px solid #EBEEF5;
        
        .total-value {
          font-weight: bold;
        }
      }
      
      .actual-payment {
        margin-top: 20px;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #EBEEF5;
        
        .payment-title {
          font-weight: bold;
        }
        
        .payment-amount {
          float: right;
        }
      }
    }
  }
}
</style>