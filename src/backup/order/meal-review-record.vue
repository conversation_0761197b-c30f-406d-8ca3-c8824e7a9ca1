<template>
  <div class="meal-review-record">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="待审核" name="pending"></el-tab-pane>
      <el-tab-pane label="已完结" name="completed"></el-tab-pane>
    </el-tabs>

    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="person" label="报餐人员" width="80" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机" width="110" align="center"></el-table-column>
      <el-table-column prop="orderType" label="订单类型" width="100" align="center"></el-table-column>
      <el-table-column prop="mealTime" label="就餐时间" width="140" align="center"></el-table-column>
      <el-table-column prop="mealPeriod" label="就餐类别" width="80" align="center"></el-table-column>
      <el-table-column prop="people" label="就餐人数" width="60" align="center"></el-table-column>
      <el-table-column prop="amount" label="金额合计" width="80" align="center"></el-table-column>
      <el-table-column prop="status" label="报餐状态" width="80" align="center">
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.status)">{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="bookTime" label="报餐时间" width="140" align="center"></el-table-column>
      <el-table-column prop="companyInfo" label="企业餐申请说明" width="140" align="center"></el-table-column>
      <el-table-column prop="company" label="企业餐申请公司" width="140" align="center"></el-table-column>
      <el-table-column prop="reviewStatus" label="审核状态" width="80" align="center">
        <template slot-scope="scope">
          <span :class="getReviewStatusClass(scope.row.reviewStatus)">{{ scope.row.reviewStatus }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="审核提出时间" width="140" align="center"></el-table-column>
      <el-table-column prop="endTime" label="审核截止时间" width="140" align="center"></el-table-column>
      <el-table-column prop="reviewer" label="审核人" width="80" align="center"></el-table-column>
      <el-table-column label="操作" width="80" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" class="primary-btn" @click="handleReview(scope.row)">
            {{ activeTab === 'pending' ? '审核' : '详情' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MealReviewRecord',
  data() {
    return {
      activeTab: 'pending',
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      listLoading: false
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true
      // 模拟数据
      setTimeout(() => {
        if (this.activeTab === 'pending') {
          this.tableData = [
            {
              index: 1,
              person: '李西',
              phone: '15800001234',
              orderType: '企业商务餐',
              mealTime: '2025-03-03\n11:45-12:00',
              mealPeriod: '午餐',
              people: 4,
              amount: 229,
              status: '待审核',
              bookTime: '2025-02-28\n11:45:00',
              companyInfo: '李西朋友还李李公司业务',
              company: '',
              reviewStatus: '未审核',
              startTime: '2025-03-03\n09:45:00',
              endTime: '2025-03-03\n09:45:00'
            },
            {
              index: 2,
              person: '李西',
              phone: '15800001234',
              orderType: '企业商务餐',
              mealTime: '2025-03-02\n11:45-12:00',
              mealPeriod: '午餐',
              people: 3,
              amount: 200,
              status: '待审核',
              bookTime: '2025-02-28\n11:45:10',
              companyInfo: '',
              company: '王经环球公司',
              reviewStatus: '未审核',
              startTime: '2025-02-28\n11:45:10',
              endTime: '2025-02-28\n11:45:10'
            },
            {
              index: 3,
              person: '王五',
              phone: '15900009584',
              orderType: '企业重点餐',
              mealTime: '2025-03-01\n11:45-12:00',
              mealPeriod: '午餐',
              people: 5,
              amount: 350,
              status: '待审核',
              bookTime: '2025-02-28\n11:45:10',
              companyInfo: '',
              company: '王经环球公司',
              reviewStatus: '未审核',
              startTime: '2025-02-28\n11:45:10',
              endTime: '2025-02-28\n11:45:10'
            }
          ]
        } else {
          this.tableData = [
            {
              index: 1,
              person: '李西',
              phone: '15800001234',
              orderType: '企业商务餐',
              mealTime: '2025-03-03\n11:45-12:00',
              mealPeriod: '午餐',
              people: 4,
              amount: 229,
              status: '报餐成功',
              bookTime: '2025-02-28\n11:45:00',
              companyInfo: '李西朋友还李李公司业务',
              company: '',
              reviewStatus: '审核通过',
              startTime: '2025-03-03\n09:45:00',
              endTime: '2025-03-03\n09:45:00',
              reviewer: '企业审核员'
            },
            {
              index: 2,
              person: '李西',
              phone: '15800001234',
              orderType: '企业商务餐',
              mealTime: '2025-03-02\n11:45-12:00',
              mealPeriod: '午餐',
              people: 3,
              amount: 200,
              status: '报餐失败',
              bookTime: '2025-02-28\n11:45:10',
              companyInfo: '',
              company: '王经环球公司',
              reviewStatus: '审核不通过',
              startTime: '2025-02-28\n11:45:10',
              endTime: '2025-02-28\n11:45:10',
              reviewer: '企业审核员'
            },
            {
              index: 3,
              person: '王五',
              phone: '15900009584',
              orderType: '企业重点餐',
              mealTime: '2025-03-01\n11:45-12:00',
              mealPeriod: '午餐',
              people: 5,
              amount: 350,
              status: '报餐失败',
              bookTime: '2025-02-28\n11:45:10',
              companyInfo: '',
              company: '王经环球公司',
              reviewStatus: '超时未审核',
              startTime: '2025-02-28\n11:45:10',
              endTime: '2025-02-28\n11:45:10',
              reviewer: '企业审核员'
            }
          ]
        }
        this.total = this.tableData.length
        this.listLoading = false
      }, 500)
    },
    getStatusClass(status) {
      if (status === '待审核') return 'status-pending'
      if (status === '报餐成功') return 'status-success'
      if (status === '报餐失败') return 'status-fail'
      return ''
    },
    getReviewStatusClass(status) {
      if (status === '未审核') return 'review-pending'
      if (status === '审核通过') return 'review-success'
      if (status === '审核不通过') return 'review-rejected'
      if (status === '超时未审核') return 'review-timeout'
      return ''
    },
    handleReview(row) {
      this.$message.info(`查看/审核订单: ${row.index}`)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    }
  },
  watch: {
    activeTab() {
      this.fetchData()
    }
  }
}
</script>

<style lang="less" scoped>
.meal-review-record {
  background-color: #fff;
  padding: 20px;

  .el-tabs {
    margin-bottom: 20px;
  }

  .status-pending {
    color: #e6a23c;
  }

  .status-success {
    color: #67c23a;
  }

  .status-fail {
    color: #f56c6c;
  }

  .review-pending {
    color: #409eff;
  }

  .review-success {
    color: #67c23a;
  }

  .review-rejected {
    color: #f56c6c;
  }

  .review-timeout {
    color: #909399;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>