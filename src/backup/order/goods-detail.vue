<template>
  <div class="goods-detail">
    <div class="page-header">
      <i class="el-icon-present"></i> 礼品明细
    </div>
    
    <div class="divider"></div>
    
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <el-form-item label="用户:">
          <el-input v-model="filterForm.user" placeholder="请输入用户名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="filterForm.status" placeholder="请选择" clearable>
            <el-option label="已领取" value="received"></el-option>
            <el-option label="未领取" value="pending"></el-option>
            <el-option label="已作废" value="canceled"></el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form>
    </div>
    
    <div class="action-container">
      <el-button type="success" icon="el-icon-download" @click="exportList">导出清单</el-button>
    </div>
    
    <el-table
      :data="goodsList"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
    >
      <el-table-column
        prop="index"
        label="序号"
        width="60"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="user"
        label="用户"
        min-width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="amount"
        label="充值金额"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="giftContent"
        label="礼品内容"
        min-width="250"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.status)">{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="cancelTime"
        label="作废时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="receiveTime"
        label="领取时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="receiver"
        label="领取人"
        min-width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        width="180"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button 
            type="text" 
            class="operate-btn"
            @click="inputReceipt(scope.row)"
          >
            确认领取
          </el-button>
          <el-button 
            type="text" 
            class="danger-btn"
            @click="cancelGift(scope.row)"
          >
            退款作废
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    
    <!-- 确认领取对话框 -->
    <el-dialog title="确认礼品领取" :visible.sync="receiveDialogVisible" width="500px">
      <el-form :model="receiveForm" :rules="receiveRules" ref="receiveForm" label-width="100px">
        <el-form-item label="领取人:" prop="receiver">
          <el-input v-model="receiveForm.receiver" placeholder="请输入领取人姓名"></el-input>
        </el-form-item>
        <el-form-item label="领取时间:" prop="receiveTime">
          <el-date-picker
            v-model="receiveForm.receiveTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            default-time="12:00:00">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input v-model="receiveForm.remark" type="textarea" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="receiveDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitReceive">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 退款作废对话框 -->
    <el-dialog title="礼品退款作废" :visible.sync="cancelDialogVisible" width="500px">
      <el-form :model="cancelForm" :rules="cancelRules" ref="cancelForm" label-width="100px">
        <el-form-item label="作废原因:" prop="reason">
          <el-select v-model="cancelForm.reason" placeholder="请选择作废原因">
            <el-option label="客户取消订单" value="customer_cancel"></el-option>
            <el-option label="礼品缺货" value="out_of_stock"></el-option>
            <el-option label="礼品损坏" value="damaged"></el-option>
            <el-option label="其他原因" value="other"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="作废时间:" prop="cancelTime">
          <el-date-picker
            v-model="cancelForm.cancelTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            default-time="12:00:00">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input v-model="cancelForm.remark" type="textarea" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCancel">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'GoodsDetail',
  data() {
    return {
      title: '礼品明细记录',
      
      // 筛选表单
      filterForm: {
        user: '',
        status: ''
      },
      
      // 表格数据
      goodsList: [
        {
          index: 4,
          user: '广州市XXX有限公司',
          amount: '2000元',
          giftContent: '2瓶辣椒酱 + 1盒套袋子',
          status: '已领取',
          createTime: '2025-02-19 12:15:10',
          cancelTime: '',
          receiveTime: '2025-02-19 12:15:10',
          receiver: '广州市XXX有限公司'
        },
        {
          index: 5,
          user: '广州市XXX有限公司',
          amount: '10000元',
          giftContent: '4瓶辣椒酱 + 2盒套袋子 + 1个充电宝',
          status: '未领取',
          createTime: '2025-02-19 12:15:10',
          cancelTime: '',
          receiveTime: '',
          receiver: ''
        },
        {
          index: 6,
          user: '广州市XXX有限公司',
          amount: '5000元',
          giftContent: '4瓶辣椒酱 + 2盒套袋子 + 1个保温杯',
          status: '已作废',
          createTime: '2025-02-19 12:15:10',
          cancelTime: '2025-02-19 12:15:10',
          receiveTime: '',
          receiver: ''
        }
      ],
      
      // 表格加载状态
      listLoading: false,
      
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 3,
      
      // 对话框相关
      receiveDialogVisible: false,
      cancelDialogVisible: false,
      
      // 当前操作的行
      currentRow: null,
      
      // 领取表单
      receiveForm: {
        receiver: '',
        receiveTime: '',
        remark: ''
      },
      
      // 领取表单规则
      receiveRules: {
        receiver: [
          { required: true, message: '请输入领取人姓名', trigger: 'blur' }
        ],
        receiveTime: [
          { required: true, message: '请选择领取时间', trigger: 'change' }
        ]
      },
      
      // 作废表单
      cancelForm: {
        reason: '',
        cancelTime: '',
        remark: ''
      },
      
      // 作废表单规则
      cancelRules: {
        reason: [
          { required: true, message: '请选择作废原因', trigger: 'change' }
        ],
        cancelTime: [
          { required: true, message: '请选择作废时间', trigger: 'change' }
        ]
      }
    }
  },
  
  methods: {
    // 获取状态样式类
    getStatusClass(status) {
      if (status === '已领取') {
        return 'status-received';
      } else if (status === '未领取') {
        return 'status-pending';
      } else if (status === '已作废') {
        return 'status-canceled';
      }
      return '';
    },
    
    // 处理筛选
    handleFilter() {
      this.currentPage = 1;
      this.getGoodsList();
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        user: '',
        status: ''
      };
      this.handleFilter();
    },
    
    // 获取礼品列表
    getGoodsList() {
      this.listLoading = true;
      
      // 模拟API请求
      setTimeout(() => {
        // 实际应用中应该调用接口
        const filteredList = this.goodsList.filter(item => {
          const userMatch = !this.filterForm.user || item.user.includes(this.filterForm.user);
          let statusMatch = true;
          if (this.filterForm.status) {
            if (this.filterForm.status === 'received') {
              statusMatch = item.status === '已领取';
            } else if (this.filterForm.status === 'pending') {
              statusMatch = item.status === '未领取';
            } else if (this.filterForm.status === 'canceled') {
              statusMatch = item.status === '已作废';
            }
          }
          return userMatch && statusMatch;
        });
        
        this.total = filteredList.length;
        this.listLoading = false;
      }, 500);
    },
    
    // 导出清单
    exportList() {
      this.$message({
        message: '礼品明细导出成功',
        type: 'success'
      });
    },
    
    // 页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getGoodsList();
    },
    
    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getGoodsList();
    },
    
    // 输入领取信息
    inputReceipt(row) {
      if (row.status === '已领取' || row.status === '已作废') {
        this.$message({
          message: `该礼品已${row.status}，不能再次确认领取`,
          type: 'warning'
        });
        return;
      }
      
      this.currentRow = row;
      this.receiveForm = {
        receiver: '',
        receiveTime: new Date().toISOString().split('T')[0] + ' 12:15:10',
        remark: ''
      };
      this.receiveDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.receiveForm && this.$refs.receiveForm.clearValidate();
      });
    },
    
    // 提交领取信息
    submitReceive() {
      this.$refs.receiveForm.validate(valid => {
        if (valid) {
          // 实际应用中应该调用接口
          if (this.currentRow) {
            this.currentRow.status = '已领取';
            this.currentRow.receiveTime = this.receiveForm.receiveTime;
            this.currentRow.receiver = this.receiveForm.receiver;
          }
          
          this.$message({
            message: '礼品领取确认成功',
            type: 'success'
          });
          this.receiveDialogVisible = false;
        }
      });
    },
    
    // 退款作废
    cancelGift(row) {
      if (row.status === '已作废') {
        this.$message({
          message: '该礼品已作废，不能再次作废',
          type: 'warning'
        });
        return;
      }
      
      this.currentRow = row;
      this.cancelForm = {
        reason: '',
        cancelTime: new Date().toISOString().split('T')[0] + ' 12:15:10',
        remark: ''
      };
      this.cancelDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.cancelForm && this.$refs.cancelForm.clearValidate();
      });
    },
    
    // 提交作废信息
    submitCancel() {
      this.$refs.cancelForm.validate(valid => {
        if (valid) {
          // 实际应用中应该调用接口
          if (this.currentRow) {
            this.currentRow.status = '已作废';
            this.currentRow.cancelTime = this.cancelForm.cancelTime;
          }
          
          this.$message({
            message: '礼品退款作废成功',
            type: 'success'
          });
          this.cancelDialogVisible = false;
        }
      });
    }
  },
  
  created() {
    this.getGoodsList();
  }
}
</script>

<style lang="less" scoped>
.goods-detail {
  background-color: #fff;
  padding: 20px;
  
  .page-header {
    font-size: 18px;
    color: #303133;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
  
  .divider {
    height: 1px;
    background-color: #EBEEF5;
    margin-bottom: 20px;
  }
  
  .filter-container {
    margin-bottom: 15px;
  }
  
  .action-container {
    margin-bottom: 15px;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .status-received {
    color: #67C23A;
  }
  
  .status-pending {
    color: #E6A23C;
  }
  
  .status-canceled {
    color: #F56C6C;
  }
  
  .operate-btn {
    color: #409EFF;
  }
  
  .danger-btn {
    color: #F56C6C;
    margin-left: 5px;
  }
  
  /deep/ .el-select {
    width: 180px;
  }
  
  /deep/ .el-date-editor.el-input {
    width: 220px;
  }
}
</style>