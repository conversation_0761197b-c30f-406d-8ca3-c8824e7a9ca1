<template>
  <div class="mall-order">
    <h1>商城订单记录</h1>
    
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <el-form-item label="操作人：">
          <el-input v-model="filterForm.operator" placeholder="输入操作人" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机：">
          <el-input v-model="filterForm.phone" placeholder="输入手机号" clearable></el-input>
        </el-form-item>
        <el-form-item label="订单状态：">
          <el-select v-model="filterForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="waiting"></el-option>
            <el-option label="已收款" value="paid"></el-option>
            <el-option label="已取消" value="canceled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间：">
          <el-date-picker
            v-model="filterForm.startDate"
            type="date"
            placeholder="开始日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
          <span style="margin: 0 5px;">-</span>
          <el-date-picker
            v-model="filterForm.endDate"
            type="date"
            placeholder="结束日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form>
    </div>
    
    <div class="action-container">
      <el-button type="success" icon="el-icon-plus" @click="addRecord">添加订单</el-button>
    </div>
    
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
    >
      <el-table-column prop="index" label="序号" width="50" align="center"></el-table-column>
      <el-table-column prop="orderNumber" label="订单号" width="140" align="center"></el-table-column>
      <el-table-column prop="operator" label="操作人" width="80" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机" width="100" align="center"></el-table-column>
      <el-table-column prop="amount" label="订单金额(元)" width="100" align="center"></el-table-column>
      <el-table-column prop="status" label="订单状态" width="80" align="center">
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.status)">{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderTime" label="下单时间" width="140" align="center"></el-table-column>
      <el-table-column prop="deliveryTime" label="领取时间" width="140" align="center"></el-table-column>
      <el-table-column prop="receivingTime" label="取货时间" width="140" align="center"></el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" class="primary-btn" @click="viewOrderDetail(scope.row)">订单详情</el-button>
          <el-button type="text" class="warning-btn" @click="exportOrder(scope.row)">确认收款</el-button>
          <el-button type="text" class="danger-btn" @click="cancelOrder(scope.row)">取消订单</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    
    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="orderDetailVisible" width="400px">
      <div class="order-detail">
        <div class="detail-content">
          <div class="order-items">
            <div class="order-item">
              <span class="item-label">麻婆豆腐</span>
              <span class="item-value">35 元</span>
              <span class="item-calc">(35×2=70)</span>
            </div>
            <div class="order-item">
              <span class="item-label">麻行炒芽菜</span>
              <span class="item-value">25 元</span>
              <span class="item-calc">(25×3=75)</span>
            </div>
            <div class="order-item">
              <span class="item-label">茄子炒豆角</span>
              <span class="item-value">28 元</span>
              <span class="item-calc">(28×3=84)</span>
            </div>
            <div class="order-item">
              <span class="item-label">房间</span>
              <span class="item-value">200 元</span>
              <span class="item-calc">(200×1=200)</span>
            </div>
          </div>
          
          <div class="order-total">
            <div class="total-line">
              <span class="total-label">订单原价:</span>
              <span class="total-value">429元</span>
            </div>
          </div>
          
          <div class="order-discount">
            <div class="discount-header">
              <span>减免详情</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <div class="discount-item">
              <span class="discount-label">商家补贴</span>
              <span class="discount-value">-200 元</span>
            </div>
          </div>
          
          <div class="order-actual">
            <div class="actual-line">
              <span class="actual-label">实付金额</span>
              <span class="actual-value">¥ 229.00</span>
            </div>
          </div>
        </div>
        
        <div class="action-line">
          <el-button @click="orderDetailVisible = false">返回</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MallOrder',
  data() {
    return {
      title: '商城订单记录',
      filterForm: {
        operator: '',
        phone: '',
        status: '',
        startDate: '',
        endDate: ''
      },
      tableData: [
        {
          index: 1,
          orderNumber: '202503040841654',
          operator: '张三',
          phone: '157xxxx4587',
          amount: '58',
          status: '待收款',
          orderTime: '2025-03-03 09:45:25'
        },
        {
          index: 2,
          orderNumber: '202503040841654',
          operator: '张三',
          phone: '157xxxx4587',
          amount: '265',
          status: '已收款',
          orderTime: '2025-03-03 09:45:25',
          deliveryTime: '2025-03-04 11:45:25'
        },
        {
          index: 3,
          orderNumber: '202503040841654',
          operator: '张三',
          phone: '157xxxx4587',
          amount: '265',
          status: '已取消',
          orderTime: '2025-03-03 09:45:25',
          receivingTime: '2025-03-04 11:45:25'
        }
      ],
      listLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 3,
      orderDetailVisible: false,
      currentOrder: null
    }
  },
  methods: {
    getStatusClass(status) {
      if (status === '待收款') return 'status-warning'
      if (status === '已收款') return 'status-success'
      if (status === '已取消') return 'status-danger'
      return ''
    },
    handleFilter() {
      // 处理筛选逻辑
      this.currentPage = 1
      this.fetchData()
    },
    resetFilter() {
      this.filterForm = {
        operator: '',
        phone: '',
        status: '',
        startDate: '',
        endDate: ''
      }
      this.handleFilter()
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.fetchData()
    },
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchData()
    },
    fetchData() {
      // 获取数据逻辑
      this.listLoading = true
      setTimeout(() => {
        this.listLoading = false
      }, 500)
    },
    viewOrderDetail(row) {
      this.currentOrder = row
      this.orderDetailVisible = true
    },
    addRecord() {
      // 添加订单逻辑
    },
    exportOrder(row) {
      // 确认收款逻辑
    },
    cancelOrder(row) {
      // 取消订单逻辑
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style lang="less" scoped>
.mall-order {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
  }
  
  .filter-container {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }
  
  .action-container {
    margin-bottom: 15px;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .status-warning {
    color: #e6a23c;
  }
  
  .status-success {
    color: #67c23a;
  }
  
  .status-danger {
    color: #f56c6c;
  }
  
  .primary-btn {
    color: #409eff;
  }
  
  .warning-btn {
    color: #e6a23c;
  }
  
  .danger-btn {
    color: #f56c6c;
  }
  
  .order-detail {
    .detail-content {
      margin-bottom: 20px;
    }
    
    .order-items {
      margin-bottom: 15px;
      
      .order-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .item-label {
          flex: 1;
          text-align: left;
        }
        
        .item-value {
          width: 80px;
          text-align: right;
        }
        
        .item-calc {
          width: 100px;
          text-align: right;
          color: #999;
        }
      }
    }
    
    .order-total {
      margin: 10px 0;
      text-align: right;
      
      .total-line {
        font-weight: bold;
      }
    }
    
    .order-discount {
      margin: 15px 0;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      
      .discount-header {
        display: flex;
        justify-content: space-between;
        padding: 8px 15px;
        background-color: #f5f7fa;
      }
      
      .discount-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 15px;
        
        .discount-value {
          color: #f56c6c;
        }
      }
    }
    
    .order-actual {
      margin-top: 20px;
      text-align: right;
      
      .actual-line {
        font-size: 16px;
        font-weight: bold;
        
        .actual-value {
          color: #f56c6c;
          font-size: 18px;
        }
      }
    }
    
    .action-line {
      text-align: center;
    }
  }
}
</style>