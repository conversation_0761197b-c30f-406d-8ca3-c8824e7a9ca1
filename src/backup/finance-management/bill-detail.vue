<template>
  <div class="bill-detail">
    <h1>开票记录明细</h1>
    
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <el-form-item label="购买人名：">
          <el-input v-model="filterForm.buyerName" placeholder="请输入购买人名" style="width: 150px;"></el-input>
        </el-form-item>
        <el-form-item label="手机：">
          <el-input v-model="filterForm.phone" placeholder="请输入手机号" style="width: 150px;"></el-input>
        </el-form-item>
        <el-form-item label="所属公司：">
          <el-input v-model="filterForm.company" placeholder="请输入公司名称" style="width: 150px;"></el-input>
        </el-form-item>
        <el-form-item label="发票税号：">
          <el-input v-model="filterForm.taxNumber" placeholder="请输入发票税号" style="width: 150px;"></el-input>
        </el-form-item>
        <el-form-item label="开票状态：">
          <el-select v-model="filterForm.billStatus" placeholder="请选择" style="width: 120px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="待开票" value="pending"></el-option>
            <el-option label="已开票" value="issued"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="就餐时间段：">
          <el-time-picker v-model="filterForm.startTime" placeholder="起始时间" style="width: 140px;" format="HH:mm:ss"></el-time-picker>
          <span style="margin: 0 5px;">-</span>
          <el-time-picker v-model="filterForm.endTime" placeholder="结束时间" style="width: 140px;" format="HH:mm:ss"></el-time-picker>
        </el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form>
    </div>
    
    <div class="action-container">
      <el-button type="primary" icon="el-icon-plus" @click="startBilling">批量开票</el-button>
      <el-button type="success" icon="el-icon-download" @click="exportBills">导出明细</el-button>
    </div>
    
    <el-table
      :data="billList"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="buyerName" label="购买人" width="80" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机" width="120" align="center"></el-table-column>
      <el-table-column prop="company" label="所属公司" min-width="180" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="orderType" label="订单类型" width="100" align="center"></el-table-column>
      <el-table-column prop="mealTime" label="就餐时间段" width="160" align="center"></el-table-column>
      <el-table-column prop="mealPeriod" label="就餐类别" width="80" align="center"></el-table-column>
      <el-table-column prop="peopleCount" label="就餐人数" width="80" align="center"></el-table-column>
      <el-table-column prop="amount" label="金额合计" width="80" align="center"></el-table-column>
      <el-table-column prop="billStatus" label="开票状态" width="80" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.billStatus === '已开票' ? 'success' : 'info'">
            {{ scope.row.billStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="billTime" label="开票时间" width="160" align="center"></el-table-column>
      <el-table-column prop="billNumber" label="发票号码与代码" min-width="180" align="center"></el-table-column>
      <el-table-column label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.billStatus === '已开票'" size="mini" type="primary" @click="handleBill(scope.row)">申请开票</el-button>
          <el-button v-else size="mini" type="success" @click="viewBill(scope.row)">查看发票</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    
    <!-- 开票信息弹窗 -->
    <el-dialog title="开票信息" :visible.sync="dialogVisible" width="500px">
      <div class="bill-form">
        <div class="form-item">
          <label>购买方名称：</label>
          <el-input v-model="billForm.buyerName" placeholder="请输入购买方名称"></el-input>
          <el-button type="primary" size="small" style="margin-left: 10px;">查</el-button>
        </div>
        <div class="form-item">
          <label>纳税人识别号：</label>
          <el-input v-model="billForm.taxNumber" placeholder="请输入纳税人识别号"></el-input>
        </div>
        <div class="form-item">
          <label>发票收件邮箱：</label>
          <el-input v-model="billForm.email" placeholder="请输入发票收件邮箱"></el-input>
        </div>
        <div class="form-item">
          <label>发票备注：</label>
          <el-input type="textarea" v-model="billForm.remark" :rows="3" placeholder="请输入发票备注"></el-input>
        </div>
        <div class="bill-total">
          <div class="bill-project">项目名称：*餐饮服务*餐费</div>
          <div class="bill-amount">含税总金额：25 元</div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBill">提交开票</el-button>
      </div>
    </el-dialog>
    
    <!-- 发票抬头弹窗 -->
    <el-dialog title="发票抬头" :visible.sync="headerDialogVisible" width="500px">
      <div class="header-tabs">
        <div class="tab-header">
          <div :class="['tab-item', activeHeaderTab === 1 ? 'active' : '']" @click="activeHeaderTab = 1">购买方1</div>
          <div :class="['tab-item', activeHeaderTab === 2 ? 'active' : '']" @click="activeHeaderTab = 2">购买方2</div>
          <div class="tab-add" @click="addNewHeader">
            <i class="el-icon-plus"></i>
          </div>
        </div>
        <div class="tab-content">
          <div class="form-item">
            <label>购买方名称：</label>
            <el-input v-model="headerForm.buyerName" placeholder="请输入购买方名称"></el-input>
          </div>
          <div class="form-item">
            <label>纳税人识别号：</label>
            <el-input v-model="headerForm.taxNumber" placeholder="请输入纳税人识别号"></el-input>
          </div>
          <div class="form-item">
            <label>发票收件邮箱：</label>
            <el-input v-model="headerForm.email" placeholder="请输入发票收件邮箱"></el-input>
          </div>
        </div>
        <div class="tab-actions">
          <el-button size="small" type="primary" @click="saveHeader">保存</el-button>
          <el-button size="small" type="danger" @click="deleteHeader">删除</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BillDetail',
  data() {
    return {
      title: '开票记录明细',
      
      // 筛选表单
      filterForm: {
        buyerName: '',
        phone: '',
        company: '',
        taxNumber: '',
        billStatus: '',
        startTime: '',
        endTime: ''
      },
      
      // 表格加载状态
      listLoading: false,
      
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 5,
      
      // 选中的行
      selectedRows: [],
      
      // 开票信息弹窗
      dialogVisible: false,
      billForm: {
        buyerName: '',
        taxNumber: '',
        email: '',
        remark: ''
      },
      
      // 发票抬头弹窗
      headerDialogVisible: false,
      activeHeaderTab: 1,
      headerForm: {
        buyerName: '',
        taxNumber: '',
        email: ''
      },
      
      // 表格数据
      billList: [
        {
          index: 1,
          buyerName: '李四',
          phone: '15000001234',
          company: '广州市某某有限公司',
          orderType: '员工自助餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          peopleCount: 1,
          amount: 25,
          billStatus: '已开票',
          billTime: '2025-03-02 11:45-12:00',
          billNumber: '发票号码'
        },
        {
          index: 2,
          buyerName: '李四',
          phone: '15000001234',
          company: '广州市XXXX有限公司',
          orderType: '企业商务餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          peopleCount: 5,
          amount: 360,
          billStatus: '申请中',
          billTime: '',
          billNumber: ''
        },
        {
          index: 3,
          buyerName: '王五',
          phone: '15000001234',
          company: '广州市XXXX有限公司',
          orderType: '企业宴品点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          peopleCount: 2,
          amount: 50,
          billStatus: '已申请待开票',
          billTime: '',
          billNumber: '202407043455092'
        },
        {
          index: 4,
          buyerName: '王五',
          phone: '15000001234',
          company: '广州市XXXX有限公司',
          orderType: '企业宴品点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          peopleCount: 3,
          amount: 150,
          billStatus: '已开票',
          billTime: '2025-03-02 11:45-12:00',
          billNumber: '202407043455092'
        },
        {
          index: 5,
          buyerName: '王五',
          phone: '15000001234',
          company: '广州市XXXX有限公司',
          orderType: '企业宴品点餐',
          mealTime: '2025-03-02 11:45-12:00',
          mealPeriod: '午餐',
          peopleCount: 4,
          amount: 220,
          billStatus: '已开票',
          billTime: '2025-03-02 11:45-12:00',
          billNumber: '202407043455092'
        }
      ]
    }
  },
  methods: {
    // 筛选
    handleFilter() {
      this.currentPage = 1
      // 实际项目中这里会调用接口获取数据
      console.log('筛选条件:', this.filterForm)
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        buyerName: '',
        phone: '',
        company: '',
        taxNumber: '',
        billStatus: '',
        startTime: '',
        endTime: ''
      }
      this.handleFilter()
    },
    
    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      // 重新获取数据
    },
    
    // 处理当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
      // 重新获取数据
    },
    
    // 处理表格选择变化
    handleSelectionChange(val) {
      this.selectedRows = val
    },
    
    // 开始批量开票
    startBilling() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录进行开票')
        return
      }
      this.dialogVisible = true
      // 默认使用第一条记录的信息
      const firstRow = this.selectedRows[0]
      this.billForm = {
        buyerName: firstRow.company || '',
        taxNumber: '',
        email: '',
        remark: ''
      }
    },
    
    // 导出明细
    exportBills() {
      this.$message.success('导出成功')
    },
    
    // 申请开票
    handleBill(row) {
      this.dialogVisible = true
      this.billForm = {
        buyerName: row.company || '',
        taxNumber: '',
        email: '',
        remark: ''
      }
    },
    
    // 查看发票
    viewBill(row) {
      this.$message.info('查看发票: ' + row.billNumber)
    },
    
    // 提交开票
    submitBill() {
      this.$message.success('开票申请提交成功')
      this.dialogVisible = false
    },
    
    // 添加新的发票抬头
    addNewHeader() {
      this.headerDialogVisible = true
      this.headerForm = {
        buyerName: '',
        taxNumber: '',
        email: ''
      }
    },
    
    // 保存发票抬头
    saveHeader() {
      this.$message.success('保存成功')
      this.headerDialogVisible = false
    },
    
    // 删除发票抬头
    deleteHeader() {
      this.$confirm('确定要删除这个发票抬头吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.headerDialogVisible = false
      }).catch(() => {})
    }
  }
}
</script>

<style lang="less" scoped>
.bill-detail {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 22px;
    font-weight: bold;
  }
  
  .filter-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .action-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .bill-form {
    .form-item {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      label {
        width: 110px;
        text-align: right;
        padding-right: 10px;
      }
      
      .el-input {
        flex: 1;
      }
    }
    
    .bill-total {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px dashed #ccc;
      
      .bill-project, .bill-amount {
        margin-bottom: 10px;
        text-align: center;
        font-weight: bold;
      }
    }
  }
  
  .header-tabs {
    .tab-header {
      display: flex;
      margin-bottom: 15px;
      border-bottom: 1px solid #dcdfe6;
      
      .tab-item {
        padding: 8px 15px;
        cursor: pointer;
        
        &.active {
          color: #409eff;
          border-bottom: 2px solid #409eff;
        }
      }
      
      .tab-add {
        margin-left: auto;
        padding: 8px 15px;
        cursor: pointer;
        color: #409eff;
      }
    }
    
    .tab-content {
      margin-bottom: 15px;
      
      .form-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        
        label {
          width: 110px;
          text-align: right;
          padding-right: 10px;
        }
        
        .el-input {
          flex: 1;
        }
      }
    }
    
    .tab-actions {
      text-align: center;
    }
  }
}

.negative-amount {
  color: #f56c6c;
}

.positive-amount {
  color: #67c23a;
}
</style>