<template>
  <div class="consumption-statistic">
    <h1>消费记录统计</h1>
    
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" @submit.native.prevent>
        <el-form-item label="名称:">
          <el-input v-model="filterForm.name" placeholder="请输入名称" style="width: 220px;"></el-input>
        </el-form-item>
        <el-form-item label="起止时间:">
          <el-date-picker
            v-model="filterForm.startDate"
            type="date"
            placeholder="开始日期"
            value-format="yyyy-MM-dd"
            style="width: 140px">
          </el-date-picker>
          <span style="margin: 0 5px;">-</span>
          <el-date-picker
            v-model="filterForm.endDate"
            type="date"
            placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 140px">
          </el-date-picker>
        </el-form-item>
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form>
    </div>
    
    <div class="summary-info">
      <div class="company-info">
        <span class="label">名称:</span>
        <span class="value">{{ companyInfo.name }}</span>
      </div>
      <div class="time-info">
        <span class="label">时间:</span>
        <span class="value">{{ timeRangeText }}</span>
      </div>
      <div class="amount-info">
        <span class="label">合计金额:</span>
        <span class="value">{{ totalAmount }}元</span>
      </div>
    </div>
    
    <div class="action-container">
      <el-button type="success" icon="el-icon-download" @click="exportData">导出清单</el-button>
    </div>
    
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
    >
      <el-table-column
        prop="index"
        label="序号"
        width="70"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="date"
        label="就餐日期"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orderType"
        label="订单类型"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mealType"
        label="就餐类别"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="peopleCount"
        label="就餐人数"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="amount"
        label="金额合计"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="status"
        label="就餐状态"
        width="120"
        align="center"
      ></el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConsumptionStatistic',
  data() {
    return {
      title: '消费记录统计',
      
      // 筛选表单
      filterForm: {
        name: '广州市某某某公司',
        startDate: '2025-03-01',
        endDate: '2025-03-31'
      },
      
      // 公司信息
      companyInfo: {
        name: '广州市某某某公司'
      },
      
      // 表格加载状态
      listLoading: false,
      
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 10,
      
      // 表格数据
      tableData: [
        {
          index: 1,
          date: '2025-03-01',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 5,
          amount: 125,
          status: '已就餐'
        },
        {
          index: 2,
          date: '2025-03-02',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 4,
          amount: 100,
          status: '已就餐'
        },
        {
          index: 3,
          date: '2025-03-03',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 4,
          amount: 100,
          status: '已就餐'
        },
        {
          index: 4,
          date: '2025-03-04',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 4,
          amount: 100,
          status: '已就餐'
        },
        {
          index: 5,
          date: '2025-03-05',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 5,
          amount: 125,
          status: '已就餐'
        },
        {
          index: 6,
          date: '2025-03-07',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 4,
          amount: 100,
          status: '已就餐'
        },
        {
          index: 7,
          date: '2025-03-07',
          orderType: '员工自助餐',
          mealType: '午餐',
          peopleCount: 4,
          amount: 100,
          status: '已就餐'
        },
        {
          index: 8,
          date: '2025-03-08',
          orderType: '企业商务餐',
          mealType: '午餐',
          peopleCount: 3,
          amount: 75,
          status: '已就餐'
        },
        {
          index: 9,
          date: '2025-03-08',
          orderType: '企业商务餐',
          mealType: '午餐',
          peopleCount: 5,
          amount: 390,
          status: '已就餐'
        },
        {
          index: 10,
          date: '2025-03-09',
          orderType: '企业宴品点餐',
          mealType: '午餐',
          peopleCount: 8,
          amount: 464,
          status: '已就餐'
        }
      ]
    }
  },
  computed: {
    timeRangeText() {
      return `${this.filterForm.startDate}至${this.filterForm.endDate}`
    },
    totalAmount() {
      return 1679
    }
  },
  methods: {
    // 筛选
    handleFilter() {
      this.currentPage = 1
      // 实际应用中这里需要调用API获取数据
      this.fetchData()
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        name: '',
        startDate: '',
        endDate: ''
      }
      this.handleFilter()
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    
    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    
    // 获取数据
    fetchData() {
      this.listLoading = true
      // 模拟API请求
      setTimeout(() => {
        this.listLoading = false
      }, 500)
    },
    
    // 导出数据
    exportData() {
      this.$message.success('导出清单成功')
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style lang="less" scoped>
.consumption-statistic {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    
    &:before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #409EFF;
      margin-right: 8px;
    }
  }
  
  .filter-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border-radius: 4px;
  }
  
  .summary-info {
    display: flex;
    margin-bottom: 20px;
    background-color: #f0f9eb;
    padding: 15px;
    border-radius: 4px;
    
    > div {
      margin-right: 30px;
      
      .label {
        font-weight: bold;
        margin-right: 5px;
      }
      
      .value {
        color: #333;
      }
    }
  }
  
  .action-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>