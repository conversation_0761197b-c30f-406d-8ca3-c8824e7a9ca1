<template>
  <div class="meal-statistic">
    <div class="header-info">
      <h1>报餐就餐统计</h1>
      <div class="refresh-time">数据更新时间：{{ refreshTime }}</div>
    </div>
    
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane label="审核通过" name="approved"></el-tab-pane>
      <el-tab-pane label="设置提醒" name="reminder"></el-tab-pane>
    </el-tabs>
    
    <div class="statistic-content">
      <div class="statistic-section">
        <h3><i class="el-icon-s-home"></i> 自助就餐区域</h3>
        <el-table :data="selfServiceData" border stripe>
          <el-table-column prop="date" label="就餐日期" width="180" align="center"></el-table-column>
          <el-table-column prop="orderType" label="订单类型" width="180" align="center"></el-table-column>
          <el-table-column prop="mealPeriod" label="就餐时段" align="center"></el-table-column>
          <el-table-column prop="reportedNum" label="已报餐-总人数" align="center"></el-table-column>
          <el-table-column prop="checkedInNum" label="已就餐-总人数" align="center"></el-table-column>
          <el-table-column prop="notCheckedInNum" label="未就餐-总人数" align="center">
            <template slot-scope="scope">
              <span style="color: #f56c6c;">{{ scope.row.notCheckedInNum }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="statistic-section">
        <h3><i class="el-icon-s-shop"></i> 商务餐厅区域</h3>
        <el-table :data="businessDiningData" border stripe>
          <el-table-column prop="date" label="就餐日期" width="180" align="center"></el-table-column>
          <el-table-column prop="orderType" label="订单类型" width="180" align="center"></el-table-column>
          <el-table-column prop="mealPeriod" label="就餐时段" align="center"></el-table-column>
          <el-table-column prop="reportedNum" label="已报餐-总人数" align="center"></el-table-column>
        </el-table>
      </div>
      
      <div class="statistic-section">
        <h3><i class="el-icon-s-goods"></i> 商品点餐区域</h3>
        <el-table :data="foodOrderData" border stripe>
          <el-table-column prop="date" label="就餐日期" width="180" align="center"></el-table-column>
          <el-table-column prop="orderType" label="订单类型" width="180" align="center"></el-table-column>
          <el-table-column prop="mealPeriod" label="就餐时段" align="center"></el-table-column>
          <el-table-column prop="reportedNum" label="已报餐-总人数" align="center"></el-table-column>
        </el-table>
      </div>
    </div>
    
    <div class="reminder-settings">
      <h2>报餐统计-设置通知</h2>
      <div class="reminder-list">
        <div class="reminder-card" v-for="(reminder, index) in reminderList" :key="index">
          <div class="reminder-header">
            <span>通知{{ index + 1 }}</span>
            <div class="button-group">
              <el-button size="mini" type="success">编辑</el-button>
              <el-button size="mini" type="danger">删除</el-button>
            </div>
          </div>
          <div class="reminder-content">
            <div class="reminder-item">通知时间：{{ reminder.time }}</div>
            <div class="reminder-item">通知内容：{{ reminder.content }}</div>
            <div class="reminder-item">通知地点：{{ reminder.locations.join('、') }}</div>
          </div>
        </div>
      </div>
      
      <div class="add-reminder">
        <el-button type="primary" size="small">新增报餐通知</el-button>
      </div>
    </div>
    
    <el-dialog
      title="新增报餐通知"
      :visible.sync="dialogVisible"
      width="500px">
      <div class="dialog-content">
        <div class="form-item">
          <span class="label">通知时间：</span>
          <el-date-picker
            v-model="newReminder.date"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
          <el-time-picker
            v-model="newReminder.time"
            placeholder="选择时间">
          </el-time-picker>
        </div>
        <div class="form-item">
          <span class="label">通知内容：</span>
          <el-checkbox-group v-model="newReminder.contentTypes">
            <el-checkbox label="午餐正常用餐"></el-checkbox>
            <el-checkbox label="午餐加班用餐"></el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="form-item">
          <span class="label">通知地点：</span>
          <el-checkbox-group v-model="newReminder.locations">
            <el-checkbox label="门店1"></el-checkbox>
            <el-checkbox label="门店2"></el-checkbox>
            <el-checkbox label="门店3"></el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveReminder">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MealStatistic',
  data() {
    return {
      refreshTime: '2023-03-03 09:00:00',
      activeTab: 'all',
      dialogVisible: false,
      selfServiceData: [
        {
          date: '2023-03-03',
          orderType: '自助餐',
          mealPeriod: '午餐',
          reportedNum: 3,
          checkedInNum: 1,
          notCheckedInNum: 2
        }
      ],
      businessDiningData: [
        {
          date: '2023-03-03',
          orderType: '商务餐',
          mealPeriod: '午餐',
          reportedNum: 8
        },
        {
          date: '2023-03-03',
          orderType: '商务餐',
          mealPeriod: '午餐',
          reportedNum: 5
        }
      ],
      foodOrderData: [
        {
          date: '2023-03-03',
          orderType: '商品点餐',
          mealPeriod: '午餐',
          reportedNum: 8
        }
      ],
      reminderList: [
        {
          time: '每天17:00:00',
          content: '午餐/加班用餐',
          locations: ['门店1', '门店2', '门店3']
        },
        {
          time: '每周五19:00:00',
          content: '午餐/加班用餐',
          locations: ['门店1', '门店2']
        },
        {
          time: '每天13:00:00',
          content: '午餐/加班用餐',
          locations: ['门店1']
        }
      ],
      newReminder: {
        date: '',
        time: '',
        contentTypes: [],
        locations: []
      }
    }
  },
  methods: {
    saveReminder() {
      // 处理保存新提醒的逻辑
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.meal-statistic {
  background-color: #fff;
  padding: 20px;
  
  .header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h1 {
      margin: 0;
    }
    
    .refresh-time {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .statistic-content {
    margin-top: 20px;
  }
  
  .statistic-section {
    margin-bottom: 30px;
    
    h3 {
      border-left: 4px solid #409EFF;
      padding-left: 10px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 5px;
      }
    }
  }
  
  .reminder-settings {
    margin-top: 40px;
    
    h2 {
      border-bottom: 1px solid #EBEEF5;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
    
    .reminder-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .reminder-card {
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      width: 300px;
      
      .reminder-header {
        background-color: #f5f7fa;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .button-group {
          display: flex;
          gap: 5px;
        }
      }
      
      .reminder-content {
        padding: 15px;
        
        .reminder-item {
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .add-reminder {
      margin-top: 20px;
    }
  }
  
  .dialog-content {
    .form-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        display: block;
        margin-bottom: 10px;
      }
    }
  }
}
</style>