<template>
  <div class="mini-program">
    <h1>小程序首页配置</h1>
    
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="产品宣传区" name="product"></el-tab-pane>
      <el-tab-pane label="文化宣传区" name="culture"></el-tab-pane>
      <el-tab-pane label="公告发布区" name="announcement"></el-tab-pane>
    </el-tabs>
    
    <!-- 产品宣传区 -->
    <div v-if="activeTab === 'product'" class="config-section">
      <div v-for="(item, index) in productItems" :key="'product-'+index" class="config-item">
        <div class="config-card">
          <div class="left-badge" :class="{ 'editing': index === 0, 'under-review': index === 1 }">
            {{ index === 0 ? '编辑中' : '审核中' }}
          </div>
          
          <div class="item-container">
            <div class="image-container">
              <img :src="item.imageUrl || 'https://via.placeholder.com/150'" class="preview-image">
            </div>
            
            <div class="form-container">
              <el-form label-width="90px">
                <el-form-item label="宣传标题">
                  <el-input v-model="item.title" placeholder="产品名称，品牌名称等"></el-input>
                </el-form-item>
                <el-form-item label="宣传副标题">
                  <el-input v-model="item.subtitle" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="图片链接">
                  <el-input v-model="item.imageLink" placeholder="https://baidu.com"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
          
          <div class="action-buttons">
            <el-button type="primary" size="small" class="blue-button">{{ index === 0 ? '下架' : '上架' }}</el-button>
            <el-button type="info" size="small" class="light-blue-button">编辑</el-button>
            <el-button type="danger" size="small">删除</el-button>
          </div>
        </div>
      </div>
      
      <div class="add-button-container">
        <el-button type="primary" icon="el-icon-plus" circle @click="showAddDialog('product')"></el-button>
      </div>
    </div>
    
    <!-- 文化宣传区 -->
    <div v-if="activeTab === 'culture'" class="config-section">
      <div v-for="(item, index) in cultureItems" :key="'culture-'+index" class="config-item">
        <div class="config-card">
          <div class="left-badge" :class="{ 'editing': index === 0, 'under-review': index === 1 }">
            {{ index === 0 ? '编辑中' : '审核中' }}
          </div>
          
          <div class="item-container">
            <div class="image-container">
              <img :src="item.imageUrl || 'https://via.placeholder.com/150'" class="preview-image">
            </div>
            
            <div class="form-container">
              <el-form label-width="90px">
                <el-form-item label="宣传标题">
                  <el-input v-model="item.title" placeholder="产品名称，品牌名称等"></el-input>
                </el-form-item>
                <el-form-item label="宣传副标题">
                  <el-input v-model="item.subtitle" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="图片链接">
                  <el-input v-model="item.imageLink" placeholder="https://baidu.com"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
          
          <div class="action-buttons">
            <el-button type="primary" size="small" class="blue-button">{{ index === 0 ? '下架' : '上架' }}</el-button>
            <el-button type="info" size="small" class="light-blue-button">编辑</el-button>
            <el-button type="danger" size="small">删除</el-button>
          </div>
        </div>
      </div>
      
      <div class="add-button-container">
        <el-button type="primary" icon="el-icon-plus" circle @click="showAddDialog('culture')"></el-button>
      </div>
    </div>
    
    <!-- 公告发布区 -->
    <div v-if="activeTab === 'announcement'" class="config-section">
      <div v-for="(item, index) in announcementItems" :key="'announcement-'+index" class="config-item">
        <div class="config-card">
          <div class="left-badge" :class="{ 'editing': index === 0, 'under-review': index === 1 }">
            {{ index === 0 ? '编辑中' : '审核中' }}
          </div>
          
          <div class="item-container">
            <div class="image-container">
              <img :src="item.imageUrl || 'https://via.placeholder.com/150'" class="preview-image">
            </div>
            
            <div class="form-container">
              <el-form label-width="90px">
                <el-form-item label="公告发布时间">
                  <div class="date-range">
                    <el-radio v-model="item.isForever" :label="false">指定时间</el-radio>
                    <template v-if="!item.isForever">
                      <el-date-picker v-model="item.startDate" type="date" placeholder="开始日期"></el-date-picker>
                      <span class="date-separator">至</span>
                      <el-date-picker v-model="item.endDate" type="date" placeholder="结束日期"></el-date-picker>
                    </template>
                  </div>
                  <div class="forever-option">
                    <el-radio v-model="item.isForever" :label="true">长期有效</el-radio>
                  </div>
                </el-form-item>
                <el-form-item label="公告内容">
                  <el-input type="textarea" v-model="item.content" placeholder="请输入公告内容！"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
          
          <div class="action-buttons">
            <el-button type="primary" size="small" class="blue-button">{{ index === 0 ? '下架' : '上架' }}</el-button>
            <el-button type="info" size="small" class="light-blue-button">编辑</el-button>
            <el-button type="danger" size="small">删除</el-button>
          </div>
        </div>
      </div>
      
      <div class="add-button-container">
        <el-button type="primary" icon="el-icon-plus" circle @click="showAddDialog('announcement')"></el-button>
      </div>
    </div>
    
    <!-- 添加项目对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="650px">
      <div v-if="dialogType === 'product' || dialogType === 'culture'">
        <el-form :model="currentItem" label-width="100px">
          <el-form-item label="宣传标题">
            <el-input v-model="currentItem.title" placeholder="产品名称，品牌名称等"></el-input>
          </el-form-item>
          <el-form-item label="宣传副标题">
            <el-input v-model="currentItem.subtitle" placeholder=""></el-input>
          </el-form-item>
          <el-form-item label="图片">
            <div class="upload-container">
              <el-upload
                class="avatar-uploader"
                action="/upload"
                :show-file-list="false"
                :on-success="handleImageSuccess">
                <img v-if="currentItem.imageUrl" :src="currentItem.imageUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="图片链接">
            <el-input v-model="currentItem.imageLink" placeholder="https://baidu.com"></el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <div v-if="dialogType === 'announcement'">
        <el-form :model="currentItem" label-width="100px">
          <el-form-item label="公告发布时间">
            <div class="date-range">
              <el-radio v-model="currentItem.isForever" :label="false">指定时间</el-radio>
              <template v-if="!currentItem.isForever">
                <el-date-picker v-model="currentItem.startDate" type="date" placeholder="开始日期"></el-date-picker>
                <span class="date-separator">至</span>
                <el-date-picker v-model="currentItem.endDate" type="date" placeholder="结束日期"></el-date-picker>
              </template>
            </div>
            <div class="forever-option">
              <el-radio v-model="currentItem.isForever" :label="true">长期有效</el-radio>
            </div>
          </el-form-item>
          <el-form-item label="公告内容">
            <el-input type="textarea" v-model="currentItem.content" placeholder="请输入公告内容！"></el-input>
          </el-form-item>
          <el-form-item label="图片">
            <div class="upload-container">
              <el-upload
                class="avatar-uploader"
                action="/upload"
                :show-file-list="false"
                :on-success="handleImageSuccess">
                <img v-if="currentItem.imageUrl" :src="currentItem.imageUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </div>
          </el-form-item>
        </el-form>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveItem">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MiniProgram',
  data() {
    return {
      activeTab: 'product',
      dialogVisible: false,
      dialogType: '',
      dialogTitle: '',
      currentItem: {},
      
      productItems: [
        {
          title: '产品名称，品牌名称等',
          subtitle: '',
          imageUrl: 'https://via.placeholder.com/150',
          imageLink: 'https://baidu.com'
        },
        {
          title: '产品名称，品牌名称等',
          subtitle: '',
          imageUrl: 'https://via.placeholder.com/150',
          imageLink: 'https://baidu.com'
        }
      ],
      
      cultureItems: [
        {
          title: '文化名称，品牌故事等',
          subtitle: '',
          imageUrl: 'https://via.placeholder.com/150',
          imageLink: 'https://baidu.com'
        },
        {
          title: '文化名称，品牌故事等',
          subtitle: '',
          imageUrl: 'https://via.placeholder.com/150',
          imageLink: 'https://baidu.com'
        }
      ],
      
      announcementItems: [
        {
          startDate: new Date(),
          endDate: new Date(new Date().setDate(new Date().getDate() + 30)),
          isForever: false,
          content: '公告内容示例，请输入公告内容！',
          imageUrl: 'https://via.placeholder.com/150'
        },
        {
          startDate: new Date(),
          endDate: new Date(new Date().setDate(new Date().getDate() + 30)),
          isForever: true,
          content: '公告内容示例，请输入公告内容！',
          imageUrl: 'https://via.placeholder.com/150'
        }
      ]
    }
  },
  methods: {
    showAddDialog(type) {
      this.dialogType = type;
      
      if (type === 'product') {
        this.dialogTitle = '添加产品宣传';
        this.currentItem = {
          title: '',
          subtitle: '',
          imageUrl: '',
          imageLink: ''
        };
      } else if (type === 'culture') {
        this.dialogTitle = '添加文化宣传';
        this.currentItem = {
          title: '',
          subtitle: '',
          imageUrl: '',
          imageLink: ''
        };
      } else if (type === 'announcement') {
        this.dialogTitle = '添加公告';
        this.currentItem = {
          startDate: new Date(),
          endDate: new Date(new Date().setDate(new Date().getDate() + 30)),
          isForever: false,
          content: '',
          imageUrl: ''
        };
      }
      
      this.dialogVisible = true;
    },
    
    handleImageSuccess(res, file) {
      // 模拟上传成功后的处理
      this.currentItem.imageUrl = URL.createObjectURL(file.raw);
    },
    
    saveItem() {
      if (this.dialogType === 'product') {
        this.productItems.unshift({...this.currentItem});
      } else if (this.dialogType === 'culture') {
        this.cultureItems.unshift({...this.currentItem});
      } else if (this.dialogType === 'announcement') {
        this.announcementItems.unshift({...this.currentItem});
      }
      
      this.dialogVisible = false;
      this.$message.success('添加成功');
    }
  }
}
</script>

<style lang="less" scoped>
.mini-program {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
  }
  
  .config-section {
    margin-top: 20px;
  }
  
  .config-item {
    margin-bottom: 20px;
  }
  
  .config-card {
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    
    .left-badge {
      position: absolute;
      top: 0;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: white;
      font-size: 12px;
      transform: rotate(-45deg) translate(-22px, -8px);
      transform-origin: center;
      z-index: 1;
      
      &.editing {
        background-color: #409EFF;
      }
      
      &.under-review {
        background-color: #E6A23C;
      }
    }
    
    .item-container {
      display: flex;
      padding: 20px;
      
      .image-container {
        width: 150px;
        height: 150px;
        margin-right: 20px;
        
        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border: 1px solid #EBEEF5;
        }
      }
      
      .form-container {
        flex: 1;
      }
    }
    
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      padding: 10px 20px;
      border-top: 1px solid #EBEEF5;
      
      .el-button {
        margin-left: 10px;
      }
      
      .blue-button {
        background-color: #409EFF;
        border-color: #409EFF;
      }
      
      .light-blue-button {
        background-color: #ecf5ff;
        border-color: #b3d8ff;
        color: #409EFF;
      }
    }
  }
  
  .add-button-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 20px;
  }
  
  .date-range {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .el-date-editor {
      margin: 0 5px;
    }
    
    .date-separator {
      margin: 0 5px;
    }
  }
  
  .forever-option {
    margin-top: 10px;
  }
  
  .upload-container {
    width: 100%;
  }
  
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 150px;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }
  
  .avatar {
    width: 150px;
    height: 150px;
    display: block;
    object-fit: cover;
  }
}
</style>