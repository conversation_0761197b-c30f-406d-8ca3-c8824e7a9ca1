<template>
  <div class="hot-electron-page">
    <el-form :model="temporary" class="top-container" @submit.native.prevent>
      <el-form-item>
        <span slot="label">用户账户：</span>
        <el-input v-model="temporary.username" placeholder="输入用户账号" maxlength="20"/>
      </el-form-item>
      <el-button plain @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleFilter">查询</el-button>
    </el-form>

    <div class="page-middle">
      <el-button
        class="button-full-wrapper"
        type="primary"
        @click="updateAdmin('add')"
      ><span class="iconfont icon-anniutubiao-ziti" />
        <span class="button-txt">添加用户</span>
      </el-button>
    </div>

    <Table
      :columns="columns"
      :loading="listLoading"
      :data="tableData"
      :total="total"
      :has-page="false"
      :table-config="tableConfig"
      @getData="getAdminList"
    >
      <el-table-column slot="username" label="用户名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="nickname" label="昵称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="realname" label="真实姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.realname }}</span>
        </template>
      </el-table-column>
      <el-table-column slot="email" label="邮箱" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.email }}</span>
        </template>
      </el-table-column>
      <el-table-column
        slot="action"
        align="center"
        label="操作"
        class-name="action-class"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="updateAdmin('edit', scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </Table>

    <el-dialog
      :title="textMap[dialogStatus] + '管理员'"
      :visible.sync="administratorsDialogVisible"
      width="600px"
    >
      <el-form
        ref="adminForm"
        :rules="rules"
        :model="adminData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="登录账户" prop="username">
          <el-input v-model="adminData.username" :disabled="dialogStatus === 'edit'" />
        </el-form-item>
        <div v-if="dialogStatus === 'add'">
          <el-form-item label="登录密码" prop="password">
            <el-input
              v-model="adminData.password"
              placeholder="必须是6位数字或字母，字符或特殊字符（空格除外）"
            />
          </el-form-item>
        </div>
        <div v-else>
          <el-form-item
            label="登录密码"
            prop="password"
            :rules="[{ min: 6, message: '密码最少6位数', trigger: 'blur' }]"
          >
            <el-input
              v-model="adminData.password"
              placeholder="必须是6位数字或字母，字符或特殊字符（空格除外）"
            />
          </el-form-item>
        </div>
        <el-form-item label="昵称" prop="nickname" maxlength="20">
          <el-input v-model="adminData.nickname" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realname" maxlength="20">
          <el-input v-model="adminData.realname" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email" maxlength="50" :rules="[
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ]">
          <el-input v-model="adminData.email" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="administratorsDialogVisible = false"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="saveAdminInfo(dialogStatus)"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from '@/utils/request'
import Table from '@/components/Table.vue'

const defaultQuery = {
  username: null
}

const defaultAdminData = {
  username: null,
  nickname: null,
  realname: null,
  password: null,
  email: null
}

export default {
  name: 'Administrator',
  components: { Table },
  data() {
    return {
      tableData: [], // 管理列表
      total: 1,
      listLoading: true, // 列表加载中
      // 表格配置
      tableConfig: {
        height: 62,
        withPagination: true
      },
      // 表格表头字段
      columns: [
        { hasIndex: true, width: 90 },
        { slot: 'username' },
        { slot: 'nickname' },
        { slot: 'realname' },
        { slot: 'action' }
      ],
      temporary: Object.assign({}, defaultQuery),
      listQuery: Object.assign({
        page: 1,
        page_size: 20
      }, defaultQuery),
      adminData: Object.assign({}, defaultAdminData), // 管理员数据
      administratorsDialogVisible: false, // 创建管理员弹框
      dialogStatus: '', // 弹框状态 add edit
      textMap: {
        edit: '编辑',
        add: '创建'
      },
      adminId: '', // 选中的管理员id
      rules: {
        nickname: [
          { required: true, message: '请填写管理员昵称', trigger: 'blur' },
          { min: 2, message: '登录账户最少2个字符', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请填写登录账户', trigger: 'blur' },
          { min: 6, message: '登录账户最少6个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请填写登录密码', trigger: 'blur' },
          { min: 6, message: '密码最少6位数', trigger: 'blur' }
        ],
        realname: [
          { required: true, message: '请填写真实姓名', trigger: 'blur' },
          { min: 2, message: '登录账户最少2个字符', trigger: 'blur' }
        ],
        email: [
          { validator: this.validateUserEmail, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getAdminList()
  },
  methods: {
    // 校验邮箱
    validateUserEmail(rule, value, callback) {
      var strRegex = /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/
      if ((!/^[^\u4e00-\u9fa5]+$/.test(value) || !strRegex.test(value)) && value) {
        callback(new Error('请输入正确的邮箱格式'))
      }
      callback()
    },

    // 重置筛选
    handleReset() {
      this.temporary = Object.assign({}, defaultQuery)
      this.listQuery = Object.assign({
        page: 1,
        page_size: 20
      }, defaultQuery)
      this.getAdminList()
    },

    // 搜索功能
    handleFilter() {
      this.listQuery = Object.assign(this.listQuery, this.temporary)
      this.listQuery.page = 1
      this.getAdminList()
    },

    // 获取管理列表
    getAdminList() {
      this.listLoading = true
      requestApi({
        name: 'getSystemAdminList',
        data: this.listQuery
      }).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.list || []
          this.listLoading = false
        }
      })
    },

    // 添加/编辑管理员
    updateAdmin(state, row) {
      this.adminData = Object.assign({}, defaultAdminData)
      this.adminId = null
      this.dialogStatus = state
      this.administratorsDialogVisible = true
      this.$nextTick(() => {
        this.$refs['adminForm'].clearValidate()
      })
      if (state === 'edit') {
        this.adminId = row.id
        requestApi({
          name: 'getSystemAdminDetail',
          data: {
            api_id: this.adminId
          }
        }).then((res) => {
          if (res.code === 200) {
            this.adminData = res.data.admin
          }
        })
      }
    },

    // 新增/更新管理员
    saveAdminInfo() {
      this.$refs['adminForm'].validate((valid) => {
        if (valid) {
          const { username, nickname, realname, password, email } = this.adminData
          const data = { username, nickname, realname, email }
          if (this.dialogStatus === 'edit') data.api_id = this.adminId
          // if (password) data.password = sha256(password);

          requestApi({
            name: this.dialogStatus === 'add' ? 'addSystemAdmin' : 'updataSystemAdmin',
            data
          }).then((res) => {
            if (res.code === 200) {
              this.administratorsDialogVisible = false
              this.getAdminList()
              this.$message.success(
                this.dialogStatus === 'edit' ? '更新成功' : '创建成功'
              )
            }
          })
        }
      })
    }
  }
}
</script>
