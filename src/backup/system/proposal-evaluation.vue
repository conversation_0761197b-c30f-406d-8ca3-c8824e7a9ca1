<template>
  <div class="proposal-evaluation">
    <h1>建议与评价</h1>
    <div class="filter-container">
      <div class="filter-item">
        <span>姓名：</span>
        <input v-model="queryParams.name" type="text" placeholder="请输入姓名" />
      </div>
      <div class="filter-item">
        <span>所属公司：</span>
        <input v-model="queryParams.company" type="text" placeholder="请输入公司名称" />
      </div>
      <div class="button-group">
        <button class="search-btn" @click="getList">查询</button>
        <button class="reset-btn" @click="resetQuery">重置</button>
      </div>
    </div>
    <div class="add-btn-container">
      <button class="add-btn">导出清单</button>
    </div>
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>日期时间</th>
            <th>姓名</th>
            <th>所属公司</th>
            <th>评价内容</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in tableData" :key="index">
            <td>{{ item.id }}</td>
            <td>{{ item.dateTime }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.company }}</td>
            <td>{{ item.content }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProposalEvaluation',
  data() {
    return {
      title: '建议与评价',
      queryParams: {
        name: '',
        company: ''
      },
      tableData: [
        {
          id: 1,
          dateTime: '2025-03-04 18:12:45',
          name: '张三',
          company: '',
          content: 'xxxx'
        },
        {
          id: 1,
          dateTime: '2025-03-04 12:05:42',
          name: '李四',
          company: '广州市XXX有限公司',
          content: 'xxxxx'
        }
      ]
    }
  },
  methods: {
    getList() {
      // 实际项目中应该调用API获取数据
      console.log('查询参数：', this.queryParams)
    },
    resetQuery() {
      this.queryParams = {
        name: '',
        company: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.proposal-evaluation {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    font-size: 18px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }
  
  .filter-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 20px;
    
    .filter-item {
      margin-right: 15px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      
      span {
        margin-right: 5px;
      }
      
      input {
        width: 200px;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 0 10px;
      }
    }
    
    .button-group {
      button {
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
      }
      
      .search-btn {
        background-color: #409eff;
        color: white;
        border: none;
      }
      
      .reset-btn {
        background-color: #fff;
        border: 1px solid #dcdfe6;
      }
    }
  }
  
  .add-btn-container {
    text-align: left;
    margin-bottom: 15px;
    
    .add-btn {
      padding: 8px 15px;
      background-color: #67c23a;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  
  .table-container {
    width: 100%;
    overflow-x: auto;
    
    .data-table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        border: 1px solid #ebeef5;
        padding: 12px;
        text-align: left;
      }
      
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }
      
      tbody tr:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>

