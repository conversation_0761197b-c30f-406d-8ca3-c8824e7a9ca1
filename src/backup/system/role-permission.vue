<template>
  <div class="role-permission">
    <div class="member-management">
      <h2>成员管理</h2>
      <div class="action-buttons">
        <el-button type="success" size="mini" @click="addMember">新增成员</el-button>
      </div>
      <el-table :data="memberList" border style="width: 100%">
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="name" label="姓名" align="center"></el-table-column>
        <el-table-column prop="phone" label="手机" align="center"></el-table-column>
        <el-table-column prop="department" label="部门" align="center"></el-table-column>
        <el-table-column prop="position" label="职位" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="editMember(scope.row)">编辑</el-button>
            <el-button type="text" class="delete-btn" @click="deleteMember(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="admin-management">
      <h2>子管理员</h2>
      <div class="action-buttons">
        <el-button type="warning" size="mini" @click="addAdmin">添加子管理员</el-button>
      </div>
      <el-table :data="adminList" border style="width: 100%">
        <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column prop="name" label="姓名" align="center"></el-table-column>
        <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
        <el-table-column prop="department" label="部门" align="center"></el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '启用' ? 'success' : 'info'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="permission" label="权限" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="editAdmin(scope.row)">编辑</el-button>
            <el-button type="text" class="delete-btn" @click="deleteAdmin(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="empty-data" v-if="adminList.length === 0">
        <i class="el-icon-monitor"></i>
        <p>暂无数据</p>
      </div>
    </div>

    <!-- 新增/编辑成员弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="memberDialogVisible" width="500px">
      <el-form :model="memberForm" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="memberForm.name"></el-input>
        </el-form-item>
        <el-form-item label="手机">
          <el-input v-model="memberForm.phone"></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="memberForm.department" placeholder="请选择部门" style="width: 100%">
            <el-option label="厨房部" value="厨房部"></el-option>
            <el-option label="行政部" value="行政部"></el-option>
            <el-option label="财务部" value="财务部"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="职位">
          <el-input v-model="memberForm.position"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="memberDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveMember">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑管理员弹窗 -->
    <el-dialog :title="adminDialogTitle" :visible.sync="adminDialogVisible" width="500px">
      <el-form :model="adminForm" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="adminForm.name"></el-input>
        </el-form-item>
        <el-form-item label="手机">
          <el-input v-model="adminForm.phone"></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="adminForm.department" placeholder="请选择部门" style="width: 100%">
            <el-option label="厨房部" value="厨房部"></el-option>
            <el-option label="行政部" value="行政部"></el-option>
            <el-option label="财务部" value="财务部"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="adminForm.statusBool" 
                    active-text="启用" 
                    inactive-text="禁用">
          </el-switch>
        </el-form-item>
        <el-form-item label="权限">
          <el-checkbox-group v-model="adminForm.permissions">
            <el-checkbox label="订单管理">订单管理</el-checkbox>
            <el-checkbox label="商品管理">商品管理</el-checkbox>
            <el-checkbox label="用户管理">用户管理</el-checkbox>
            <el-checkbox label="系统设置">系统设置</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="adminDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveAdmin">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RolePermission',
  data() {
    return {
      // 成员管理数据
      memberList: [
        { index: 1, name: '', phone: '157xxxx9584', department: '厨房部', position: '厨师长', createTime: '2025-02-13 10:24:18' },
        { index: 2, name: '李四', phone: '158xxxx1234', department: '行政部', position: '采购员', createTime: '2025-02-13 10:24:18' },
        { index: 3, name: '王五', phone: '159xxxx9584', department: '财务部', position: '会计', createTime: '2025-02-13 10:24:18' }
      ],
      memberDialogVisible: false,
      dialogTitle: '新增成员',
      memberForm: {
        name: '',
        phone: '',
        department: '',
        position: ''
      },
      editingMemberIndex: -1,
      
      // 子管理员数据
      adminList: [],
      adminDialogVisible: false,
      adminDialogTitle: '添加子管理员',
      adminForm: {
        name: '',
        phone: '',
        department: '',
        statusBool: true,
        permissions: []
      },
      editingAdminIndex: -1
    }
  },
  methods: {
    // 成员管理方法
    addMember() {
      this.dialogTitle = '新增成员'
      this.memberForm = {
        name: '',
        phone: '',
        department: '',
        position: ''
      }
      this.editingMemberIndex = -1
      this.memberDialogVisible = true
    },
    editMember(row) {
      this.dialogTitle = '编辑成员'
      this.memberForm = { ...row }
      this.editingMemberIndex = this.memberList.findIndex(item => item.index === row.index)
      this.memberDialogVisible = true
    },
    deleteMember(row) {
      this.$confirm('确定要删除该成员吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.memberList.findIndex(item => item.index === row.index)
        if (index !== -1) {
          this.memberList.splice(index, 1)
          // 重新排序
          this.memberList.forEach((item, i) => {
            item.index = i + 1
          })
        }
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    saveMember() {
      if (!this.memberForm.name || !this.memberForm.phone || !this.memberForm.department || !this.memberForm.position) {
        return this.$message.warning('请填写完整信息')
      }
      
      const now = new Date()
      const formatDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`
      
      if (this.editingMemberIndex === -1) {
        // 新增
        const newMember = {
          ...this.memberForm,
          index: this.memberList.length + 1,
          createTime: formatDate
        }
        this.memberList.push(newMember)
      } else {
        // 编辑
        this.memberList[this.editingMemberIndex] = {
          ...this.memberList[this.editingMemberIndex],
          ...this.memberForm
        }
      }
      
      this.memberDialogVisible = false
      this.$message.success(this.editingMemberIndex === -1 ? '添加成功' : '修改成功')
    },
    
    // 管理员管理方法
    addAdmin() {
      this.adminDialogTitle = '添加子管理员'
      this.adminForm = {
        name: '',
        phone: '',
        department: '',
        statusBool: true,
        permissions: []
      }
      this.editingAdminIndex = -1
      this.adminDialogVisible = true
    },
    editAdmin(row) {
      this.adminDialogTitle = '编辑子管理员'
      this.adminForm = { 
        ...row,
        statusBool: row.status === '启用',
        permissions: row.permission ? row.permission.split(',') : []
      }
      this.editingAdminIndex = this.adminList.findIndex(item => item.index === row.index)
      this.adminDialogVisible = true
    },
    deleteAdmin(row) {
      this.$confirm('确定要删除该管理员吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.adminList.findIndex(item => item.index === row.index)
        if (index !== -1) {
          this.adminList.splice(index, 1)
          // 重新排序
          this.adminList.forEach((item, i) => {
            item.index = i + 1
          })
        }
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    saveAdmin() {
      if (!this.adminForm.name || !this.adminForm.phone || !this.adminForm.department) {
        return this.$message.warning('请填写完整信息')
      }
      
      const adminData = {
        ...this.adminForm,
        status: this.adminForm.statusBool ? '启用' : '禁用',
        permission: this.adminForm.permissions.join(',')
      }
      
      if (this.editingAdminIndex === -1) {
        // 新增
        const newAdmin = {
          ...adminData,
          index: this.adminList.length + 1
        }
        this.adminList.push(newAdmin)
      } else {
        // 编辑
        this.adminList[this.editingAdminIndex] = {
          ...this.adminList[this.editingAdminIndex],
          ...adminData
        }
      }
      
      this.adminDialogVisible = false
      this.$message.success(this.editingAdminIndex === -1 ? '添加成功' : '修改成功')
    }
  }
}
</script>

<style lang="less" scoped>
.role-permission {
  background-color: #fff;
  padding: 20px;
  
  h2 {
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: bold;
  }
  
  .action-buttons {
    margin-bottom: 15px;
  }
  
  .member-management {
    margin-bottom: 30px;
  }
  
  .admin-management {
    margin-bottom: 30px;
  }
  
  .empty-data {
    text-align: center;
    padding: 50px 0;
    color: #909399;
    
    i {
      font-size: 48px;
      margin-bottom: 10px;
    }
  }
  
  .delete-btn {
    color: #F56C6C;
  }
}
</style>