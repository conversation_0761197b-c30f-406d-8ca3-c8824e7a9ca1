<template>
  <div class="business-information">
    <h1>企业信息</h1>
    <el-form :model="companyInfo" label-width="120px" ref="companyInfoForm">
      <div class="info-section">
        <div class="section-header">
          <h3><i class="el-icon-office-building"></i> 单位信息</h3>
          <el-button type="primary" size="small" @click="saveCompanyInfo">保存更新</el-button>
        </div>
        <el-form-item label="企业ID:">
          <el-input v-model="companyInfo.companyId" disabled></el-input>
        </el-form-item>
        <el-form-item label="企业名称:">
          <el-input v-model="companyInfo.companyName" placeholder="请输入企业名称"></el-input>
        </el-form-item>
        <el-form-item label="企业地址:">
          <el-input v-model="companyInfo.companyAddress" placeholder="请输入企业地址"></el-input>
        </el-form-item>
        <el-form-item label="企业邮箱:">
          <el-input v-model="companyInfo.companyEmail" placeholder="请输入企业邮箱"></el-input>
        </el-form-item>
        <el-form-item label="企业税号:">
          <el-input v-model="companyInfo.taxNumber" placeholder="请输入企业税号"></el-input>
        </el-form-item>
        <el-form-item label="联系人姓名:">
          <el-input v-model="companyInfo.contactName" placeholder="请输入联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系人电话:">
          <el-input v-model="companyInfo.contactPhone" placeholder="请输入联系人电话"></el-input>
        </el-form-item>
      </div>

      <div class="info-section">
        <div class="section-header">
          <h3><i class="el-icon-user"></i> 超级管理员</h3>
          <el-button type="warning" size="small" @click="modifyPassword">修改密码</el-button>
        </div>
        <el-form-item label="超管账号:">
          <el-input v-model="companyInfo.adminAccount" disabled></el-input>
        </el-form-item>
        <el-form-item label="超管密码:">
          <el-input v-model="companyInfo.adminPassword" type="password" disabled show-password></el-input>
        </el-form-item>
      </div>

      <div class="info-section">
        <div class="section-header">
          <h3><i class="el-icon-s-custom"></i> 子管理员</h3>
          <el-button type="primary" size="small" @click="addSubAdmin">添加子管理员</el-button>
        </div>
        <el-table :data="subAdmins" style="width: 100%">
          <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.status === '启用' ? 'status-active' : 'status-inactive'">{{ scope.row.status }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
          <el-table-column label="操作" width="220" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editSubAdmin(scope.row)">编辑</el-button>
              <el-button 
                type="text" 
                size="small" 
                :style="{color: scope.row.status === '启用' ? '#F56C6C' : '#67C23A'}"
                @click="toggleSubAdminStatus(scope.row)">
                {{ scope.row.status === '启用' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="text" size="small" style="color: #F56C6C" @click="deleteSubAdmin(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改超级管理员密码" :visible.sync="passwordDialogVisible" width="400px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPasswordChange">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加/编辑子管理员对话框 -->
    <el-dialog :title="dialogStatus === 'add' ? '添加子管理员' : '编辑子管理员'" :visible.sync="subAdminDialogVisible" width="500px">
      <el-form :model="subAdminForm" :rules="subAdminRules" ref="subAdminForm" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="subAdminForm.name" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="subAdminForm.phone" maxlength="11"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="subAdminDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSubAdmin">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BusinessInformation',
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else if (value.length < 6) {
        callback(new Error('密码长度不能少于6位'));
      } else {
        if (this.passwordForm.confirmPassword !== '') {
          this.$refs.passwordForm.validateField('confirmPassword');
        }
        callback();
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    const validatePhone = (rule, value, callback) => {
      const reg = /^1[3-9]\d{9}$/;
      if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的手机号码'));
      }
    };
    return {
      companyInfo: {
        companyId: '************',
        companyName: '广州市xxx有限公司',
        companyAddress: '广州市黄埔区',
        companyEmail: '<EMAIL>',
        taxNumber: '914401MA91578G4562',
        contactName: '魏某某',
        contactPhone: '020-********',
        adminAccount: '157xxxx9584',
        adminPassword: '********'
      },
      subAdmins: [
        {
          index: 1,
          name: '张三',
          phone: '157xxxx9584',
          status: '启用',
          createTime: '2025-02-19 12:15:10'
        },
        {
          index: 2,
          name: '李四',
          phone: '157xxxx1234',
          status: '禁用',
          createTime: '2025-02-19 12:15:10'
        }
      ],
      passwordDialogVisible: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPassword: [
          { validator: validatePass, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validatePass2, trigger: 'blur' }
        ]
      },
      subAdminDialogVisible: false,
      dialogStatus: 'add', // 'add' 或 'edit'
      subAdminForm: {
        id: null,
        name: '',
        phone: ''
      },
      subAdminRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 保存企业信息
    saveCompanyInfo() {
      this.$message({
        message: '保存成功',
        type: 'success'
      });
    },
    // 打开修改密码对话框
    modifyPassword() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.passwordDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.passwordForm && this.$refs.passwordForm.clearValidate();
      });
    },
    // 提交密码修改
    submitPasswordChange() {
      this.$refs.passwordForm.validate(valid => {
        if (valid) {
          this.$message({
            message: '密码修改成功',
            type: 'success'
          });
          this.passwordDialogVisible = false;
        }
      });
    },
    // 添加子管理员
    addSubAdmin() {
      this.dialogStatus = 'add';
      this.subAdminForm = {
        id: null,
        name: '',
        phone: ''
      };
      this.subAdminDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.subAdminForm && this.$refs.subAdminForm.clearValidate();
      });
    },
    // 编辑子管理员
    editSubAdmin(row) {
      this.dialogStatus = 'edit';
      this.subAdminForm = {
        id: row.index,
        name: row.name,
        phone: row.phone
      };
      this.subAdminDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.subAdminForm && this.$refs.subAdminForm.clearValidate();
      });
    },
    // 切换子管理员状态
    toggleSubAdminStatus(row) {
      const status = row.status === '启用' ? '禁用' : '启用';
      this.$confirm(`确认要${status}该管理员吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = status;
        this.$message({
          type: 'success',
          message: '状态已更新'
        });
      }).catch(() => {});
    },
    // 删除子管理员
    deleteSubAdmin(row) {
      this.$confirm('确认要删除该管理员吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.subAdmins.findIndex(item => item.index === row.index);
        if (index !== -1) {
          this.subAdmins.splice(index, 1);
        }
        this.$message({
          type: 'success',
          message: '删除成功'
        });
      }).catch(() => {});
    },
    // 提交子管理员表单
    submitSubAdmin() {
      this.$refs.subAdminForm.validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'add') {
            // 添加新管理员
            const newAdmin = {
              index: this.subAdmins.length + 1,
              name: this.subAdminForm.name,
              phone: this.subAdminForm.phone,
              status: '启用',
              createTime: new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
              }).replace(/\//g, '-')
            };
            this.subAdmins.push(newAdmin);
          } else {
            // 更新现有管理员
            const index = this.subAdmins.findIndex(item => item.index === this.subAdminForm.id);
            if (index !== -1) {
              this.subAdmins[index].name = this.subAdminForm.name;
              this.subAdmins[index].phone = this.subAdminForm.phone;
            }
          }
          this.subAdminDialogVisible = false;
          this.$message({
            message: this.dialogStatus === 'add' ? '添加成功' : '更新成功',
            type: 'success'
          });
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
.business-information {
  background-color: #fff;
  padding: 20px;

  h1 {
    margin-bottom: 30px;
    font-size: 20px;
    color: #303133;
  }

  .info-section {
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;

    .section-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #EBEEF5;
      padding-bottom: 10px;

      h3 {
        font-size: 16px;
        color: #303133;
        margin: 0;

        i {
          margin-right: 5px;
          color: #409EFF;
        }
      }
      
      .el-button {
        margin-left: 15px;
      }
    }
  }

  .status-active {
    color: #67C23A;
  }

  .status-inactive {
    color: #F56C6C;
  }

  /deep/ .el-input {
    width: 400px;
  }
}
</style>