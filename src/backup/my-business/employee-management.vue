<template>
  <div class="employee-management">
    <h1>员工管理</h1>
    
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="姓名:">
          <el-input v-model="filterForm.name" placeholder="输入姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机:">
          <el-input v-model="filterForm.phone" placeholder="输入手机号" clearable></el-input>
        </el-form-item>
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form>
    </div>
    
    <div class="action-container">
      <el-button type="primary" @click="handleAddEmployee">添加员工</el-button>
    </div>
    
    <div class="table-container">
      <el-table
        :data="employeeList"
        border
        style="width: 100%"
        v-loading="listLoading"
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="userId"
          label="用户ID"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="用户姓名"
          min-width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="phone"
          label="手机号码"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          label="企业信息查看及采购申报权限"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span :class="scope.row.companyInfoPerm ? 'status-active' : 'status-inactive'">
              {{ scope.row.companyInfoPerm ? '允许' : '不允许' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="企业签约及发票管理权限"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span :class="scope.row.companyContractPerm ? 'status-active' : 'status-inactive'">
              {{ scope.row.companyContractPerm ? '允许' : '不允许' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="供应商集中采购管理权限"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span :class="scope.row.supplierPurchasePerm ? 'status-active' : 'status-inactive'">
              {{ scope.row.supplierPurchasePerm ? '允许' : '不允许' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          min-width="160"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="180"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" class="delete-btn" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
    
    <!-- 添加/编辑员工对话框 -->
    <el-dialog :title="dialogStatus === 'add' ? '添加员工' : '编辑员工信息'" :visible.sync="dialogVisible" width="600px">
      <el-form :model="employeeForm" :rules="employeeRules" ref="employeeForm" label-width="150px">
        <el-form-item label="用户ID:" prop="userId" v-if="dialogStatus === 'edit'">
          <el-input v-model="employeeForm.userId" disabled></el-input>
        </el-form-item>
        <el-form-item label="用户姓名:" prop="name">
          <el-input v-model="employeeForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号码:" prop="phone">
          <el-input v-model="employeeForm.phone" placeholder="请输入手机号码"></el-input>
        </el-form-item>
        
        <el-form-item label="企业信息查看及采购申报权限:">
          <el-radio-group v-model="employeeForm.companyInfoPerm">
            <el-radio :label="true">允许</el-radio>
            <el-radio :label="false">不允许</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="企业签约及发票管理权限:">
          <el-radio-group v-model="employeeForm.companyContractPerm">
            <el-radio :label="true">允许</el-radio>
            <el-radio :label="false">不允许</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="供应商集中采购管理权限:">
          <el-radio-group v-model="employeeForm.supplierPurchasePerm">
            <el-radio :label="true">允许</el-radio>
            <el-radio :label="false">不允许</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EmployeeManagement',
  data() {
    // 手机号验证规则
    const validatePhone = (rule, value, callback) => {
      const reg = /^1[3-9]\d{9}$/;
      if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的手机号码'));
      }
    };
    
    return {
      title: '员工管理',
      
      // 筛选表单
      filterForm: {
        name: '',
        phone: ''
      },
      
      // 表格数据
      employeeList: [
        {
          index: 1,
          userId: '202504051226',
          name: '李四',
          phone: '150xxxx1234',
          companyInfoPerm: true,
          companyContractPerm: true,
          supplierPurchasePerm: true,
          createTime: '2025-02-19 10:24:18'
        },
        {
          index: 2,
          userId: '202504051226',
          name: '王五',
          phone: '159xxxx9584',
          companyInfoPerm: true,
          companyContractPerm: true,
          supplierPurchasePerm: true,
          createTime: '2025-02-19 10:24:18'
        },
        {
          index: 3,
          userId: '202504051237',
          name: '赵六',
          phone: '150xxxx1234',
          companyInfoPerm: false,
          companyContractPerm: false,
          supplierPurchasePerm: true,
          createTime: '2025-02-19 10:24:18'
        }
      ],
      
      // 分页相关
      listLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 3,
      
      // 对话框相关
      dialogVisible: false,
      dialogStatus: 'add', // 'add' 或 'edit'
      employeeForm: {
        userId: '',
        name: '',
        phone: '',
        companyInfoPerm: true,
        companyContractPerm: true,
        supplierPurchasePerm: true
      },
      employeeRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ]
      }
    }
  },
  
  methods: {
    // 过滤表格
    handleFilter() {
      this.currentPage = 1;
      this.getEmployeeList();
    },
    
    // 重置过滤器
    resetFilter() {
      this.filterForm = {
        name: '',
        phone: ''
      };
      this.handleFilter();
    },
    
    // 获取员工列表
    getEmployeeList() {
      this.listLoading = true;
      
      // 模拟API请求
      setTimeout(() => {
        // 在实际应用中应该调用接口
        const filteredList = this.employeeList.filter(item => {
          const nameMatch = !this.filterForm.name || item.name.includes(this.filterForm.name);
          const phoneMatch = !this.filterForm.phone || item.phone.includes(this.filterForm.phone);
          return nameMatch && phoneMatch;
        });
        
        this.total = filteredList.length;
        this.listLoading = false;
      }, 500);
    },
    
    // 处理页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getEmployeeList();
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getEmployeeList();
    },
    
    // 添加员工
    handleAddEmployee() {
      this.dialogStatus = 'add';
      this.employeeForm = {
        userId: '',
        name: '',
        phone: '',
        companyInfoPerm: true,
        companyContractPerm: true,
        supplierPurchasePerm: true
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.employeeForm && this.$refs.employeeForm.clearValidate();
      });
    },
    
    // 编辑员工
    handleEdit(row) {
      this.dialogStatus = 'edit';
      this.employeeForm = {
        userId: row.userId,
        name: row.name,
        phone: row.phone,
        companyInfoPerm: row.companyInfoPerm,
        companyContractPerm: row.companyContractPerm,
        supplierPurchasePerm: row.supplierPurchasePerm
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.employeeForm && this.$refs.employeeForm.clearValidate();
      });
    },
    
    // 删除员工
    handleDelete(row) {
      this.$confirm('确认要删除该员工吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应用中应该调用删除API
        const index = this.employeeList.findIndex(item => item.userId === row.userId);
        if (index !== -1) {
          this.employeeList.splice(index, 1);
          this.total = this.employeeList.length;
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        // 取消删除操作
      });
    },
    
    // 提交表单
    submitForm() {
      this.$refs.employeeForm.validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'add') {
            // 添加新员工
            const newEmployee = {
              index: this.employeeList.length + 1,
              userId: 'EP' + Date.now().toString().slice(-8), // 生成临时ID
              name: this.employeeForm.name,
              phone: this.employeeForm.phone,
              companyInfoPerm: this.employeeForm.companyInfoPerm,
              companyContractPerm: this.employeeForm.companyContractPerm,
              supplierPurchasePerm: this.employeeForm.supplierPurchasePerm,
              createTime: new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
              }).replace(/\//g, '-')
            };
            this.employeeList.push(newEmployee);
            this.total = this.employeeList.length;
          } else {
            // 更新员工信息
            const index = this.employeeList.findIndex(item => item.userId === this.employeeForm.userId);
            if (index !== -1) {
              this.employeeList[index] = {
                ...this.employeeList[index],
                name: this.employeeForm.name,
                phone: this.employeeForm.phone,
                companyInfoPerm: this.employeeForm.companyInfoPerm,
                companyContractPerm: this.employeeForm.companyContractPerm,
                supplierPurchasePerm: this.employeeForm.supplierPurchasePerm
              };
            }
          }
          
          this.dialogVisible = false;
          this.$message({
            message: this.dialogStatus === 'add' ? '添加成功' : '更新成功',
            type: 'success'
          });
        }
      });
    }
  },
  
  created() {
    this.getEmployeeList();
  }
}
</script>

<style lang="less" scoped>
.employee-management {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 20px;
    color: #303133;
  }
  
  .filter-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .action-container {
    margin-bottom: 15px;
    text-align: left;
  }
  
  .table-container {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 15px;
    text-align: right;
  }
  
  .status-active {
    color: #67C23A;
  }
  
  .status-inactive {
    color: #F56C6C;
  }
  
  .delete-btn {
    color: #F56C6C;
  }
  
  .el-button + .el-button {
    margin-left: 10px;
  }
  
  .note {
    margin-top: 10px;
    color: #E6A23C;
    font-size: 12px;
  }
}
</style>