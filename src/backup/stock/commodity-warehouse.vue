<template>
  <div class="commodity-warehouse">
    <h1>商品库</h1>
    <div class="top-container">
      <div class="left-buttons">
        <el-button type="primary" size="mini" @click="showNewCategoryDialog">新增分类</el-button>
        <el-button type="danger" size="mini" @click="batchDelete">批量删除</el-button>
      </div>
      <div class="search-container">
        <el-input v-model="searchName" placeholder="商品名称" size="mini" class="search-input"></el-input>
        <el-input v-model="searchCode" placeholder="商品编号" size="mini" class="search-input"></el-input>
        <el-button type="primary" size="mini" @click="search">查询</el-button>
      </div>
    </div>

    <div class="category-container">
      <div class="category-list">
        <div class="category-item" v-for="(item, index) in categoryList" :key="index">
          <div class="category-icon">
            <i class="el-icon-folder"></i>
          </div>
          <div class="category-name">{{ item.name }}</div>
          <div class="category-count">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" width="60" align="center">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column prop="productCode" label="商品编号" width="80" align="center"></el-table-column>
      <el-table-column label="商品图片" width="100" align="center">
        <template slot-scope="scope">
          <div class="product-image">
            <img :src="scope.row.imageUrl" alt="商品图片">
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" align="center"></el-table-column>
      <el-table-column prop="specification" label="规格" align="center"></el-table-column>
      <el-table-column prop="price" label="价格/单位" align="center">
        <template slot-scope="scope">
          {{ scope.row.price }} / {{ scope.row.unit }}
        </template>
      </el-table-column>
      <el-table-column prop="categoryName" label="商品分类" align="center"></el-table-column>
      <el-table-column prop="status" label="商品状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status === '上架' ? '上架' : '下架' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editProduct(scope.row)">编辑</el-button>
          <el-button type="warning" size="mini" @click="toggleStatus(scope.row)">{{ scope.row.status === '上架' ? '下架' : '上架' }}</el-button>
          <el-button type="danger" size="mini" @click="deleteProduct(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="10"
        layout="prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 新增分类对话框 -->
    <el-dialog title="新增商品分类" :visible.sync="categoryDialogVisible" width="500px">
      <el-form :model="categoryForm" label-width="100px">
        <el-form-item label="商品类型名称">
          <el-input v-model="categoryForm.typeName" placeholder="请输入商品类型名称"></el-input>
        </el-form-item>
        <el-form-item label="商品类型图标">
          <div class="upload-container">
            <el-upload
              class="avatar-uploader"
              action="/upload"
              :show-file-list="false"
              :on-success="handleCategoryAvatarSuccess">
              <img v-if="categoryForm.imageUrl" :src="categoryForm.imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCategory">确认</el-button>
      </div>
    </el-dialog>

    <!-- 新增商品对话框 -->
    <el-dialog title="新增商品" :visible.sync="productDialogVisible" width="650px">
      <el-form :model="productForm" label-width="100px">
        <el-form-item label="商品编号">
          <el-input v-model="productForm.productCode" placeholder="请输入商品编号"></el-input>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input v-model="productForm.productName" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        <el-form-item label="商品规格">
          <el-input v-model="productForm.specification" placeholder="请输入商品规格"></el-input>
        </el-form-item>
        <el-form-item label="商品价格">
          <el-input v-model="productForm.price" placeholder="请输入商品价格"></el-input>
        </el-form-item>
        <el-form-item label="商品单位">
          <el-input v-model="productForm.unit" placeholder="请输入商品单位"></el-input>
        </el-form-item>
        <el-form-item label="商品分类">
          <el-select v-model="productForm.categoryName" placeholder="请选择商品分类" style="width: 100%">
            <el-option label="食材类" value="食材类"></el-option>
            <el-option label="饮料类" value="饮料类"></el-option>
            <el-option label="零食类" value="零食类"></el-option>
            <el-option label="调味料" value="调味料"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品状态">
          <el-radio v-model="productForm.status" label="上架">上架</el-radio>
          <el-radio v-model="productForm.status" label="下架">下架</el-radio>
        </el-form-item>
        <el-form-item label="商品图片">
          <div class="upload-container">
            <el-upload
              class="avatar-uploader"
              action="/upload"
              :show-file-list="false"
              :on-success="handleProductAvatarSuccess">
              <img v-if="productForm.imageUrl" :src="productForm.imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="productDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProduct">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CommodityWarehouse',
  data() {
    return {
      title: '商品库',
      searchName: '',
      searchCode: '',
      categoryDialogVisible: false,
      productDialogVisible: false,
      currentPage: 1,
      total: 0,
      multipleSelection: [],
      categoryForm: {
        typeName: '',
        imageUrl: ''
      },
      productForm: {
        productCode: '',
        productName: '',
        specification: '',
        price: '',
        unit: '',
        categoryName: '',
        status: '上架',
        imageUrl: ''
      },
      categoryList: [
        { name: '食材类', count: 3 },
        { name: '饮料类', count: 2 },
        { name: '零食类', count: 4 },
        { name: '调味料', count: 1 }
      ],
      tableData: [
        {
          productCode: '0025',
          productName: '冬瓜芝麻',
          specification: '500g/袋',
          price: '25.00',
          unit: '袋',
          categoryName: '食材类',
          status: '上架',
          imageUrl: '/path/to/image1.jpg'
        },
        {
          productCode: '0023',
          productName: '配菜包子',
          specification: '500g/包',
          price: '30.00',
          unit: '包',
          categoryName: '食材类',
          status: '上架',
          imageUrl: '/path/to/image2.jpg'
        },
        {
          productCode: '0005',
          productName: '绿豆面',
          specification: '300ml/瓶',
          price: '15.00',
          unit: '瓶',
          categoryName: '食材类',
          status: '上架',
          imageUrl: '/path/to/image3.jpg'
        },
        {
          productCode: '0008',
          productName: '特制面',
          specification: '600ml/袋',
          price: '20.00',
          unit: '袋',
          categoryName: '食材类',
          status: '上架',
          imageUrl: '/path/to/image4.jpg'
        }
      ]
    }
  },
  methods: {
    search() {
      console.log('搜索关键词:', this.searchName, '编号:', this.searchCode)
      // 这里应该调用API进行搜索
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
      // 加载对应页的数据
    },
    showNewCategoryDialog() {
      this.categoryForm = {
        typeName: '',
        imageUrl: ''
      }
      this.categoryDialogVisible = true
    },
    batchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一条记录')
        return
      }
      this.$confirm('确认删除选中的记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 执行删除操作
        this.$message.success('删除成功')
      })
    },
    editProduct(row) {
      this.productForm = JSON.parse(JSON.stringify(row))
      this.productDialogVisible = true
    },
    toggleStatus(row) {
      const newStatus = row.status === '上架' ? '下架' : '上架'
      // 这里应该调用API修改状态
      row.status = newStatus
      this.$message.success(`状态修改为${newStatus}成功`)
    },
    deleteProduct(row) {
      this.$confirm('确认删除该商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 执行删除操作
        const index = this.tableData.findIndex(item => item.productCode === row.productCode)
        if (index !== -1) {
          this.tableData.splice(index, 1)
        }
        this.$message.success('删除成功')
      })
    },
    handleCategoryAvatarSuccess(res) {
      // 处理类型图片上传成功
      this.categoryForm.imageUrl = res.url
    },
    handleProductAvatarSuccess(res) {
      // 处理商品图片上传成功
      this.productForm.imageUrl = res.url
    },
    saveCategory() {
      // 保存商品类型
      if (!this.categoryForm.typeName) {
        this.$message.warning('请输入商品类型名称')
        return
      }
      // 这里应该调用API保存类型
      this.$message.success('保存成功')
      this.categoryDialogVisible = false
    },
    saveProduct() {
      // 保存商品信息
      if (!this.productForm.productName) {
        this.$message.warning('请输入商品名称')
        return
      }
      // 这里应该调用API保存商品
      if (this.productForm.productCode) {
        // 更新现有商品
        const index = this.tableData.findIndex(item => item.productCode === this.productForm.productCode)
        if (index !== -1) {
          this.tableData.splice(index, 1, {...this.productForm})
        }
      } else {
        // 添加新商品
        this.productForm.productCode = String(Math.floor(Math.random() * 9000) + 1000).padStart(4, '0')
        this.tableData.push({...this.productForm})
      }
      this.$message.success('保存成功')
      this.productDialogVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.commodity-warehouse {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.top-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  gap: 15px;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 180px;
}

.category-container {
  margin-bottom: 20px;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.category-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  width: 120px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.category-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 5px;
}

.category-name {
  font-size: 14px;
  margin-bottom: 5px;
}

.category-count {
  font-size: 12px;
  color: #909399;
}

.product-image img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

h1 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}

/deep/ .el-table {
  margin-bottom: 15px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 8px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-table--border, .el-table--group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

/deep/ .el-button--mini {
  padding: 5px 12px;
}

/deep/ .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
