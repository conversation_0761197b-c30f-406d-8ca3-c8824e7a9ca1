<template>
  <div>
    <el-form :model="temporary" class="top-container" @submit.native.prevent size="mini">
      <el-form-item>
        <span slot="label">关键字：</span>
        <el-input v-model="temporary.keyword" placeholder="输入关键字" maxlength="20"/>
      </el-form-item>
      <el-button type="primary" @click="handleFilter">查询</el-button>
      <el-button plain @click="handleReset">重置</el-button>
    </el-form>
    <Table
      :columns="columns"
      :loading="listLoading"
      :data="tableData"
      :pagination="listQuery"
      :total="total"
      :table-config="tableConfig"
      @getData="getDataList"
    >
      <el-table-column slot="company_name" label="公司名称" min-width="200" align="center">
        <div v-if="scope.row.company_name" slot-scope="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.company_name"
            placement="top"
          >
            <p
              class="line-clamp"
              @dblclick="copyTextFun(scope.row.company_name)"
            >
              {{ scope.row.company_name }}
            </p>
          </el-tooltip>
        </div>
        <div v-else><span>-</span></div>
      </el-table-column>
      <el-table-column slot="company_address" label="地址" min-width="200" align="center">
        <div v-if="scope.row.company_address" slot-scope="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.company_address"
            placement="top"
          >
            <p class="line-clamp">{{ scope.row.company_address }}</p>
          </el-tooltip>
        </div>
        <div v-else><span>-</span></div>
      </el-table-column>
      <el-table-column slot="tax_no" label="纳税人识别号" min-width="140" align="center">
        <div slot-scope="scope">
          {{ scope.row.tax_no }}
        </div>
      </el-table-column>
      <el-table-column slot="legal_represent" label="法人代表" min-width="140" align="center">
        <div slot-scope="scope">
          <div>{{ scope.row.legal_represent || "-" }}</div>
        </div>
      </el-table-column>
      <el-table-column slot="company_phone" label="联系电话" min-width="140" align="center">
        <div slot-scope="scope">
          <div>{{ scope.row.company_phone || "-" }}</div>
        </div>
      </el-table-column>
      <el-table-column
        slot="action"
        align="center"
        label="操作"
        min-width="140"
        class-name="action-class"
        fixed="right"
      >
        <div slot-scope="scope">
          <el-button type="text" :disabled="!scope.row.inventory_enabled" @click="lookStock(scope.row.company_id, scope.row.company_name)"
            >查看库存</el-button
          >
        </div>
      </el-table-column>
    </Table>
    <el-dialog
      :title="tableForm.company_name"
      width="800px"
      :visible.sync="dialogVisible"
    >
      <div>
        <el-dialog
          width="450px"
          title="库存信息"
          :visible.sync="innerVisible"
          append-to-body
        >
          <el-form
            ref="formDataRef"
            :model="formData"
            :rules="formDataRules"
            label-width="130px"
          >
            <el-form-item prop="product_name">
              <span slot="label">商品名称：</span>
              <el-input
                v-model.trim="formData.product_name"
                placeholder="输入商品名称"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item>
              <span slot="label">型号规格：</span>
              <el-input
                v-model.trim="formData.specification"
                placeholder="输入型号规格"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item prop="product_price">
              <span slot="label">单价：</span>
              <el-input
                v-model.trim="formData.product_price"
                placeholder="输入单价"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item>
              <span slot="label">单位：</span>
              <el-input
                v-model.trim="formData.unit"
                placeholder="输入单位"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item>
              <span slot="label">数量：</span>
              <el-input
                v-model.trim="formData.number"
                placeholder="输入数量"
                maxlength="20"
              />
            </el-form-item>
          </el-form>
          <div class="flex-row-center">
            <el-button @click="innerVisible = false">取消</el-button>
            <el-button type="primary" @click="submitData">保存</el-button>
          </div>
        </el-dialog>
        <el-button
          style="margin-bottom: 10px;margin-top: -10px;"
          class="button-full-wrapper"
          type="primary"
          size="mini"
          @click="handleData('add')"
        >
          <span class="button-txt">新增库存</span>
        </el-button>
        <el-table :data="tableForm.inventory_list" border style="width: 100%">
          <el-table-column prop="product_name" label="商品名称" align="center">
          </el-table-column>
          <el-table-column prop="specification" label="型号规格" align="center">
          </el-table-column>
          <el-table-column prop="product_price" label="单价" align="center"> </el-table-column>
          <el-table-column prop="unit" label="单位" align="center"> </el-table-column>
          <el-table-column prop="number" label="数量" align="center"></el-table-column>
          <el-table-column label="操作" width="150px" align="center">
            <div slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleData('edit', scope.row)"
                >修改</el-button
              >
              <el-button
                size="mini"
                @click="deleteData(scope.row)"
                type="danger"
                >删除</el-button
              >
            </div>
          </el-table-column>
        </el-table>
        <div style="text-align: right; margin-top: 15px">
          <el-pagination
            background
            prev-text="上一页"
            next-text="下一页"
            @current-change="handleCurrentChange"
            :current-page.sync="stockQuery.page"
            :page-size="stockQuery.page_size"
            layout="prev, pager, next, total"
            :total="count"
          >
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { requestApi } from "@/utils/request";
import { copyText } from "@/utils";
import Table from "@/components/Table.vue";

const defaultQuery = {
  keyword: null,
};
const defaultDataItem = {
  inventory_id: "",
  number: "",
  product_name: "",
  product_price: "",
  specification: "",
  state: 0,
  unit: "",
};
export default {
  name: "StockManagement",
  components: {
    Table,
  },
  data() {
    return {
      tableData: [], // 用户列表
      total: 0,
      listLoading: false, // Loading
      temporary: Object.assign({}, defaultQuery),
      listQuery: Object.assign(
        {
          page: 1,
          page_size: 10,
        },
        defaultQuery
      ),
      tableConfig: {
        height: 62,
        withPagination: true,
      },
      columns: [
        { hasIndex: true },
        { slot: "company_name" },
        { slot: "company_address" },
        { slot: "tax_no" },
        { slot: "legal_represent" },
        { slot: "company_phone" },
        { slot: "action" },
      ],
      copyTextFun: copyText,
      dialogVisible: false,
      innerVisible: false,
      tableForm: {
        company_id: "",
        company_name: "",
        company_short_name: "",
        inventory_list: [],
      },
      dialogType: "",
      company_id: null,
      formData: Object.assign({}, defaultDataItem),
      formDataRules: {
        product_name: [
          {
            required: true,
            message: "此项不能为空",
            trigger: ["blur", "change"],
          },
        ],
        product_price: [
          {
            required: true,
            message: "此项不能为空",
            trigger: ["blur", "change"],
          },
        ],
      },
      stockQuery: {
        page: 1,
        page_size: 10,
      },
      count: 0,
    };
  },
  created() {
    this.getDataList();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (
        (from.name === "EnterpriseDetailAdd" ||
          from.name === "EnterpriseDetailEdit") &&
        vm.$route.query.update === "1"
      ) {
        vm.$router.replace({
          ...to,
          query: {},
        });
        vm.handleReset();
      }
    });
  },
  methods: {
    // 重置筛选
    handleReset() {
      this.temporary = Object.assign({}, defaultQuery);
      this.listQuery = Object.assign(
        {
          page: 1,
          page_size: 10,
        },
        defaultQuery
      );
      this.getDataList();
    },

    // 搜索功能
    handleFilter() {
      this.listQuery = Object.assign(this.listQuery, this.temporary);
      this.listQuery.page = 1;
      this.getDataList();
    },

    // 获取列表
    getDataList() {
      this.listLoading = true;
      requestApi({
        name: "getCompanyList",
        data: this.listQuery,
      }).then((res) => {
        this.tableData = res.results || [];
        this.total = res.count;
        setTimeout(() => {
          this.listLoading = false;
        }, 500)
      });
    },
    // 查看库存
    lookStock(id, name) {
      this.tableForm.company_name = name
      this.company_id = id;
      requestApi({
        name: "getInventoryList",
        data: {
          page: this.stockQuery.page,
          page_size: this.stockQuery.page_size,
          company_id: this.company_id,
        },
      }).then((res) => {
        if (res.code === 200) {
          this.tableForm.company_id = res.data.company_id;
          this.tableForm.company_name = res.data.company_name;
          this.tableForm.company_short_name = res.data.company_short_name;
          this.tableForm.inventory_list = res.data.inventory_list || [];
          this.count = res.count;
          this.dialogVisible = true;
        }
      });
    },
    // 添加编辑
    handleData(type, row) {
      this.innerVisible = true;
      // 新增编辑
      this.dialogType = type;
      if (type === "edit") {
        this.formData = Object.assign({}, row);
      } else {
        this.formData = Object.assign({}, defaultDataItem);
      }
      this.$nextTick(() => {
        this.$refs.formDataRef.clearValidate();
      });
    },
    // 提交结果
    submitData() {
      this.$refs["formDataRef"].validate((valid) => {
        if (valid) {
          let data = this.formData;
          data.company_id = this.company_id;
          requestApi({
            name: "postProductManage",
            data,
          }).then((res) => {
            if (res.code === 200) {
              this.innerVisible = false;
              this.$message.success("保存成功");
              this.lookStock(this.company_id);
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },
    // 删除库存
    deleteData(row) {
      let data = row;
      data.state = 1;
      data.company_id = this.company_id;
      requestApi({
        name: "postProductManage",
        data,
      }).then((res) => {
        if (res.code === 200) {
          this.innerVisible = false;
          this.$message.success("删除成功");
          this.lookStock(this.company_id);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleCurrentChange(page) {
      this.stockQuery.page = page;
      this.lookStock(this.company_id);
    },
  },
};
</script>
