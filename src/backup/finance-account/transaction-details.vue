<template>
  <div class="transaction-details">
    <div class="page-header">
      <i class="el-icon-tickets"></i> 交易明细
    </div>
    
    <div class="action-container">
      <el-button type="success" icon="el-icon-download" @click="exportData">导出明细</el-button>
    </div>
    
    <el-table
      :data="transactionList"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
    >
      <el-table-column
        prop="index"
        label="序号"
        width="60"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="accountId"
        label="账户ID"
        width="130"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="accountName"
        label="账户名称"
        min-width="160"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="amount"
        label="发生额"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span :class="scope.row.amount.startsWith('-') ? 'negative-amount' : 'positive-amount'">
            {{ scope.row.amount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="companyBalance"
        label="企业账户余额"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="rewardBalance"
        label="赠送账户余额"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="wechatPayment"
        label="微信支付"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="productService"
        label="产品服务"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="productDetails"
        label="产品服务详情"
        min-width="180"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="operator"
        label="操作人"
        width="100"
        align="center"
      ></el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TransactionDetails',
  data() {
    return {
      title: '交易明细',
      listLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 13,
      transactionList: [
        {
          index: 1,
          accountId: '**********',
          accountName: '广州市xxx有限公司',
          amount: '-50',
          companyBalance: '',
          rewardBalance: '0',
          wechatPayment: '',
          productService: '账户退款',
          productDetails: '赠送账户自动清零',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 2,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '-1525',
          companyBalance: '0',
          rewardBalance: '',
          wechatPayment: '',
          productService: '账户退款',
          productDetails: '退款成功',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 3,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '+25',
          companyBalance: '1525',
          rewardBalance: '',
          wechatPayment: '',
          productService: '自助餐',
          productDetails: '取消报餐',
          createTime: '2025-02-19 12:15:10',
          operator: '李四'
        },
        {
          index: 4,
          accountId: '**********',
          accountName: '广州市xxx有限公司',
          amount: '-500',
          companyBalance: '',
          rewardBalance: '50',
          wechatPayment: '',
          productService: '消费兑换',
          productDetails: '消费已满10000元，赠送账户提现500元',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 5,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '+500',
          companyBalance: '1500',
          rewardBalance: '',
          wechatPayment: '',
          productService: '消费兑换',
          productDetails: '消费已满10000元，赠送账户提现500元',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 6,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '-8000',
          companyBalance: '1000',
          rewardBalance: '',
          wechatPayment: '',
          productService: '菜品点餐',
          productDetails: '下单成功',
          createTime: '2025-02-19 12:15:10',
          operator: '王五'
        },
        {
          index: 7,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '-1950',
          companyBalance: '9000',
          rewardBalance: '',
          wechatPayment: '',
          productService: '商务餐',
          productDetails: '下单成功',
          createTime: '2025-02-19 12:15:10',
          operator: '李四'
        },
        {
          index: 8,
          accountId: '**********',
          accountName: '广州市xxx有限公司',
          amount: '+50',
          companyBalance: '',
          rewardBalance: '550',
          wechatPayment: '',
          productService: '充值赠送',
          productDetails: '充值1000元，赠送50元',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 9,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '+1000',
          companyBalance: '10950',
          rewardBalance: '',
          wechatPayment: '',
          productService: '账户充值',
          productDetails: '充值成功',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 10,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '-25',
          companyBalance: '9950',
          rewardBalance: '',
          wechatPayment: '',
          productService: '自助餐',
          productDetails: '下单成功',
          createTime: '2025-02-19 12:15:10',
          operator: '王五'
        },
        {
          index: 11,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '-25',
          companyBalance: '9975',
          rewardBalance: '',
          wechatPayment: '',
          productService: '自助餐',
          productDetails: '下单成功',
          createTime: '2025-02-19 12:15:10',
          operator: '李四'
        },
        {
          index: 12,
          accountId: '**********',
          accountName: '广州市xxx有限公司',
          amount: '+500',
          companyBalance: '',
          rewardBalance: '500',
          wechatPayment: '',
          productService: '充值赠送',
          productDetails: '充值10000元，赠送500元',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        },
        {
          index: 13,
          accountId: '666',
          accountName: '广州市xxx有限公司',
          amount: '+10000',
          companyBalance: '10000',
          rewardBalance: '',
          wechatPayment: '',
          productService: '账户充值',
          productDetails: '充值成功',
          createTime: '2025-02-19 12:15:10',
          operator: '系统管理员'
        }
      ]
    }
  },
  methods: {
    // 导出数据
    exportData() {
      this.$message({
        message: '交易明细导出成功',
        type: 'success'
      });
    },
    
    // 处理页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  }
}
</script>

<style lang="less" scoped>
.transaction-details {
  background-color: #fff;
  padding: 20px;
  
  .page-header {
    font-size: 18px;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
  
  .action-container {
    margin-bottom: 15px;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .positive-amount {
    color: #67C23A;
  }
  
  .negative-amount {
    color: #F56C6C;
  }
}
</style>