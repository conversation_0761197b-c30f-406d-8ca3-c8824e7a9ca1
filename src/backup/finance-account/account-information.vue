<template>
  <div class="account-information">
    <h1>账户信息</h1>
    
    <!-- 公司账户信息卡片 -->
    <div class="account-card">
      <div class="card-header">
        <i class="el-icon-wallet"></i> 公司账户信息
        <span class="note">注: 已消费金额的统计，以登记的时间金额计，会自动根据允许对热点的调整余额，创企业账户。</span>
      </div>
      
      <div class="account-balance-container">
        <div class="balance-item">
          <div class="balance-header">
            <span>ID: {{ accountInfo.id }}</span>
          </div>
          <div class="balance-content">
            <div class="balance-label">企业账户余额</div>
            <div class="balance-value">
              <i class="el-icon-money"></i> {{ accountInfo.balance }} 元
            </div>
          </div>
        </div>
        
        <div class="balance-item">
          <div class="balance-header">
            <span>ID: {{ accountInfo.rewardId }}</span>
          </div>
          <div class="balance-content">
            <div class="balance-label">赠送账户余额</div>
            <div class="balance-value">
              <i class="el-icon-money"></i> {{ accountInfo.rewardBalance }} 元
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 预付金额明细 -->
    <div class="prepay-details">
      <div class="card-header">
        <i class="el-icon-document"></i> 预付金额明细
      </div>
      
      <div class="filter-container">
        <el-button type="success" icon="el-icon-plus" @click="handleAddPrepay">快速预付</el-button>
        
        <div class="filter-form">
          <el-form :inline="true" :model="filterForm" @submit.native.prevent>
            <el-form-item label="姓名:">
              <el-input v-model="filterForm.name" placeholder="输入姓名" clearable></el-input>
            </el-form-item>
            <el-form-item label="手机:">
              <el-select v-model="filterForm.phone" placeholder="选择手机号" filterable clearable>
                <el-option
                  v-for="item in phoneOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form>
        </div>
      </div>
      
      <el-table
        :data="prepayList"
        border
        style="width: 100%"
        v-loading="listLoading"
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="phone"
          label="手机"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="workerSubsidy"
          label="预付员工补助餐"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="businessService"
          label="预付企业服务餐"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="businessImportant"
          label="预付企业重点餐"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          label="操作"
          width="120"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button 
              type="text" 
              class="primary-text" 
              @click="viewPrepayDetail(scope.row)">
              预付详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
    
    <!-- 预付详情对话框 -->
    <el-dialog title="预付详情" :visible.sync="detailDialogVisible" width="90%" class="detail-dialog">
      <div class="detail-header">
        <div class="user-info">
          <span>姓名: {{ selectedUser.name }}</span>
          <span>手机号码: {{ selectedUser.phone }}</span>
        </div>
      </div>
      
      <el-table
        :data="prepayDetailList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="接收人员"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="phone"
          label="手机"
          width="110"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="orderType"
          label="订单类型"
          width="110"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="mealTime"
          label="就餐类别"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="people"
          label="就餐人数"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="amount"
          label="金额合计"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="payType"
          label="预购种类"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="payTime"
          label="支付时间"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="useFor"
          label="用途说明"
          min-width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="status"
          label="就餐状态"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          label="操作"
          width="150"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button 
              type="text" 
              class="danger-text" 
              @click="handleCancel(scope.row)">
              未就餐
            </el-button>
            <el-button 
              type="text" 
              class="warning-text" 
              @click="handleRefund(scope.row)">
              申请退款
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          :current-page="detailCurrentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="detailPageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="detailTotal">
        </el-pagination>
      </div>
    </el-dialog>
    
    <!-- 添加预付对话框 -->
    <el-dialog title="快速预付" :visible.sync="addDialogVisible" width="500px">
      <el-form :model="prepayForm" :rules="prepayRules" ref="prepayForm" label-width="120px">
        <el-form-item label="员工姓名:" prop="name">
          <el-select v-model="prepayForm.userId" placeholder="请选择员工" filterable>
            <el-option
              v-for="item in employeeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预付类型:" prop="type">
          <el-select v-model="prepayForm.type" placeholder="请选择预付类型">
            <el-option label="员工补助餐" value="workerSubsidy"></el-option>
            <el-option label="企业服务餐" value="businessService"></el-option>
            <el-option label="企业重点餐" value="businessImportant"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预付金额:" prop="amount">
          <el-input-number v-model="prepayForm.amount" :min="1" :max="10000" label="预付金额"></el-input-number>
        </el-form-item>
        <el-form-item label="人数:" prop="people">
          <el-input-number v-model="prepayForm.people" :min="1" :max="100" label="人数"></el-input-number>
        </el-form-item>
        <el-form-item label="餐点类型:" prop="mealType">
          <el-select v-model="prepayForm.mealType" placeholder="请选择餐点类型">
            <el-option label="午餐" value="lunch"></el-option>
            <el-option label="晚餐" value="dinner"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用途说明:" prop="useFor">
          <el-input v-model="prepayForm.useFor" placeholder="请输入用途说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPrepay">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AccountInformation',
  data() {
    return {
      title: '账户信息',
      // 账户信息
      accountInfo: {
        id: '666',
        balance: '4200',
        rewardId: '**********',
        rewardBalance: '200'
      },
      
      // 筛选表单
      filterForm: {
        name: '',
        phone: ''
      },
      
      // 手机号选项
      phoneOptions: [
        {
          value: '159xxxx9584',
          label: '159xxxx9584'
        },
        {
          value: '158xxxx1234',
          label: '158xxxx1234'
        },
        {
          value: '150xxxx1234',
          label: '150xxxx1234'
        },
        {
          value: '151xxxx1234',
          label: '151xxxx1234'
        }
      ],
      
      // 员工选项
      employeeOptions: [
        {
          value: '1',
          label: '李四 (158xxxx1234)'
        },
        {
          value: '2',
          label: '王五 (159xxxx9584)'
        },
        {
          value: '3',
          label: '赵六 (150xxxx1234)'
        },
        {
          value: '4',
          label: '钱七 (151xxxx1234)'
        }
      ],
      
      // 预付列表数据
      prepayList: [
        {
          index: 1,
          name: '李四',
          phone: '158xxxx1234',
          workerSubsidy: 100,
          businessService: 286,
          businessImportant: 150
        },
        {
          index: 2,
          name: '王五',
          phone: '159xxxx9584',
          workerSubsidy: 50,
          businessService: 0,
          businessImportant: 0
        },
        {
          index: 3,
          name: '赵六',
          phone: '150xxxx1234',
          workerSubsidy: 200,
          businessService: 0,
          businessImportant: 0
        },
        {
          index: 4,
          name: '钱七',
          phone: '151xxxx1234',
          workerSubsidy: 125,
          businessService: 0,
          businessImportant: 0
        }
      ],
      
      // 预付详情数据
      selectedUser: {
        name: '李四',
        phone: '158xxxx1234'
      },
      prepayDetailList: [
        {
          index: 1,
          name: '李四',
          phone: '158xxxx1234',
          orderType: '员工补助餐',
          createTime: '2025-03-01 11:45-12:00',
          mealTime: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-01 11:45:10',
          useFor: '正常用餐',
          status: '未就餐'
        },
        {
          index: 2,
          name: '李四',
          phone: '158xxxx1234',
          orderType: '员工补助餐',
          createTime: '2025-03-02 11:45-12:00',
          mealTime: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-02 11:45:10',
          useFor: '正常用餐',
          status: '未就餐'
        },
        {
          index: 3,
          name: '李四',
          phone: '158xxxx1234',
          orderType: '员工补助餐',
          createTime: '2025-03-03 11:45-12:00',
          mealTime: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-02-03 11:45:10',
          useFor: '正常用餐',
          status: '未就餐'
        },
        {
          index: 4,
          name: '李四',
          phone: '158xxxx1234',
          orderType: '员工补助餐',
          createTime: '2025-03-04 11:45-12:00',
          mealTime: '午餐',
          people: 1,
          amount: 25,
          payType: '账号预付',
          payTime: '2025-03-04 11:45:10',
          useFor: '正常用餐',
          status: '未就餐'
        },
        {
          index: 5,
          name: '李四',
          phone: '158xxxx1234',
          orderType: '企业服务餐',
          createTime: '2025-03-02 11:45-12:00',
          mealTime: '午餐',
          people: 4,
          amount: 286,
          payType: '账号预付',
          payTime: '2025-03-05 11:45:10',
          useFor: '正常用餐',
          status: '未就餐'
        },
        {
          index: 6,
          name: '李四',
          phone: '158xxxx1234',
          orderType: '企业重点餐',
          createTime: '2025-03-02 11:45-12:00',
          mealTime: '午餐',
          people: 3,
          amount: 150,
          payType: '账号预付',
          payTime: '2025-02-28 11:45:25',
          useFor: '客户招待用餐',
          status: '已'
        }
      ],
      
      // 表格加载状态
      listLoading: false,
      
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 4,
      
      // 详情分页
      detailCurrentPage: 1,
      detailPageSize: 10,
      detailTotal: 6,
      
      // 对话框
      detailDialogVisible: false,
      addDialogVisible: false,
      
      // 选中的行
      multipleSelection: [],
      
      // 预付表单
      prepayForm: {
        userId: '',
        type: '',
        amount: 25,
        people: 1,
        mealType: 'lunch',
        useFor: '正常用餐'
      },
      
      // 表单规则
      prepayRules: {
        userId: [
          { required: true, message: '请选择员工', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择预付类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入预付金额', trigger: 'blur' }
        ],
        people: [
          { required: true, message: '请输入人数', trigger: 'blur' }
        ],
        mealType: [
          { required: true, message: '请选择餐点类型', trigger: 'change' }
        ]
      }
    }
  },
  
  methods: {
    // 处理筛选
    handleFilter() {
      this.currentPage = 1;
      this.getPrepayList();
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        name: '',
        phone: ''
      };
      this.handleFilter();
    },
    
    // 获取预付列表
    getPrepayList() {
      this.listLoading = true;
      
      // 模拟API请求
      setTimeout(() => {
        // 实际应用中应该调用接口
        const filteredList = this.prepayList.filter(item => {
          const nameMatch = !this.filterForm.name || item.name.includes(this.filterForm.name);
          const phoneMatch = !this.filterForm.phone || item.phone === this.filterForm.phone;
          return nameMatch && phoneMatch;
        });
        
        this.total = filteredList.length;
        this.listLoading = false;
      }, 500);
    },
    
    // 页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPrepayList();
    },
    
    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getPrepayList();
    },
    
    // 详情页码大小变化
    handleDetailSizeChange(val) {
      this.detailPageSize = val;
    },
    
    // 详情页码变化
    handleDetailCurrentChange(val) {
      this.detailCurrentPage = val;
    },
    
    // 查看预付详情
    viewPrepayDetail(row) {
      this.selectedUser = {
        name: row.name,
        phone: row.phone
      };
      this.detailDialogVisible = true;
    },
    
    // 选择变化
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    
    // 未就餐处理
    handleCancel(row) {
      this.$confirm('确定标记为未就餐吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '已标记为未就餐'
        });
      }).catch(() => {});
    },
    
    // 申请退款
    handleRefund(row) {
      this.$confirm('确定申请退款吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '退款申请已提交'
        });
      }).catch(() => {});
    },
    
    // 打开添加预付对话框
    handleAddPrepay() {
      this.prepayForm = {
        userId: '',
        type: '',
        amount: 25,
        people: 1,
        mealType: 'lunch',
        useFor: '正常用餐'
      };
      this.addDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.prepayForm && this.$refs.prepayForm.clearValidate();
      });
    },
    
    // 提交预付
    submitPrepay() {
      this.$refs.prepayForm.validate(valid => {
        if (valid) {
          // 实际应用中应该调用接口
          this.$message({
            type: 'success',
            message: '预付创建成功'
          });
          this.addDialogVisible = false;
        }
      });
    }
  },
  
  created() {
    this.getPrepayList();
  }
}
</script>

<style lang="less" scoped>
.account-information {
  background-color: #fff;
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 20px;
    color: #303133;
  }
  
  .account-card, .prepay-details {
    margin-bottom: 25px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
  
  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #EBEEF5;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
    
    .note {
      margin-left: 20px;
      font-size: 12px;
      font-weight: normal;
      color: #F56C6C;
    }
  }
  
  .account-balance-container {
    display: flex;
    padding: 20px;
    
    .balance-item {
      flex: 1;
      background-color: #EEF5FF;
      border-radius: 4px;
      padding: 15px;
      margin: 0 10px;
      
      &:first-child {
        margin-left: 0;
      }
      
      &:last-child {
        margin-right: 0;
      }
      
      .balance-header {
        color: #606266;
        font-size: 13px;
        margin-bottom: 15px;
      }
      
      .balance-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .balance-label {
          font-size: 16px;
          color: #303133;
        }
        
        .balance-value {
          font-size: 20px;
          font-weight: bold;
          color: #F56C6C;
          
          i {
            margin-right: 5px;
          }
        }
      }
    }
  }
  
  .filter-container {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .filter-form {
      flex: 1;
      margin-left: 20px;
    }
  }
  
  .pagination-container {
    padding: 15px 20px;
    text-align: right;
  }
  
  .detail-header {
    margin-bottom: 15px;
    
    .user-info {
      display: flex;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      span {
        margin-right: 20px;
        font-weight: bold;
      }
    }
  }
  
  .primary-text {
    color: #409EFF;
  }
  
  .danger-text {
    color: #F56C6C;
  }
  
  .warning-text {
    color: #E6A23C;
  }
  
  /deep/ .el-select {
    width: 180px;
  }
  
  /deep/ .detail-dialog .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>