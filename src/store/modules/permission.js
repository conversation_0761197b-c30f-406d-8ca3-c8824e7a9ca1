import {
  asyncRoutes,
  constantRoutes
} from '@/router'
// import Layout from '@/layout'

/**
 * 通过meta.role判断是否与当前用户权限匹配
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.role) {
    return roles.some(role => route.meta.role.indexOf(role) >= 0)
  } else {
    return true
  }
}

/**
 * 通过权限码判断是否有权限
 * @param permissions 用户权限码数组
 * @param route 路由对象
 */
function hasPermissionCode(permissions, route) {
  if (route.meta && route.meta.permission) {
    return permissions.some(permission => route.meta.permission.indexOf(permission) >= 0)
  } else {
    return true
  }
}

/**
 * 综合权限判断：角色权限 + 权限码权限
 * @param userPermissions 用户权限信息 {roles: [], permissions: []}
 * @param route 路由对象
 */
function hasRoutePermission(userPermissions, route) {
  const { roles = [], permissions = [] } = userPermissions
  
  // 如果路由没有配置权限，默认允许访问
  if (!route.meta || (!route.meta.role && !route.meta.permission)) {
    return true
  }
  
  // 角色权限检查
  const hasRolePermission = hasPermission(roles, route)
  
  // 权限码检查
  const hasCodePermission = hasPermissionCode(permissions, route)
  
  // 同时满足角色权限和权限码权限
  return hasRolePermission && hasCodePermission
}

/**
 * 递归过滤异步路由表，返回符合用户权限的路由表
 * @param routes asyncRoutes
 * @param userPermissions 用户权限信息
 */
export function filterAsyncRoutes(routes, userPermissions) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    
    if (hasRoutePermission(userPermissions, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, userPermissions)
        // 如果所有子路由都被过滤掉了，且父路由没有自己的组件，则不显示父路由
        if (tmp.children.length === 0 && !tmp.component) {
          return
        }
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * 生成菜单数据，用于侧边栏显示
 * @param routes 路由数组
 */
export function generateMenus(routes) {
  const menus = []
  
  routes.forEach(route => {
    // 跳过隐藏的路由
    if (route.hidden) {
      return
    }
    
    const menu = {
      path: route.path,
      name: route.name,
      meta: route.meta,
      children: []
    }
    
    if (route.children && route.children.length > 0) {
      menu.children = generateMenus(route.children)
    }
    
    menus.push(menu)
  })
  
  return menus
}

// export function generaMenu(routes, data) {
//   console.log(data)
//   data.forEach((item) => {
//     const menu = {
//       path: item.route_url,
//       component:
//         item.component_path === 'Layout'
//           ? Layout
//           : loadView(item.component_path),
//       hidden: item.visiable === 0,
//       // children: [],
//       name: item.route_name,
//       meta: {
//         title: item.name,
//         icon: item.icon,
//         noCache: !item.cacheable,
//         permission: item.permission
//       }
//     }
//     if (item.children && item.children.length > 0) {
//       if (item.type === 1 && item.children[0] && item.children[0].visiable !== 0 && item.children[0].route_url) {
//         // 目录重定向到第一个不隐藏的子菜单
//         menu.redirect = item.children[0].route_url
//       }
//       menu.children = menu.children || []
//       generaMenu(menu.children, item.children)
//     }
//     if (item.route_url) {
//       routes.push(menu)
//     }
//   })
// }

// export const loadView = (view) => {
//   // 路由懒加载
//   return (resolve) => require([`@/views${view}`], resolve)
// }

const permission = {
  namespaced: true,
  state: {
    routers: [],
    addRouters: [],
    menus: []
  },
  mutations: {
    SET_ROUTERS: (state, routes) => {
      state.addRouters = routes
      state.routers = constantRoutes.concat(routes)
    },
    SET_MENUS: (state, menus) => {
      state.menus = menus
    }
  },
  actions: {
    generateRoutes({ commit }, userPermissions) {
      return new Promise(resolve => {
        let accessedRoutes = []
        
        // 兼容性处理：如果传入的是数组（旧的roles格式），转换为新格式
        if (Array.isArray(userPermissions)) {
          userPermissions = {
            roles: userPermissions,
            permissions: []
          }
        }
        
        // 如果是超级管理员，拥有所有权限
        if (userPermissions.roles && userPermissions.roles.includes('super_admin')) {
          accessedRoutes = asyncRoutes || []
        } else {
          // 根据用户权限过滤路由
          accessedRoutes = filterAsyncRoutes(asyncRoutes, userPermissions)
        }
        
        // 生成菜单数据
        const menus = generateMenus(accessedRoutes)
        
        commit('SET_ROUTERS', accessedRoutes)
        commit('SET_MENUS', menus)
        resolve(accessedRoutes)
      })
    }
  }
}

export default permission
