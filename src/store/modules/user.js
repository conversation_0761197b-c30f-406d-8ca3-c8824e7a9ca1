import { getToken, setToken, removeToken, getTypes, setTypes, removeTypes } from '@/utils/auth'
import { resetRouter } from '@/router'
import { requestApi } from '@/utils/request'

const getDefaultState = () => {
  return {
    token: getToken(), // 用户:1   业务员:2  管理员:3
    types: getTypes(),
    username: localStorage.getItem('userInfo') && JSON.parse(localStorage.getItem('userInfo')) || {},
    userInfo: '',
    avatar: '',
    roles: [],
    permissions: []
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    localStorage.removeItem('username')
    localStorage.removeItem('userInfo')
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_TYPES: (state, types) => {
    state.types = types
  },
  SET_NAME: (state, username) => {
    state.username = username
    localStorage.setItem('username', username)
  },
  SET_USER_DATA: (state, data) => {
    state.userInfo = data
    localStorage.setItem('userInfo', JSON.stringify(data))
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  }
}

const actions = {
  login({ commit }, userInfo) {
    const { phone, valid_code, company_id, company_type } = userInfo
    return new Promise((resolve, reject) => {
      requestApi({
        name: 'login',
        data: {
          phone: phone.trim(),
          valid_code,
          company_id,
          company_type
        }
      }).then((response) => {
        if (response.code === 200) {
          const { data } = response
          commit('SET_TOKEN', data.access_token)
          commit('SET_TYPES', data.types)
          setToken(data.access_token)
          setTypes(data.types)
          resolve(response)
        } else {
          reject(response)
        }
      })
      .catch((error) => {
        reject(error)
      })
    })
  },
  passLogin({ commit }, userInfo) {
    const { phone, passwd, company_id, company_type } = userInfo
    return new Promise((resolve, reject) => {
      requestApi({
        name: 'passLogin',
        data: {
          phone: phone.trim(),
          passwd,
          company_id,
          company_type
        }
      }).then((response) => {
        if (response.code === 200) {
          const { data } = response
          commit('SET_TOKEN', data.access_token)
          commit('SET_TYPES', data.types)
          setToken(data.access_token)
          setTypes(data.types)
          resolve(response)
        } else {
          reject(response)
        }
      })
      .catch((error) => {
        reject(error)
      })
    })
  },

  authLogin({ commit }, userInfo) {
    const { username, password, company_id, company_type } = userInfo
    return new Promise((resolve, reject) => {
      // 使用x-www-form-urlencoded格式
      const params = new URLSearchParams()
      params.append('username', username.trim())
      params.append('password', password)
      params.append('company_id', company_id)
      params.append('company_type', company_type)
      console.log("nn")
      requestApi({
        name: 'authLogin',
        data: params,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then((response) => {
        console.log("xx")
        if (response.code === 200) {
          console.log("yy")

          const { data } = response
          console.log("data")
          console.log(data)
          commit('SET_TOKEN', data.access_token)
          commit('SET_TYPES', data.types)
          console.log("zz")

          setToken(data.access_token)
          setTypes(data.types)
          console.log("aa")

          resolve(response)
        } else {
          console.log("bb")

          reject(response)
        }
      })
        .catch((error) => {
          console.log("cc")

          reject(error)
        })
    })
  },

  // get user info
  getUserInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      requestApi({
        name: 'getUserInfo',
      })
        .then((response) => {
          const { user_name, types, permissions = [] } = response.data
          let role = [[''], ['user'], ['salesman'], ['admin'], ['super_admin']]
          // process.env.NODE_ENV === 'development'
          let roles = role[types]
          
          // 存储角色和权限信息
          commit('SET_ROLES', roles)
          commit('SET_PERMISSIONS', permissions)
          commit('SET_NAME', user_name)
          commit('SET_USER_DATA', response.data)
          
          // 返回完整的权限信息给路由权限模块使用
          const userPermissions = {
            roles: roles,
            permissions: permissions
          }
          
          resolve(userPermissions)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      // requestApi({
      //   name: 'logout'
      // })
      //   .then(() => {
      removeToken() // must remove token first
      removeTypes() // also remove types
      // resetRouter()
      commit('RESET_STATE')
      resolve()
      // })
      // .catch((error) => {
      //   reject(error)
      // })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove token first
      removeTypes() // also remove types
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
