import Vue from 'vue'
import SvgIcon from '@/components/SvgIcon'// svg component

// register globally
Vue.component('svg-icon', SvgIcon)
Vue.component('svg-icon-seleted', SvgIcon) // 此组件用于供用户选择

const req = require.context('./svg', false, /\.svg$/)
const reqSeleted = require.context('./svgIcons', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(req)
requireAll(reqSeleted)
