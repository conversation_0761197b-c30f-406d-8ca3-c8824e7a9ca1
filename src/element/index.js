import {
  Loading,
  MessageBox,
  Tag,
  Select,
  Option,
  OptionGroup,
  Input,
  Tree,
  Dialog,
  Row,
  Col,
  Breadcrumb,
  BreadcrumbItem,
  Tooltip,
  Button,
  Pagination,
  Form,
  FormItem,
  Upload,
  Radio,
  RadioGroup,
  RadioButton,
  Cascader,
  Checkbox,
  CheckboxButton,
  CheckboxGroup,
  Autocomplete,
  Message,
  Notification,
  Timeline,
  TimelineItem,
  Collapse,
  CollapseItem,
  Popover,
  Slider,
  Drawer
} from 'element-ui'
const element = {
  install: function(Vue) {
    Vue.use(Loading.directive)
    Vue.prototype.loading = Loading.service
    Vue.prototype.$msgbox = MessageBox
    Vue.prototype.$confirm = MessageBox.confirm
    Vue.prototype.$message = Message
    Vue.prototype.$notify = Notification
    Vue.use(Tag)
    Vue.use(Select)
    Vue.use(Option)
    Vue.use(OptionGroup)
    Vue.use(Input)
    Vue.use(Tree)
    Vue.use(Dialog)
    Vue.use(Row)
    Vue.use(Col)
    Vue.use(Breadcrumb)
    Vue.use(BreadcrumbItem)
    Vue.use(Tooltip)
    Vue.use(Button)
    Vue.use(Pagination)
    Vue.use(Form)
    Vue.use(FormItem)
    Vue.use(Upload)
    Vue.use(Radio)
    Vue.use(RadioGroup)
    Vue.use(RadioButton)
    Vue.use(Cascader)
    Vue.use(Checkbox)
    Vue.use(CheckboxButton)
    Vue.use(CheckboxGroup)
    Vue.use(Autocomplete)
    Vue.use(Timeline)
    Vue.use(TimelineItem)
    Vue.use(Collapse)
    Vue.use(CollapseItem)
    Vue.use(Popover)
    Vue.use(Slider)
    Vue.use(Drawer)
  }
}
export default element
