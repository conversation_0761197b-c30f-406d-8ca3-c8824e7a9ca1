<template>
  <el-dialog
    custom-class="img-dialog"
    :width="imageWidth + 'px'"
    :visible.sync="posterPreviewVisible"
    append-to-body
    :show-close="false"
    @close="closeDialog"
  >
    <div class="img-con">
      <span class="close" @click="closeDialog"><i class="el-icon-circle-close" /></span>
      <img :src="imageUrl" alt="">
    </div>
  </el-dialog>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialogVisible: Boolean,
    imageUrl: String,
    imageWidth: {
      type: String,
      default: '500'
    }
  },
  data() {
    return {
      posterPreviewVisible: false
    }
  },
  watch: {
    dialogVisible(val) {
      this.posterPreviewVisible = val
    }
  },
  created() {
  },
  methods: {
    closeDialog() {
      this.posterPreviewVisible = false
      this.$emit('closeDialog', false)
    }
  }
}
</script>
<style lang="less" scoped>
 .el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style lang="less">

.img-dialog {
  background: transparent !important;
  box-shadow:none;
  margin: auto!important;
  margin-top: 0 !important;

  .el-dialog__header{
    display: none;
  }
  .el-dialog__body {
    padding: 20px;
    position: relative;
    // top: -50px;
  }

  .img-con{
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    img{
       width: 100%;
    }
  }

  .close {
    position: absolute;
    right: 0;
    top: -35px;
    .el-icon-circle-close {
      color:#fff;
      z-index: 9;
      cursor: pointer;
      display: inline-block;
      font-size: 30px;
      text-align: center;
      top: 0;
      &:hover {
        animation: close .15s linear;
      }
    }
  }
  @keyframes close {
    0%{
      transform:rotate(0deg)
    }
    25%{
      transform:rotate(60deg)
    }
    50% {
      transform:rotate(90deg)
    }
    75% {
      transform:rotate(120deg)
    }
    100% {
      transform:rotate(180deg)
    }
  }
}

@media screen and(max-width: 600px) {
  .img-dialog {
    width: 90%!important;
    .el-dialog__body {
      padding: 14px;
      top: 0;
    }
  }
}

</style>
