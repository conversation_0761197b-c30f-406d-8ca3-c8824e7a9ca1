<template>
  <div class="clearfix">
    <div :class="{ hidden: hidden }" class="pagination-container">
      <el-pagination
        :background="background"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="layout"
        :page-sizes="pageSizes"
        :total="total"
        v-bind="$attrs"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { scrollTo } from "@/utils/scrollTo";

export default {
  name: "Pagination",
  props: {
    total: {
      required: true,
      type: Number,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 20,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50];
      },
    },
    layout: {
      type: String,
      default: " prev, pager, next,sizes, jumper, ->, total",
    },
    background: {
      type: Boolean,
      default: true,
    },
    autoScroll: {
      type: Boolean,
      default: true,
    },
    hidden: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit("update:page", val);
      },
    },
    pageSize: {
      get() {
        return this.limit;
      },
      set(val) {
        this.$emit("update:limit", val);
      },
    },
  },
  methods: {
    handleSizeChange(val) {
      console.log(val);
      this.$emit("pagination", { page: this.currentPage, limit: val });
      if (this.autoScroll) {
        scrollTo(0, 800);
      }
    },
    handleCurrentChange(val) {
      this.$emit("pagination", { page: val, limit: this.pageSize });
      if (this.autoScroll) {
        scrollTo(0, 800);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.pagination-container {
  background: transparent;
  padding: 10px 15px;
  // min-width: 800px;
  // width: 400px;
  float: right;
  /deep/.el-pagination.is-background .el-pager li {
    background-color: #fff !important;
    color: #07090d;
    font-weight: 400;
    border: 1px solid #d9d9d9;
  }
  /deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #409eff !important;
    color: #fff;
  }
  /deep/.el-pagination.is-background .btn-next,
  /deep/.el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: #fff !important;
    color: #07090d;
    border: 1px solid #d9d9d9;
    line-height: 28px !important;
  }
  /deep/.el-pager li {
    line-height: 27px !important;
  }
  /deep/.el-pager .more::before {
    line-height: 26px !important;
  }
  /deep/.el-pagination__rightwrapper {
    min-width: 100px;
    text-align: right;
  }
}
.pagination-container.hidden {
  display: none;
}
</style>
