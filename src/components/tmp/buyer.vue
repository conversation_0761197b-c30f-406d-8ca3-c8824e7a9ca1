<template>
  <div>
    <el-form ref="formDataRef" :model="formData" label-width="130px">
      <el-form-item v-show="formData.buyer_id">
        <span slot="label">客户id：</span>
        <el-input disabled v-model.trim="formData.buyer_id" placeholder="id" maxlength="40" />
      </el-form-item>
      <el-form-item prop="company_name">
        <span slot="label">购买⽅身份类型：</span>
        <el-radio-group v-model="formData.identify_type" :disabled="type === 'look'">
          <el-radio :label="1">企业</el-radio>
          <el-radio :label="2">自然人</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="buyer_name" v-if="formData.identify_type == 1" :rules="[
        { required: true, message: '请输入购买⽅名称', trigger: 'blur' }
      ]">
        <span slot="label">购买⽅名称：</span>
        <!-- <el-input :disabled="type === 'look'" v-model.trim="formData.buyer_name" placeholder="输入购买⽅名称" maxlength="50" /> -->
        <el-autocomplete :disabled="type === 'look'" v-model="formData.buyer_name" popper-class="search-autocpmplete"
          :fetch-suggestions="querySearchCompanyAsync" value-key="Name" :debounce="500" :trigger-on-focus="false"
          placeholder="输入企业名称" @select="handleSelect" style="width:100%"></el-autocomplete>
      </el-form-item>
      <el-form-item prop="buyer_name" v-else :rules="[
        { required: true, message: '请输入自然人名称', trigger: 'blur' }
      ]">
        <span slot="label">自然人：</span>
        <el-input :disabled="type === 'look'" v-model.trim="formData.buyer_name" placeholder="输入自然人名称" maxlength="50" />
      </el-form-item>
      <el-form-item prop="buyer_short_name" v-if="formData.identify_type == 1" :rules="[
        { required: true, message: '请输入购买⽅简称', trigger: 'blur' }
      ]">
        <span slot="label">购买⽅简称：</span>
        <el-input :disabled="type === 'look'" v-model.trim="formData.buyer_short_name" maxlength="50"
          placeholder="输入购买⽅简称" />
      </el-form-item>
      <el-form-item prop="tax_no" :rules="[
        { required: true, message: '请输入纳税人识别号', trigger: 'blur' }
      ]">
        <span slot="label">{{ formData.identify_type == 1 ? '纳税人识别号' : '身份证' }}：</span>
        <el-input :disabled="type === 'look'" maxlength="30" v-model.trim="formData.tax_no" placeholder="输入纳税人识别号" />
      </el-form-item>
      <el-form-item prop="bank">
        <span slot="label">购⽅开户银⾏：</span>
        <el-input :disabled="type === 'look'" maxlength="50" v-model.trim="formData.bank" placeholder="输入购⽅开户银⾏" />
      </el-form-item>
      <el-form-item prop="bank_account">
        <span slot="label">购⽅银⾏账号：</span>
        <el-input :disabled="type === 'look'" maxlength="25" v-model.trim="formData.bank_account"
          placeholder="输入购⽅银⾏账号" />
      </el-form-item>
      <el-form-item prop="buyer_phone"
        :rules="[{ required: false, pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }]">
        <span slot="label">购买⽅电话：</span>
        <el-input :disabled="type === 'look'" v-model.trim="formData.buyer_phone" placeholder="输入购买⽅电话" maxlength="11" />
      </el-form-item>
      <el-form-item prop="buyer_address">
        <span slot="label">购买⽅地址：</span>
        <el-input :disabled="type === 'look'" maxlength="120" v-model.trim="formData.buyer_address"
          placeholder="输入购买⽅地址" />
      </el-form-item>
      <el-form-item prop="buyer_email" :rules="[
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ]">
        <span slot="label">购买⽅邮箱：</span>
        <el-input :disabled="type === 'look'" v-model.trim="formData.buyer_email" placeholder="输入购买⽅邮箱" maxlength="50" />
      </el-form-item>
      <el-form-item prop="business_scope" v-if="formData.identify_type == 1">
        <span slot="label">经营范围：</span>
        <el-input :disabled="type === 'look'" type="textarea" rows="6" v-model.trim="formData.business_scope"
          placeholder="输入经营范围" />
      </el-form-item>
      <el-form-item prop="legal_represent" v-if="formData.identify_type == 1">
        <span slot="label">法⼈代表：</span>
        <el-input :disabled="type === 'look'" v-model.trim="formData.legal_represent" placeholder="输入法⼈代表"
          maxlength="20" />
      </el-form-item>
      <!-- && !formData.user_id -->
      <el-form-item v-if="auth == 2" prop="user_id" :rules="[
        { required: true, message: '请选择客户联系人', trigger: 'blur' }
      ]">
        <span slot="label">客户联系人：</span>
        <el-select :disabled="type === 'look'" v-model="formData.user" value-key="user_id" style="width: 100%"
          :filterable="filterable1" remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod"
          :loading="loading" maxlength="30">
          <el-option v-for="item in options" :key="item.user_id" :label="item.user_name" :value="item">
            <div @click="userSelect(item)">
              <span>{{ item.user_name }}</span>
              <span style=" color: #8492a6; font-size: 13px"> {{ item.user_id }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-else>
        <span slot="label">客户联系人：</span>
        <div>
          {{ formData.user_name }}
        </div>
      </el-form-item>
      <el-form-item v-if="auth == 2" prop="company_id" :rules="[
        { required: true, message: '请选择销售方企业', trigger: 'blur' }
      ]">
        <span slot="label">销售方企业：</span>
        <el-select :disabled="type === 'look'" v-model="formData.company" value-key="company_id" style="width:100%;"
          :filterable="filterable2" remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethodUser"
          :loading="loading" maxlength="30">
          <el-option v-for="item in company_list" :key="item.company_id" :label="item.company_name" :value="item">
            <div @click="companySelect(item)">
              <span>{{ item.company_name }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="flex-row-center" v-if="type != 'look'">
      <el-button @click="close">关闭</el-button>
      <el-button type="primary" @click="submitData">保存</el-button>
    </div>
  </div>
</template>

<script>
const defaultDataItem = {
  bank: "",
  bank_account: "",
  business_scope: "",
  buyer_address: "",
  buyer_email: "",
  buyer_id: "",
  buyer_name: "",
  buyer_phone: "",
  buyer_recipient: "",
  buyer_short_name: "",
  identify_type: 1,
  legal_represent: "",
  tax_no: "",
  user_id: "",
  company_id: "",
};
import { getToken } from '@/utils/auth' // get token from cookie
import { requestApi } from "@/utils/request";
export default {
  name: "Buyer",
  props: {
    baseData: {
      type: Object,
      default() {
        return defaultDataItem
      }
    },
    type: {
      type: String
    }
  },
  watch: {
    'formData.identify_type'() {
      this.formData.business_scope = ''
      this.formData.legal_represent = ''
    },
    'baseData'() {
      this.formData = Object.assign(this.formData, this.baseData)
      console.log(this.formData, 'watch')
      this.formData.user = {
        user_name: this.baseData.user_name,
        user_id: this.baseData.user_id
      }
      this.formData.company = {
        company_id: this.baseData.company_id,
        company_name: this.baseData.company_name
      }
      this.options = [{
        user_name: this.baseData.user_name,
        user_id: this.baseData.user_id
      }]
      this.company_list = [{
        company_id: this.baseData.company_id,
        company_name: this.baseData.company_name
      }]
      if (this.formData.user_id) {
        this.getClientSearchCompany({ user_id: this.formData.user_id })
      }
      if (this.formData.company_id) {
        this.getSearchClients({ company_id: this.formData.company_id })
      }
    }
  },
  data() {
    return {
      company_list: [],
      options: [],
      loading: false,
      filterable1: true,
      filterable2: true,
      formData: Object.assign({}, defaultDataItem),
      auth: getToken()
    };
  },
  created() {
  },
  mounted() {
  },
  methods: {
    // 用户选择获取企业
    userSelect(item) {
      this.formData.user_name = item.user_name
      this.formData.user_id = item.user_id
      this.formData.user = {
        user_name: item.user_name,
        user_id: item.user_id
      }
      let data = {
        user_id: item.user_id
      }
      this.getClientSearchCompany(data)
    },
    // 企业选择获取用户
    companySelect(item) {
      this.formData.company_name = item.company_name
      this.formData.company_id = item.company_id
      this.formData.company = {
        company_name: item.company_name,
        company_id: item.company_id
      }
      let data = {
        company_id: item.company_id
      }
      this.getSearchClients(data)
    },
    // 企业获取用户
    getSearchClients(data) {
      requestApi({
        name: 'getSearchClients',
        data: data
      }).then(res => {
        if (res.code === 200) {
          this.options = res.data.user_list || []
        }
      })
    },
    // 客户获取企业
    getClientSearchCompany(data) {
      requestApi({
        name: 'getClientSearchCompany',
        data: data
      }).then(res => {
        if (res.code === 200) {
          this.company_list = res.data.company_list || []
        }
      })
    },
    // 搜索企业
    querySearchCompanyAsync(queryString, cb) {
      let results = []
      let data = {
        keyword: queryString
      }
      requestApi({
        name: 'qccCompanySearch',
        data
      }).then((res) => {
        if (res.code === 200) {
          results = res.data.Data || []
          cb(results)
        }
      })
    },
    close() {
      this.$emit("update:close", false)
    },
    // 选择公司
    handleSelect(item) {
      let data = {
        keyword: item.Name
      }
      requestApi({
        name: 'tycCompanyInfo',
        data
      }).then((res) => {
        if (res.code === 200) {
          let info = res.data
          this.formData.business_scope = info.businessScope
          this.formData.buyer_address = info.regLocation
          this.formData.buyer_short_name = info.alias
          this.formData.legal_represent = info.legalPersonName
          this.formData.tax_no = info.creditCode
        }
      })
    },
    // 提交结果
    submitData() {
      console.log(this.formData)
      this.$refs["formDataRef"].validate((valid) => {
        if (valid) {
          const data = this.formData;
          requestApi({
            name: "postBuyerInfo",
            data,
          }).then((res) => {
            if (res.code === 200) {
              this.$message.success("保存成功");
            } else {
              // this.$message.error(res.msg);
            }
          });
        }
      });
    },
    // 客户联系人远程
    remoteMethod(queryString) {
      if (queryString !== "") {
        this.loading = true;
        this.formData.company_name = ''
        this.formData.company_id = ''
        this.formData.company = {
          company_name: '',
          company_id: ''
        }
        requestApi({
          name: "getCompanyAssociateUsers",
          data: {
            keyword: queryString,
            page: 1,
            page_size: 100,
          },
        }).then((res) => {
          this.loading = false;
          this.options = res.results;
        });
      } else {
        this.options = []
      }
    },
    // 销售方企业远程
    remoteMethodUser(queryString) {
      if (queryString !== "") {
        this.loading = true;
        this.formData.user_id = ''
        this.formData.user_name = ''
        this.formData.user = {
          user_id: '',
          user_name: ''
        }
        requestApi({
          name: "getCompanyList",
          data: {
            keyword: queryString,
            page: 1,
            page_size: 100,
          },
        }).then((res) => {
          this.loading = false;
          this.company_list = res.results;
        });
      } else {
        this.company_list = []
      }
    }
  },
};
</script>
