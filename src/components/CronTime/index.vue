<template>
  <div class="cron-time-container">
    <div class="cron-mode-switch">
      <el-radio-group v-model="timeMode" size="mini">
        <el-radio-button label="specific">指定时间</el-radio-button>
        <el-radio-button label="repeat">重复时间</el-radio-button>
      </el-radio-group>
      <el-button size="mini" type="text" @click="reset" class="reset-btn"
        >重置</el-button
      >
    </div>

    <!-- 指定时间模式 -->
    <div v-show="timeMode === 'specific'" class="specific-time-mode">
      <el-date-picker
        v-model="specificTime"
        type="datetime"
        placeholder="选择具体日期和时间"
        format="yyyy-MM-dd HH:mm"
        value-format="timestamp"
        @change="handleSpecificTimeChange"
      />
      <div class="mode-help">
        <span>指定时间模式: 选择一个特定的日期和时间</span>
      </div>
    </div>

    <!-- 重复时间模式 -->
    <div v-show="timeMode === 'repeat'" class="repeat-time-mode">
      <div class="frequency">
        <span>每</span>
        <el-select
          v-model="frequency.value"
          placeholder="间隔值"
          size="mini"
          style="width: 70px"
          v-show="showIntervalSelect"
        >
          <el-option v-for="i in 365" :key="i" :label="i" :value="i" />
        </el-select>
        <el-select
          v-model="frequency.type"
          placeholder="周期类型"
          size="mini"
          style="width: 80px"
          @change="handleFrequencyTypeChange"
        >
          <el-option label="天" value="day" />
          <el-option label="周" value="week" />
          <el-option label="月" value="month" />
          <el-option label="年" value="year" />
        </el-select>
      </div>

      <!-- 每天模式 -->
      <div v-if="frequency.type === 'day'" class="day-mode">
        <div class="mode-help">
          <span>每天或每隔多天在指定时间执行</span>
        </div>
        <el-time-picker
          v-model="dayTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="generateCronExpression"
          size="mini"
        />
      </div>

      <!-- 每周模式 -->
      <div v-if="frequency.type === 'week'" class="week-mode">
        <div class="mode-help">
          <span>选择每周的特定几天执行</span>
        </div>
        <div class="weekday-selector">
          <span>星期</span>
          <el-checkbox-group
            v-model="selectedWeekdays"
            @change="handleWeekdayChange"
          >
            <el-checkbox-button
              v-for="day in weekdays"
              :key="day.value"
              :label="day.value"
            >
              周{{ day.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
        <el-time-picker
          v-model="weekTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="generateCronExpression"
          size="mini"
        />
      </div>

      <!-- 每月模式 -->
      <div v-if="frequency.type === 'month'" class="month-mode">
        <div class="mode-help">
          <span>选择每月的特定日期执行</span>
        </div>
        <div class="day-selector">
          <span>按日期</span>
          <el-select
            v-model="selectedMonthDays"
            multiple
            placeholder="选择日期"
            size="mini"
            @change="generateCronExpression"
          >
            <el-option v-for="i in 31" :key="i" :label="`${i}日`" :value="i" />
          </el-select>
        </div>
        <el-time-picker
          v-model="monthTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="generateCronExpression"
          size="mini"
        />
      </div>

      <!-- 每年模式 -->
      <div v-if="frequency.type === 'year'" class="year-mode">
        <div class="mode-help">
          <span>选择每年的特定日期执行</span>
        </div>
        <el-date-picker
          v-model="yearDate"
          type="date"
          placeholder="选择日期"
          format="MM月dd日"
          value-format="MM-dd"
          @change="generateCronExpression"
          size="mini"
        />
        <el-time-picker
          v-model="yearTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="generateCronExpression"
          size="mini"
        />
      </div>
    </div>

    <!-- 跳过法定节假日/双休日 -->
    <!-- <div class="skip-options">
      <el-checkbox v-model="skipHolidays" @change="generateCronExpression"
        >跳过法定节假日</el-checkbox
      >
      <el-checkbox v-model="skipWeekends" @change="generateCronExpression"
        >跳过双休日</el-checkbox
      >
    </div> -->

    <!-- Cron 表达式显示 -->
    <div class="cron-result">
      <el-input
        v-model="cronExpression"
        placeholder="Cron表达式"
        :readonly="true"
        size="mini"
      >
        <template slot="prepend">Cron</template>
      </el-input>
    </div>
  </div>
</template>

<script>
export default {
  name: "CronTime",
  props: {
    value: {
      type: String,
      default: "0 0 * * * 0 *", // 默认cron表达式
    },
  },
  data() {
    return {
      timeMode: "repeat", // 默认为重复时间模式
      cronExpression: this.value,
      specificTime: null, // 具体时间

      // 防抖计时器
      debounceTimer: null,

      // 重复时间模式
      frequency: {
        value: 1,
        type: "day", // 默认为每天模式
      },
      showIntervalSelect: true,

      // 每天模式
      dayTime: null,

      // 每周模式
      weekdays: [
        { label: "日", value: "0" },
        { label: "一", value: "1" },
        { label: "二", value: "2" },
        { label: "三", value: "3" },
        { label: "四", value: "4" },
        { label: "五", value: "5" },
        { label: "六", value: "6" },
      ],
      selectedWeekdays: ["1"], // 默认只选中周一，减少初始化负担
      weekTime: null,

      // 每月模式
      selectedMonthDays: [],
      monthTime: null,

      // 每年模式
      yearDate: null,
      yearTime: null,

      // 跳过选项
      skipHolidays: false,
      skipWeekends: false,
    };
  },
  watch: {
    value(newVal) {
      this.cronExpression = newVal;
      this.parseCronExpression();
    },
    cronExpression(newVal) {
      this.$emit("input", newVal);
    },
    // 监听各种模式下的时间变化
    timeMode() {
      this.debounce(this.generateCronExpression);
    },
    "frequency.value"() {
      this.debounce(this.generateCronExpression);
    },
    "frequency.type"() {
      this.debounce(this.generateCronExpression);
    },
    dayTime() {
      if (this.frequency.type === "day") {
        this.debounce(this.generateCronExpression);
      }
    },
    weekTime() {
      if (this.frequency.type === "week") {
        this.debounce(this.generateCronExpression);
      }
    },
    selectedWeekdays: {
      handler() {
        if (this.frequency.type === "week") {
          this.debounce(this.generateCronExpression);
        }
      },
      deep: true,
      immediate: false,
    },
    monthTime() {
      if (this.frequency.type === "month") {
        this.debounce(this.generateCronExpression);
      }
    },
    selectedMonthDays: {
      handler() {
        if (this.frequency.type === "month") {
          this.debounce(this.generateCronExpression);
        }
      },
      deep: true,
    },
    yearTime() {
      if (this.frequency.type === "year") {
        this.debounce(this.generateCronExpression);
      }
    },
    yearDate() {
      if (this.frequency.type === "year") {
        this.debounce(this.generateCronExpression);
      }
    },
  },
  created() {
    this.initComponent();
  },
  methods: {
    // 初始化组件
    initComponent() {
      // 设置初始时间对象
      const now = new Date();
      now.setHours(9);
      now.setMinutes(0);
      now.setSeconds(0);

      if (!this.dayTime) this.dayTime = new Date(now);
      if (!this.weekTime) this.weekTime = new Date(now);
      if (!this.monthTime) this.monthTime = new Date(now);
      if (!this.yearTime) this.yearTime = new Date(now);
      if (!this.yearDate) this.yearDate = "01-01"; // 默认1月1日

      // 解析传入的表达式
      if (this.cronExpression) {
        this.parseCronExpression();
      } else {
        // 如果没有传入表达式，生成一个默认的
        this.debounce(this.generateCronExpression);
      }
    },

    // 防抖函数，避免频繁更新
    debounce(func, wait = 300) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        func.call(this);
      }, wait);
    },

    // 解析传入的cron表达式
    parseCronExpression() {
      if (!this.cronExpression) return;

      try {
        // 分割cron表达式
        // 格式：分 时 日 月 周 秒 年
        const parts = this.cronExpression.split(" ");
        if (parts.length < 5) return;

        // 处理5位、6位或7位cron表达式
        let minute,
          hour,
          day,
          month,
          weekday,
          second = "0",
          year = "*";

        if (parts.length >= 7) {
          [minute, hour, day, month, weekday, second, year] = parts;
        } else if (parts.length >= 6) {
          [minute, hour, day, month, weekday, second] = parts;
        } else {
          [minute, hour, day, month, weekday] = parts;
        }

        // 设置默认时间对象，如果需要的话
        const time = new Date();
        time.setHours(minute !== "*" && hour !== "*" ? parseInt(hour) : 9);
        time.setMinutes(minute !== "*" ? parseInt(minute) : 0);
        time.setSeconds(second !== "*" ? parseInt(second) : 0);

        // 如果年份不是*，那么是指定时间模式
        if (year && year !== "*") {
          this.timeMode = "specific";

          // 创建日期对象
          const date = new Date();
          date.setFullYear(parseInt(year));
          date.setMonth(parseInt(month) - 1);
          date.setDate(parseInt(day));
          date.setHours(parseInt(hour));
          date.setMinutes(parseInt(minute));
          date.setSeconds(parseInt(second));

          this.specificTime = date.getTime();
          return;
        }

        // 否则是重复时间模式
        this.timeMode = "repeat";

        // 预设时间对象
        this.dayTime = new Date(time);
        this.weekTime = new Date(time);
        this.monthTime = new Date(time);
        this.yearTime = new Date(time);

        // 检查是哪种重复模式

        // 每天模式 - 检查日字段是否有斜杠模式 (*/n)
        if (
          (day.includes("/") || day === "*") &&
          weekday === "*" &&
          month === "*"
        ) {
          this.frequency.type = "day";
          this.showIntervalSelect = true;

          // 解析间隔天数
          if (day.includes("/")) {
            const interval = day.split("/")[1];
            this.frequency.value = parseInt(interval);
          } else {
            this.frequency.value = 1; // 每天
          }
        }
        // 每周模式 - 检查周字段是否不是星号
        else if (weekday !== "*" && day === "*" && month === "*") {
          this.frequency.type = "week";
          this.showIntervalSelect = false;

          // 清空当前选择，避免多次渲染
          this.selectedWeekdays = [];

          // 延迟处理周选择，防止界面卡顿
          this.$nextTick(() => {
            // 解析选中的星期
            if (weekday.includes(",")) {
              this.selectedWeekdays = weekday.split(",");
            } else if (weekday.includes("-")) {
              const [start, end] = weekday.split("-").map(Number);
              const days = [];
              for (let i = start; i <= end; i++) {
                days.push(i.toString());
              }
              this.selectedWeekdays = days;
            } else {
              this.selectedWeekdays = [weekday];
            }
          });
        }
        // 每月模式 - 检查日字段是否不是星号且不含斜杠
        else if (
          day !== "*" &&
          !day.includes("/") &&
          weekday === "*" &&
          month === "*"
        ) {
          this.frequency.type = "month";
          this.showIntervalSelect = false;
          this.selectedMonthDays = [];

          // 解析选中的日期
          if (day.includes(",")) {
            this.selectedMonthDays = day.split(",").map(Number);
          } else if (day.includes("-")) {
            const [start, end] = day.split("-").map(Number);
            const days = [];
            for (let i = start; i <= end; i++) {
              days.push(i);
            }
            this.selectedMonthDays = days;
          } else {
            this.selectedMonthDays = [parseInt(day)];
          }
        }
        // 每年模式 - 检查月字段和日字段是否都不是星号
        else if (day !== "*" && month !== "*" && weekday === "*") {
          this.frequency.type = "year";
          this.showIntervalSelect = false;

          // 设置日期
          const monthVal = parseInt(month);
          const dayVal = parseInt(day);
          this.yearDate = `${monthVal < 10 ? "0" + monthVal : monthVal}-${
            dayVal < 10 ? "0" + dayVal : dayVal
          }`;
        }
        // 默认回退到每天模式
        else {
          this.frequency.type = "day";
          this.frequency.value = 1;
          this.showIntervalSelect = true;
        }
      } catch (error) {
        console.error("解析cron表达式出错:", error);
        // 设置默认值
        this.timeMode = "repeat";
        this.frequency.type = "day";
        this.frequency.value = 1;
        this.showIntervalSelect = true;
      }
    },

    // 处理频率类型变化
    handleFrequencyTypeChange(type) {
      this.showIntervalSelect = type === "day";

      // 当切换到年模式时，设置默认值
      if (type === "year") {
        if (!this.yearDate) {
          // 设置默认日期为1月1日
          this.yearDate = "01-01";
        }
        if (!this.yearTime) {
          // 设置默认时间为9:00
          const defaultTime = new Date();
          defaultTime.setHours(9);
          defaultTime.setMinutes(0);
          defaultTime.setSeconds(0);
          this.yearTime = defaultTime;
        }
      }

      // 使用防抖来生成表达式
      this.debounce(this.generateCronExpression);
    },

    // 处理星期选择变化
    handleWeekdayChange(value) {
      // 确保至少选择一个星期
      if (!value || value.length === 0) {
        this.selectedWeekdays = ["1"]; // 默认选择周一
      }
      this.generateCronExpression();
    },

    // 处理具体时间模式的变化
    handleSpecificTimeChange() {
      if (!this.specificTime) return;

      // 使用防抖来生成表达式
      this.debounce(() => {
        const date = new Date(this.specificTime);
        const minute = date.getMinutes();
        const hour = date.getHours();
        const day = date.getDate();
        const month = date.getMonth() + 1;
        const year = date.getFullYear();

        // 格式: 分 时 日 月 周 秒 年
        this.cronExpression = `${minute} ${hour} ${day} ${month} * 0 ${year}`;
      });
    },

    // 生成cron表达式
    generateCronExpression() {
      if (this.timeMode === "specific") {
        this.handleSpecificTimeChange();
        return;
      }

      // 重复时间模式
      let minute = "*";
      let hour = "*";
      let day = "*";
      let month = "*";
      let weekday = "*";
      const second = "0";
      const year = "*";

      // 提取时间
      const getTimeComponents = (timeValue) => {
        if (!timeValue) return { minute: "*", hour: "*" };
        const hours = timeValue.getHours();
        const minutes = timeValue.getMinutes();
        return { minute: minutes, hour: hours };
      };

      switch (this.frequency.type) {
        case "day":
          if (this.dayTime) {
            const { minute: m, hour: h } = getTimeComponents(this.dayTime);
            minute = m;
            hour = h;
          }

          if (this.frequency.value > 1) {
            day = `*/${this.frequency.value}`;
          }
          break;

        case "week":
          if (this.weekTime) {
            const { minute: m, hour: h } = getTimeComponents(this.weekTime);
            minute = m;
            hour = h;
          }

          // 修复每周模式下的性能问题
          if (this.selectedWeekdays && this.selectedWeekdays.length > 0) {
            // 避免频繁排序，使用浅拷贝
            const sortedWeekdays = [...this.selectedWeekdays].sort();
            weekday = sortedWeekdays.join(",");
          } else {
            // 如果没有选择任何星期，默认选择周一
            this.selectedWeekdays = ["1"];
            weekday = "1";
          }
          break;

        case "month":
          if (this.monthTime) {
            const { minute: m, hour: h } = getTimeComponents(this.monthTime);
            minute = m;
            hour = h;
          }

          if (this.selectedMonthDays && this.selectedMonthDays.length > 0) {
            const sortedDays = [...this.selectedMonthDays].sort(
              (a, b) => a - b
            );

            // 检查是否是连续的日期，可以使用范围表示
            let isRange = true;
            for (let i = 1; i < sortedDays.length; i++) {
              if (sortedDays[i] !== sortedDays[i - 1] + 1) {
                isRange = false;
                break;
              }
            }

            if (isRange && sortedDays.length > 1) {
              day = `${sortedDays[0]}-${sortedDays[sortedDays.length - 1]}`;
            } else {
              day = sortedDays.join(",");
            }
          }
          break;

        case "year":
          // 设置时间
          if (this.yearTime) {
            const { minute: m, hour: h } = getTimeComponents(this.yearTime);
            minute = m;
            hour = h;
          } else {
            // 默认时间为9:00
            minute = 0;
            hour = 9;
          }

          // 设置日期
          if (this.yearDate) {
            // 解析MM-DD格式
            const dateParts = this.yearDate.split("-");
            if (dateParts.length === 2) {
              const [monthVal, dayVal] = dateParts.map(Number);
              month = monthVal;
              day = dayVal;
            }
          } else {
            // 默认日期为1月1日
            month = 1;
            day = 1;
          }
          break;
      }

      // 确保分钟和小时不为*，如果用户选择了时间
      if (this.frequency.type === "day" && this.dayTime && minute === "*") {
        minute = "0";
      }
      if (this.frequency.type === "week" && this.weekTime && minute === "*") {
        minute = "0";
      }
      if (this.frequency.type === "month" && this.monthTime && minute === "*") {
        minute = "0";
      }
      if (this.frequency.type === "year" && this.yearTime && minute === "*") {
        minute = "0";
      }

      // 构建cron表达式：分 时 日 月 周 秒 年
      const newCronExpression = `${minute} ${hour} ${day} ${month} ${weekday} ${second} ${year}`;

      // 只有在表达式确实变化时才更新
      if (this.cronExpression !== newCronExpression) {
        this.cronExpression = newCronExpression;
      }
    },

    // 重置组件状态
    reset() {
      this.timeMode = "repeat";
      this.frequency.type = "day";
      this.frequency.value = 1;
      this.showIntervalSelect = true;

      const now = new Date();
      now.setHours(9);
      now.setMinutes(0);
      now.setSeconds(0);

      this.dayTime = new Date(now);
      this.weekTime = new Date(now);
      this.monthTime = new Date(now);
      this.yearTime = new Date(now);

      this.selectedWeekdays = ["1"];
      this.selectedMonthDays = [];
      this.yearDate = "01-01"; // 默认1月1日

      this.specificTime = null;

      // 生成默认表达式
      this.debounce(this.generateCronExpression);
    },
  },
};
</script>

<style lang="less" scoped>
.cron-time-container {
  padding: 15px;
  border-radius: 4px;
  background-color: #f5f7fa;

  .cron-mode-switch {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .specific-time-mode,
  .repeat-time-mode {
    margin-bottom: 15px;
  }

  .frequency {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    span {
      margin-right: 10px;
    }
  }

  .day-mode,
  .week-mode,
  .month-mode,
  .year-mode {
    margin-top: 10px;
    margin-bottom: 15px;
    padding: 12px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
  }

  .weekday-selector {
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    span {
      margin-right: 10px;
    }
  }

  .day-selector {
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    span {
      margin-right: 10px;
    }

    .el-select {
      width: 240px;
    }
  }

  .skip-options {
    margin-bottom: 15px;
    display: flex;
    gap: 15px;
  }

  .cron-result {
    margin-top: 15px;
  }

  .el-checkbox {
    margin-right: 15px;
  }

  .el-date-picker,
  .el-time-picker {
    margin-right: 10px;
  }

  .mode-help {
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 0.8em;
    color: #909399;
  }

  .reset-btn {
    margin-left: 10px;
    color: #409eff;
  }

  // 修复复选框组样式
  /deep/ .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }

  /deep/ .el-checkbox-button__inner {
    padding: 6px 10px;
  }
}
</style>
