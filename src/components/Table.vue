<template>
  <div ref="kkk" class="tables" style="background: #fff;">
    <div class="table-outer">
      <div
        class="table-h"
        :style="{
          'min-height':
            tableConfig.withPagination
              ? 'calc(100vh - ' + tConfig.height + ')'
              : 'auto',
        }"
      >
        <el-table
          id="tableData"
          ref="tableData"
          v-loading="loading"
          :empty-text="emptyText"
          :row-key="rowKey ? rowKey :
            (row) => {
              return row.id;
            }
          "
          header-row-class-name="table-list-header"
          row-class-name="table-list-row"
          :style="{ 'margin-bottom': hasPage ? '50px' : '' }"
          :size="tableSize"
          :data="data"
          :tree-props="
            isHaveChild
              ? { children: children, hasChildren: 'hasChildren' }
              : {}
          "
          :cell-style="tableRowClassNameFun"
          border
          @selection-change="handleSelectionChange"
          @current-change="handleTableCurrentChange"
          @row-click="handleTableRowClick"
          @sort-change="clickSort"
          @expand-change="expandChange"
        >
          <template v-for="(item, index) in columns">
            <!-- 勾选框 -->
            <el-table-column
              v-if="item.hasSelection"
              :key="index"
              type="selection"
              :reserve-selection="item.reserve"
              :width="item.width || 50"
              :selectable="item.selectableFun"
            />
            <!-- 序号 -->
            <el-table-column
              v-else-if="item.hasIndex"
              :key="`index_${index}`"
              type="index"
              :width="item.width || 60"
              :fixed="item.fixed"
              label="序号"
              align="center"
              :index="item.indexMethod"
            >
              <template slot-scope="scope">
                <div v-if="pagination">
                  {{ pagination.page_size * (pagination.page - 1) + scope.$index + 1 }}
                </div>
                <div v-else>{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <!-- 自定义内容 -->
            <slot
              v-else-if="item.slot"
              show-overflow-tooltip
              :name="item.slot"
              :fixed="item.fixed"
            />
            <!-- 常规字段 -->
            <el-table-column
              v-else
              :key="`normal_${index}`"
              show-overflow-tooltip
              v-bind="item"
              :fixed="item.fixed"
              :min-width="item.minWidth"
            >
              <template>
                <div slot-scope="scope"></div>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <div v-if="hasPage">
        <Pagination
          v-if="!tableConfig.withPagination"
          :total="total"
          :page.sync="pagination.page"
          :limit.sync="pagination.page_size"
          style="background: #fff"
          @pagination="getPageList"
        />
        <Pagination
          v-else
          :total="total"
          :page.sync="pagination.page"
          :limit.sync="pagination.page_size"
          :style="{
            width: $store.state.app.sidebar.opened
              ? 'calc(100% - 262px)'
              : 'calc(100% - 78px)',
            left: $store.state.app.sidebar.opened ? '252px' : '66px',
          }"
          style="
            background: #fff;
            position: fixed;
            bottom: 0px;
            right: 12px;
            z-index: 99;
          "
          @pagination="getPageList"
        />
      </div>
      <!-- 自定义内容 -->
      <slot name="custom-content" />
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
export default {
  name: 'Table',
  components: {
    Pagination
  },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        page_size: 10
      })
    },
    // isPaginationShow: {
    //   type: Boolean,
    //   default: true
    // },
    tableConfig: {
      type: Object,
      default: () => ({
        height: 0, // 查询和表格之间按钮或者tab的高度
        border: false,
        withPagination: true
      })
    },
    total: {
      type: Number,
      default: 0
    },
    hasPage: {
      type: Boolean,
      default: true
    },
    tableSize: {
      type: String,
      default: 'small'
    },
    loading: {
      type: Boolean,
      default: false
    },
    rowKey: {},
    emptyText: {
      type: String,
      default: ''
    },
    isHaveChild: {
      type: Boolean,
      default: false
    },
    children: {
      type: String,
      default: ''
    },
    tableRowClassName: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      loadingShow: null,
      tConfig: {},
      aad: false,
      tableRowClassNameFun: ({ row, rowIndex }) => {
        return this.tableRowClassName({ row, rowIndex })
      }
    }
  },
  watch: {
    data() {
      // 重新请求数据时 table滚动到顶部
      this.$refs.tableData.$refs.bodyWrapper.scrollTop = 0
    },
    loading(val) {
      if (!val) {
        // this.loadingShow.close();
      }
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.resizeHandler()
      this.setBorderStyle()
      window.addEventListener('resize', this.resizeHandler)
    })
    // this.loadingShow = this.$loading({
    //   lock: true, //同v-loading的修饰符
    //   text: "", //加载文案
    //   backgroundColor: "rgba(55,55,55,0.4)", //背景色
    //   target: document.querySelector(".el-table__body"), //loading需要覆盖的DOM节点，默认为body
    // });
  },
  methods: {
    setBorderStyle() {
      const h = document.querySelector('.table-list-header')
      const c = document.querySelector('.el-table__body')
      h.onmouseenter = function() {
        c.className = 'el-table__body bc'
      }
      h.onmouseleave = function() {
        c.className = 'el-table__body'
      }
    },
    setBorderStyle1() {
      const h = document.querySelector('.table-list-header')
      const c = document.querySelectorAll('.table-list-row td')
      h.onmouseenter = function() {
        c.className = 'el-table__body bc'
      }
      h.onmouseleave = function() {
        c.className = 'el-table__body'
      }
      // let aa = document.querySelectorAll(".table-list-header th");
      // for (let i = 0; i < aa.length; i++) {
      //   aa[i].onmouseenter = function () {
      //     for (let j = 0; j < aa.length; j++) {
      //       aa[j].setAttribute("id", "");
      //     }
      //     this.setAttribute("id", "cdc");
      //   };
      //   aa[i].onmouseleave = function () {
      //     aa[i].setAttribute("id", "");
      //   };
      // }
    },
    // 计算table、分页的高度和宽度
    resizeHandler() {
      const c = this.$parent.$el.firstChild
      const baseConfig = {
        height: c && c.offsetHeight + Number(this.tableConfig.height || 0) + 100 + 'px',
        width: c && c.clientWidth
      }
      this.tConfig = baseConfig
      // console.log('---this.tConfig', c.offsetHeight, Number(this.tableConfig.height || 0))
    },
    // 切换页码
    handleCurrentChange() {
      this.$emit('getData')
    },
    // 切换每页条数
    handleSizeChange(value) {
      // current-page和 page-size都支持 .sync修饰符，用了.sync修饰符，就不需要手动给 this.pagination赋值了
      this.pagination.size = value
      this.$emit('getData')
    },
    // 切换选择
    handleSelectionChange(val) {
      this.$emit('changeSelection', val)
    },
    getPageList() {
      this.$emit('getData')
    },
    // 单选
    handleTableCurrentChange(currentRow) {
      this.$emit('changeCurrent', currentRow)
    },
    // 点击行
    handleTableRowClick(currentRow) {
      this.$emit('rowClick', currentRow)
    },
    // 点击排序
    clickSort(val) {
      this.$emit('clickSort', val)
    },
    // 点击展开关闭
    expandChange(row, expandedRows) {
      this.$emit('expandChange', { row, expandedRows })
    }
  }
}
</script>
<style lang="less" scoped>
.table-outer {
  background-position: bottom right;
  padding: 10px 10px;
}
#tableData {
  background: transparent !important;
  /deep/.el-table__header,
  /deep/.is-leaf {
    background: #eff2f5 !important;
    height: 50px;
  }
}
/deep/.el-table__row:hover {
  background: #ffffff !important;
  box-shadow: 0px 0px 10px 0px rgba(82, 111, 146, 0.15) !important;
}
.table-h {
  overflow-y: auto;
  /deep/.el-table__header-wrapper .el-table__cell {
    background: #eff2f5 !important;
    color: #333;
    font-size: 13px;
  }

  /deep/.el-table__body {
    background: transparent;
  }

  /deep/.el-table tr {
    background: none !important;
  }
  /deep/.el-table tbody tr:hover > td {
    background-color: #f0f6fd;
  }
  /deep/.table-list-row {
    .el-table__cell {
      // padding: 10px 0 !important;
      background: transparent;
      font-size: 12px;
    }
    .action-class {
      background-position: 100% 100%;
      border-right: 1px solid #ebeef5 !important;
    }
  }
  .el-table--small {
    font-size: 14px !important;
  }
}
/deep/.table-list-header:hover {
  th {
    // border-right: 1px solid #d3d9de !important;
  }
}
// /deep/.el-table--border .el-table__cell,
// .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
//   border-right: 1px solid #117af7;
// }
</style>
<style>
#cdc {
  border-right: 1px solid #117af7 !important;
}
</style>
