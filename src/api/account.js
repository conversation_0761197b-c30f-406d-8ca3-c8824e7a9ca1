const base = ''
export default {
  // ====================== 个人用户账户管理接口 ======================
  //根据关键字搜索个人用户账户列表
  getPersonalAccountsBySearch: {
    url: base + '/account/personal/search/',
    method: 'GET'
  },

  // 获取个人用户帐户明细
  getUserAccountDetail: {
    url: base + '/account/personal/:{user_id}',
    method: 'GET'
  },
  // 获取个人用户账户交易记录
  getUserAccountTranscations: {
    url: base + '/account/personal/:{user_id}/transactions/',
    method: 'GET'
  },
  // 充值个人用户帐户
  rechargeUserAccount: {
    url: base + '/account/personal/recharge',
    method: 'POST'
  },

  // ====================== 企业账户管理接口 ======================

  //根据关键字搜索企业账户列表
  getEnterpriseAccountsBySearch: {
    url: base + '/account/enterprise/search/',
    method: 'GET'
  },

  // 获取企业帐户明细
  getEnterpriseAccountDetail: {
    url: base + '/account/enterprise/:{user_id}',
    method: 'GET'
  },
  // 获取企业账户交易记录
  getEnterpriseAccountTranscations: {
    url: base + '/account/enterprise/:{user_id}/transactions/',
    method: 'GET'
  },
  // 充值企业帐户
  rechargeEnterpriseAccount: {
    url: base + '/account/enterprise/recharge',
    method: 'POST'
  },
}
