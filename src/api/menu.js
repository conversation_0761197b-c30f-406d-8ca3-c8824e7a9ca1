const base = ''
export default {
  // ====================== 人用户账户管理接口 ======================
  //根据关键字搜索菜单列表
  getMenusBySearch: {
    url: base + '/menu/search/',
    method: 'GET'
  },

  // 获取菜单明细
  getMenu: {
    url: base + '/menu/:{Menu_id}',
    method: 'GET'
  },

  // 新增菜单
  addMenu: {
    url: base + '/menu/',
    method: 'POST'
  },

  // 删除菜单
  deleteMenu: {
    url: base + '/menu/:{id}',
    method: 'DELETE'
  },

  // 更新菜单
  updateMenu: {
    url: base + '/menu/:{id}',
    method: 'PUT'
  },

  // 更新菜单状态（上架/下架）
  updateMenuStatus: {
    url: base + '/menu/status/:{id}',
    method: 'PUT'
  },

  getMenuWithContents: {
    url: base + '/menu/:{Menu_id}/with/contents',
    method: 'GET'
  },
}
