const base = ''
export default {
  // ====================== 规则管理接口 ======================
  //根据关键字搜索规则列表
  getRulesBySearch: {
    url: base + '/rule/search/',
    method: 'GET'
  },

  // 获取规则明细
  getRule: {
    url: base + '/rule/view/:{rule_id}',
    method: 'GET'
  },

  // 新增规则
  addRule: {
    url: base + '/rule/create/',
    method: 'POST'
  },

  // 删除规则
  deleteRule: {
    url: base + '/rule/delete/:{id}',
    method: 'DELETE'
  },

  // 更新规则
  updateRule: {
    url: base + '/rule/update/:{id}',
    method: 'PUT'
  },

  // 更新规则状态（启用/禁用）
  updateRuleStatus: {
    url: base + '/rule/status/:{id}',
    method: 'POST'
  },


  addRuleItem: {
    url: base + '/rule/item/create',
    method: 'POST'
  },

  delteRuleItem: {
    url: base + '/rule/item/delete/:{id}',
    method: 'DELETE'
  },

  updateRuleItem: {
    url: base + '/rule/item/update/:{id}',
    method: 'PUT'
  },

  viewRuleItem: {
    url: base + '/rule/item/view/:{id}',
    method: 'GET'
  },
}
