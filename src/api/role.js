const base = ''
export default {
// ====================== 角色管理接口 ======================
// 搜索角色列表
  getRoleList: {
    url: base + '/admin/roles/search/',
    method: 'GET'
  },

// 获取角色详情
  getRoleDetail: {
    url: base + '/admin/roles/:{id}',
    method: 'GET'
  },

// 获取角色权限列表
  getRolePermissions: {
    url: base + '/admin/roles/:{roleId}/permissions/',
    method: 'GET'
  },

// 新增角色
  addRole: {
    url: base + '/admin/roles/',
    method: 'POST'
  },

// 更新角色
  updateRole: {
    url: base + '/admin/roles/',
    method: 'PUT'
  },

// 删除角色
  deleteRole: {
    url: base + '/admin/roles/',
    method: 'DELETE'
  },

// 角色添加权限
  addRolePermission: {
    url: base + '/admin/roles/:{roleId}/permissions/:{permissionId}',
    method: 'POST'
  },

// 角色移除权限
  removeRolePermission: {
    url: base + '/admin/roles/:{roleId}/permissions/:{permissionId}',
    method: 'DELETE'
  },

// 更新角色状态（上架/下架）
  updateRoleStatus: {
    url: base + '/admin/roles/status/',
    method: 'POST'
  },
}
