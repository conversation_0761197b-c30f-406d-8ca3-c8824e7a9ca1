const base = ''
export default {
  // ====================== 人用户账户管理接口 ======================
  //根据关键字搜索产品分类分类列表
  getCategoryTree: {
    url: base + '/category/tree',
    method: 'GET'
  },

  getCategoriesBySearch: {
    url: base + '/category/search/',
    method: 'GET'
  },

  // 获取产品分类明细
  getCategory: {
    url: base + '/category/view/:{category_id}',
    method: 'GET'
  },

  // 新增产品分类
  addCategory: {
    url: base + '/category/create/',
    method: 'POST'
  },

  // 删除产品分类
  deleteCategory: {
    url: base + '/category/delete/:{id}',
    method: 'DELETE'
  },

  // 更新产品分类
  updateCategory: {
    url: base + '/category/update/:{id}',
    method: 'PUT'
  },

  // 更新产品分类状态（上架/下架）
  updateCategoryStatus: {
    url: base + '/category/status/:{id}',
    method: 'POST'
  },

  getCategoryProducts: {
    url: base + '/category/contents/search',
    method: 'GET'
  },

  // 添加产品分类内容
  addCategoryProduct: {
    url: base + '/category/add/contents/',
    method: 'POST'
  },

  // 添加产品分类内容
  removeCategoryProduct: {
    url: base + '/category/remove/contents/',
    method: 'POST'
  }
}
