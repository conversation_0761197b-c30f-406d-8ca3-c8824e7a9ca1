const base = ''
export default {
// ====================== 文章管理接口 ======================
// 获取文章列表
  getArticleList: {
    url: base + '/content/article/',
    method: 'GET'
  },

  //根据关键字搜索文章列表
  getArticleListBySearch: {
    url: base + '/content/article/search/',
    method: 'GET'
  },

// 获取文章详情
  getArticleDetail: {
    url: base + '/content/article/:{id}',
    method: 'GET'
  },
// 新增文章
  addArticle: {
    url: base + '/content/article/',
    method: 'POST'
  },
// 更新文章
  updateArticle: {
    url: base + '/content/article/',
    method: 'PUT'
  },
// 删除文章
  deleteArticle: {
    url: base + '/content/article/',
    method: 'DELETE'
  },
// 更新文章状态（上架/下架）
  updateArticleStatus: {
    url: base + '/content/status/',
    method: 'POST'
  },
}
