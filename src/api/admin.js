const base = ''
export default {
// ====================== 管理员管理接口 ======================
// 搜索管理员列表
  getAdminList: {
    url: base + '/admin/search/',
    method: 'GET'
  },

// 获取管理员详情
  getAdminDetail: {
    url: base + '/admin/:{id}',
    method: 'GET'
  },

// 获取管理员角色列表
  getAdminRoles: {
    url: base + '/admin/:{adminId}/roles/',
    method: 'GET'
  },

// 获取可分配角色列表
  getAvailableRoles: {
    url: base + '/admin/:{adminId}/available-roles/',
    method: 'GET'
  },

// 新增管理员
  addAdmin: {
    url: base + '/admin/',
    method: 'POST'
  },

// 更新管理员
  updateAdmin: {
    url: base + '/admin/',
    method: 'PUT'
  },

// 删除管理员
  deleteAdmin: {
    url: base + '/admin/',
    method: 'DELETE'
  },

// 管理员添加角色
  addAdminRole: {
    url: base + '/admin/:{adminId}/roles/:{roleId}',
    method: 'POST'
  },

// 管理员移除角色
  removeAdminRole: {
    url: base + '/admin/:{adminId}/roles/:{roleId}',
    method: 'DELETE'
  },

// 更新管理员状态（上架/下架）
  updateAdminStatus: {
    url: base + '/admin/status/',
    method: 'POST'
  },
}
