const base = ''
export default {
// ====================== 个人用户管理接口 ======================
// 获取个人用户列表
  getPersonalList: {
    url: base + '/user/personal/',
    method: 'GET'
  },

  //根据关键字搜索个人用户列表
  getPersonalListBySearch: {
    url: base + '/user/personal/search/',
    method: 'GET'
  },

  getPersonalListByName: {
    url: base + '/user/personal/search/name',
    method: 'GET'
  },

// 获取个人用户详情
  getPersonalDetail: {
    url: base + '/user/personal/:{id}',
    method: 'GET'
  },
// 新增个人用户
  addPersonal: {
    url: base + '/user/personal/',
    method: 'POST'
  },
// 更新个人用户
  updatePersonal: {
    url: base + '/user/personal/:{id}',
    method: 'PUT'
  },

  //根据人用户id获取绑定的企业列表
  getEnterpriseListByPersonalId: {
    url: base + '/user/enterprise/list/by/personal/:{id}',
    method: 'GET'
  },

  //根据人用户id获取绑定的企业列表
  addEnterprisePersonalRel: {
    url: base + '/user/enterprise/:{enterprise_id}/personal/:{personl_id}',
    method: 'POST'
  },

  //根据人用户id获取绑定的企业列表
  removeEnterprisePersonalRel: {
    url: base + '/user/enterprise/:{enterprise_id}/personal/:{personl_id}',
    method: 'DELETE'
  },

  //更新企业用户关系
  updateEnterprisePersonalRel: {
    url: base + '/user/enterprise/:{enterprise_id}/personal/:{user_id}',
    method: 'PUT'
  }

}
