const base = ''

export default {
  //======================== vegan-admin =========================
  // 获取企业列表
  getEnterpriseList: {
    url: base + '/user/enterprise/list',
    method: 'GET'
  },

  // 获取个人信息(ok)
  getUserInfo: {
    url: base + '/auth/user/info',
    method: 'GET'
  },

  // 发送验证码(ok)
  sendValidateCode: {
    url: base + '/user/send-validate-code',
    method: 'GET'
  },

  // 登录(ok)
  login: {
    url: base + '/user/valid/login',
    method: 'GET'
  },

  // 修改密码
  updateUserPasswd: {
    url: base + '/user/update/user/passwd',
    method: 'POST'
  },

  //======================== old-code =========================

  // 发送验证码(ok)
  NosendValidateCode: {
    url: base + '/send/validate/code',
    method: 'GET'
  },
  // 登录(ok)
  Nologin: {
    url: base + '/user/login',
    method: 'GET'
  },
  // 密码登录
  passLogin: {
    url: base + '/user/passwd/login',
    method: 'GET'
  },
  // 修改密码
  NoupdateUserPasswd: {
    url: base + '/update/user/passwd',
    method: 'POST'
  },
  // 获取个人信息(ok)
  NogetUserInfo: {
    url: base + '/get/user/info',
    method: 'GET'
  },
  // 修改用户信息(ok)
  updateUserInfo: {
    url: base + '/update/user/info',
    method: 'POST'
  },
  // 购买方列表查询
  getBuyerInfoList: {
    url: base + '/buyer/info/list',
    method: 'GET'
  },
  // 获取购买方信息
  getBuyerInfoDetail: {
    url: base + '/buyer/info/detail',
    method: 'GET'
  },
  // 购买方信息管理: 新增, 修改
  postBuyerInfo: {
    url: base + '/buyer/info/manage',
    method: 'POST'
  },
  // 库存列表查询
  getInventoryList: {
    url: base + '/inventory/list',
    method: 'GET'
  },
  // 库存详情
  getInventoryDetail: {
    url: base + '/inventory/detail',
    method: 'GET'
  },
  // 单个库存商品详情
  getProductManageDetail: {
    url: base + '/product/manage',
    method: 'GET'
  },
  // 单个库存商品的新增, 修改
  postProductManage: {
    url: base + '/product/manage',
    method: 'POST'
  },

  // 客户公司企业微信群二维码
  getWechatQRCode: {
    url: base + '/company/manage/qrcode',
    method: 'GET'
  },
  // 客户公司列表查询
  getCompanyList: {
    url: base + '/company/list',
    method: 'GET'
  },
  // 客户公司+已绑定的员工列表信息
  getSearchClients: {
    url: base + '/company/search/clients',
    method: 'GET'
  },

  // 签约企业列表查询
  getContractCompanyManage: {
    url: base + '/contract/company/manage',
    method: 'GET'
  },
  // 签约企业管理新增, 修改
  postContractCompanyManage: {
    url: base + '/contract/company/manage',
    method: 'POST'
  },


  // 业务员列表查询
  getOperatorManager: {
    url: base + '/operator/manager',
    method: 'GET'
  },
  // 业务员管理新增, 修改
  postOperatorManager: {
    url: base + '/operator/manager',
    method: 'POST'
  },
  // 业务员详情(列表直接获取)
  getOperatorDetail: {
    url: base + '/operator/detail',
    method: 'GET'
  },
  // 客户员工列表查询
  getClientManager: {
    url: base + '/client/manager',
    method: 'GET'
  },
  // 客户员工列表查询(排除禁用员工)
  getCompanyAssociateUsers: {
    url: base + '/company/associate/users',
    method: 'GET'
  },
  // 客户员工修改
  postClientManager: {
    url: base + '/client/manager',
    method: 'POST'
  },
  // 客户员工+客户公司列表
  getClientSearchCompany: {
    url: base + '/client/search/company',
    method: 'GET'
  },
  // 客户公司详情
  getCompanyManage: {
    url: base + '/company/manage',
    method: 'GET'
  },
  // 客户公司管理: 新增, 修改
  postCompanyManage: {
    url: base + '/company/manage',
    method: 'POST'
  },
  // 客户公司绑定客户员工
  postCompanyBindClient: {
    url: base + '/company/bind/client',
    method: 'POST'
  },
  // 发票列表
  getInvoiceTaskManage: {
    url: base + '/invoice/task/manage',
    method: 'GET'
  },
  // 发票详情查询
  getInvoiceTaskDetail: {
    url: base + '/invoice/task/detail',
    method: 'GET'
  },
  // 发票详情，删除自主开票"申请修改"数据
  getInvoiceTaskProactiveDelete: {
    url: base + '/invoice/task/proactive/delete',
    method: 'GET'
  },
  // 销售方项目列表查询
  getSalesProjectManage: {
    url: base + '/company/search/sales_project',
    method: 'GET'
  },
  // 根据公司id, 获取对应的销售项目列表
  getCompanySalesProjectManage: {
    url: base + '/sales/project/manage',
    method: 'GET'
  },
  // 修改扫码开票销售项目开关
  postScanState: {
    url: base + '/scan/update/scan_state',
    method: 'POST'
  },
  // 客户项目名称库
  bingSalesProject: {
    url: base + '/company/bind/sales_project',
    method: 'POST'
  },
  // 发票管理: 新增
  postInvoiceTaskManage: {
    url: base + '/invoice/task/manage',
    method: 'POST'
  },
  // 发票管理: 修改
  postInvoiceTaskUpdate: {
    url: base + '/invoice/task/update',
    method: 'POST'
  },
  // 发票批量开具：新增
  postInvoiceBatchTask: {
    url: base + '/invoice/batch/manage',
    method: 'POST'
  },
  // 发票批量列表
  getBatchTaskManage: {
    url: base + '/invoice/batch/manage',
    method: 'GET'
  },
  // 发票批量列表明细
  getBatchTaskDetails: {
    url: base + '/invoice/batch/details',
    method: 'GET'
  },
  // 申请修改发票，预览
  getInvoiceEditPreview: {
    url: base + '/invoice/edit/preview',
    method: 'GET'
  },
  // 申请修改发票，预览
  postInvoiceEditPreview: {
    url: base + '/invoice/edit/preview',
    method: 'POST'
  },
  // 发票审批
  postInvoiceOperator: {
    url: base + '/invoice/operator',
    method: 'POST'
  },
  // 公司地址
  getAddressList: {
    url: base + '/company/address/manage',
    method: 'GET'
  },
  // 修改公司地址
  postAddressManage: {
    url: base + '/company/address/manage',
    method: 'POST'
  },
  // /company/email/manage
  // 邮箱地址
  getEmailList: {
    url: base + '/company/email/manage',
    method: 'GET'
  },
  // 公司邮箱管理: 新增, 修改
  postEmailManage: {
    url: base + '/company/email/manage',
    method: 'POST'
  },
  // 根据用户id获取关联公司列表
  getAssociateCompanies: {
    url: base + '/user/associate/companies',
    method: 'GET'
  },
  // 根据用户id获取关联购买方列表
  getAssociateBuyers: {
    url: base + '/user/associate/buyers',
    method: 'GET'
  },
  // 地区编号列表
  getAreaCodeList: {
    url: base + '/area/code/list',
    method: 'GET'
  },
  // 导出发票
  exportInvoiceTask: {
    url: base + '/invoice/task/export',
    method: 'GET',
    responseType: 'blob',
    withoutErrorMessage: true
  },
  // 顺丰接口: 通过运单号查询路由信息
  expressService: {
    url: base + '/sf/express/service',
    method: 'GET'
  },
  // 企查查: 通过企业名称模糊搜索匹配企业，展示前5条记录，返回企业名称、匹配原因等信息。
  qccCompanySearch: {
    url: base + '/qcc/company/search',
    method: 'GET'
  },
  // 天眼查接口: 获取公司信息
  tycCompanyInfo: {
    url: base + '/tyc/company/info',
    method: 'GET'
  },
  // 电子发票：绑定电子开票服务
  companyFpCaptcha: {
    url: base + '/company/fp/captcha',
    method: 'GET'
  },
  // 电子发票：添加开票员-提交验证码
  fpSmsCaptcha: {
    url: base + '/company/fp/binding',
    method: 'GET'
  },
  // 销售项目原表列表查询
  getProjectOrginSearch: {
    url: base + '/sales/project/orgin/search',
    method: 'GET'
  },
  // 新公司关联销售项目管理: 新增, 修改, 删除
  newSalesProjectManage: {
    url: base + '/sales/project/manage',
    method: 'POST'
  },
  // 公司关联销售项目原表详情
  newSalesProjectDetail: {
    url: base + '/sales/project/detail',
    method: 'GET'
  },
  // 税率列表
  getTaxRateOptionList: {
    url: base + '/tax/rate/option/list',
    method: 'GET'
  },
  // 删除发票
  postInvoiceDelete: {
    url: base + '/invoice/task/bulk/delete',
    method: 'POST'
  },
  // 激活企业税局
  getAutoCaptcha: {
    url: base + '/auto/task/insert',
    method: 'POST'
  },
  // 发送微信群
  getGeneratePreview: {
    url: base + '/invoice/generate/preview',
    method: 'GET'
  },
  // 发送微信群，进行自主开票
  getGeneratePreviewInvoice: {
    url: base + '/invoice/proactive/preview',
    method: 'GET'
  },
  // 销售项拖拽
  postSalesProjectDragSort: {
    url: base + '/sales/project/drag/sort',
    method: 'POST'
  },
  // 获取微信群
  getWorkWxGroup: {
    url: base + '/work/wx/group',
    method: 'GET'
  },
  // 获取微信群
  getWorkWxGroupBinding: {
    url: base + '/work/wx/group/binding',
    method: 'GET'
  },
  // 同步税务局购买方数据
  postSyncAutoBuyers: {
    url: base + '/auto/task/insert',
    method: 'POST'
  },
  // 同步税局销售项数据
  postSyncAutoProjects: {
    url: base + '/auto/task/insert',
    method: 'POST'
  },
  // 下载销售项Excel表格模版
  downloadSalesProjectTemplate: {
    url: base + '/bulk/sales/project/template/download',
    method: 'GET',
    responseType: 'blob',
    withoutErrorMessage: true
  },
  // 上传销售项excel表格数据
  uploadSalesProjectTemplate: {
    url: base + '/bulk/sales/project/template/upload',
    method: 'POST'
  },
  // 任务中心 - 获取任务列表
  GetTaskList: {
    url: base + '/auto/task/list',
    method: 'GET'
  },
  // 获取特殊发票列表
  GetSpecialInvoiceList: {
    url: base + '/special/invoice/list',
    method: 'GET'
  },
  // 获取获取 省-市-区 的数据
  GetMunicipalities: {
    url: base + '/province/municipalities/data',
    method: 'GET'
  },
  // 获取运输类型列表
  GetTransportTypeList: {
    url: base + '/transport/type/list',
    method: 'GET'
  },
  // 获取面积单位列表
  GetAreaUnitList: {
    url: base + '/area/unit/list',
    method: 'GET'
  },
  // 冲红发票：开具
  PostInvoiceRedTask: {
    url: base + '/invoice/red/task/manage',
    method: 'POST'
  },
  // 冲红发票：查询
  GetInvoiceRedTaskDetail: {
    url: base + '/invoice/red/task/detail',
    method: 'GET'
  },
  // 开票时间
  GetFaceTaskList: {
    url: base + '/auto/face/task/list',
    method: 'GET'
  },
  // 进行人脸识别
  PostTaskInsert: {
    url: base + '/auto/task/insert',
    method: 'POST'
  },
  // 获取优惠政策及简易计税（否），是否选择普通零税率（否）税率
  GetProjectSearchTaxRate: {
    url: base + '/project/search/tax/rate',
    method: 'GET'
  },
  // 获取客户公司关联的购买方列表-24-06-18
  GetCompanyAssociateBuyers: {
    url: base + '/company/associate/buyers',
    method: 'GET'
  },
  // 任务中心-删除任务
  PostDelTask: {
    url: base + '/auto/task/delete',
    method: 'POST'
  },
  // 任务中心-优先任务
  PostTaskPriority: {
    url: base + '/auto/task/priority',
    method: 'POST'
  },
  // 实时查询企业累计金额
  PostCurrentQuota: {
    url: base + '/auto/quota/task/query',
    method: 'POST'
  },
  // 查询企业累计金额
  GetTaskCompanyAmount: {
    url: base + '/auto/task/company/amount',
    method: 'GET'
  },
  // 客户公司列表查询-24-06-25
  GetListsCompany: {
    url: base + '/company/list',
    method: 'GET'
  },
  // 销售方 批量绑定/解绑 业务员
  PostCompanyBindClientBulk: {
    url: base + '/company/bind/client/bulk',
    method: 'POST'
  },
  // 开票中-优先任务
  PostTaskPriorityInvoice: {
    url: base + '/auto/task/priority/invoice',
    method: 'POST'
  },
  // 扫码开票-修改限额
  QuickScanQuota: {
    url: base + '/quick/scan/quota',
    method: 'POST'
  },
  // 客户联系人列表查询
  getQuickScanUserManager: {
    url: base + '/quick/scan/user/manager',
    method: 'GET'
  },
  // 获取扫码开票列表
  getScanInvoiceList: {
    url: base + '/scan/invoice/list',
    method: 'GET'
  },
  postQuickScanUserManager: {
    url: base + '/quick/scan/user/manager',
    method: 'POST'
  },
  getAdInfo: {
    url: base + '/ad/company_info',
    method: 'GET'
  },
  postAdInfo: {
    url: base + '/ad/company_info',
    method: 'POST'
  },
  deleteAdImg: {
    url: base + '/ad/delete/img',
    method: 'POST'
  },
  uploadAdImg: {
    url: base + '/ad/upload/img',
    method: 'POST'
  }
}
