const base = ''
export default {
  // ====================== 价格策略管理接口 ======================
  //根据关键字搜索价格策略列表
  getOrderGiftStrategiesBySearch: {
    url: base + '/gift/order-gift-rules',
    method: 'GET'
  },

  // 获取价格策略明细
  getOrderGiftStrategy: {
    url: base + '/gift/order-gift-rules/:{id}',
    method: 'GET'
  },

  // 新增价格策略
  addOrderGiftStrategy: {
    url: base + '/gift/order-gift-rules',
    method: 'POST'
  },

  // 删除价格策略
  deleteOrderGiftStrategy: {
    url: base + '/gift/order-gift-rules/:{id}',
    method: 'DELETE'
  },

  // 更新价格策略
  updateOrderGiftStrategy: {
    url: base + '/gift/order-gift-rules/:{id}',
    method: 'PUT'
  },

  // 更新价格策略状态（启用/禁用）
  updateOrderGiftStrategyStatus: {
    url: base + '/gift/order-gift-rules/:{id}/status/',
    method: 'PUT'
  },
}
