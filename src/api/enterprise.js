const base = ''
export default {
// ====================== 个人用户管理接口 ======================
// 获取个人用户列表
  getEnterpriseList: {
    url: base + '/user/enterprise/',
    method: 'GET'
  },

  //根据关键字搜索个人用户列表
  getEnterpriseListBySearch: {
    url: base + '/user/enterprise/search/',
    method: 'GET'
  },

// 获取个人用户详情
  getEnterpriseDetail: {
    url: base + '/user/enterprise/:{id}',
    method: 'GET'
  },
// 新增个人用户
  addEnterprise: {
    url: base + '/user/enterprise/',
    method: 'POST'
  },
// 更新个人用户
  updateEnterprise: {
    url: base + '/user/enterprise/:{id}',
    method: 'PUT'
  },

  //根据人用户id获取绑定的企业列表
  getPersonalListByEnterpriseId: {
    url: base + '/user/personal/list/by/enterprise/:{id}',
    method: 'GET'
  },

  //根据人用户id获取绑定的企业列表
  getEnterpriseNameListByKeyword: {
    url: base + '/user/enterprise/name-list/',
    method: 'GET'
  },
}
