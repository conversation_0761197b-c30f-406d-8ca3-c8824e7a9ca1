const base = ''
export default {
  // ====================== 价格策略管理接口 ======================
  //根据关键字搜索价格策略列表
  getPricingStrategiesBySearch: {
    url: base + '/pricing/search/',
    method: 'GET'
  },

  // 获取价格策略明细
  getPricingStrategy: {
    url: base + '/pricing/view/:{id}',
    method: 'GET'
  },

  // 新增价格策略
  addPricingStrategy: {
    url: base + '/pricing/create/',
    method: 'POST'
  },

  // 删除价格策略
  deletePricingStrategy: {
    url: base + '/pricing/delete/:{id}',
    method: 'DELETE'
  },

  // 更新价格策略
  updatePricingStrategy: {
    url: base + '/pricing/update/:{id}',
    method: 'PUT'
  },

  // 更新价格策略状态（启用/禁用）
  updatePricingStrategyStatus: {
    url: base + '/pricing/status/:{id}',
    method: 'POST'
  },
}
