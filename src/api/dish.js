const base = ''
export default {
// ====================== 菜品管理接口 ======================
// 获取菜品列表
  getDishList: {
    url: base + '/content/dish/',
    method: 'GET'
  },

  //根据关键字搜索菜品列表
  getDishListBySearch: {
    url: base + '/content/dish/search/',
    method: 'GET'
  },

// 获取菜品详情
  getDishDetail: {
    url: base + '/content/dish/:{id}',
    method: 'GET'
  },
// 新增菜品
  addDish: {
    url: base + '/content/dish/',
    method: 'POST'
  },
// 更新菜品
  updateDish: {
    url: base + '/content/dish/',
    method: 'PUT'
  },
// 删除菜品
  deleteDish: {
    url: base + '/content/dish/',
    method: 'DELETE'
  },
// 更新菜品状态（上架/下架）
  updateDishStatus: {
    url: base + '/content/status/',
    method: 'POST'
  },

// 获取菜品分类列表
  getDishCategoryList: {
    url: base + '/dish/category/list',
    method: 'GET'
  },
// 新增菜品分类
  addDishCategory: {
    url: base + '/dish/category/add',
    method: 'POST'
  },
// 更新菜品分类
  updateDishCategory: {
    url: base + '/dish/category/update',
    method: 'POST'
  },
// 删除菜品分类
  deleteDishCategory: {
    url: base + '/dish/category/delete',
    method: 'POST'
  }
}
