const base = ''
export default {
  // ====================== 人用户账户管理接口 ======================
  //根据关键字搜索产品列表
  getProductsBySearch: {
    url: base + '/product/search/',
    method: 'GET'
  },

  // 获取产品明细
  getProduct: {
    url: base + '/product/view/:{product_id}',
    method: 'GET'
  },

  // 新增产品
  addProduct: {
    url: base + '/product/create/',
    method: 'POST'
  },

  // 删除产品
  deleteProduct: {
    url: base + '/product/delete/:{id}',
    method: 'DELETE'
  },

  // 更新产品
  updateProduct: {
    url: base + '/product/update',
    method: 'PUT'
  },

  // 更新产品状态（上架/下架）
  updateProductStatus: {
    url: base + '/product/status/:{id}',
    method: 'POST'
  },


  getProductContents: {
    url: base + '/product/contents/search',
    method: 'GET'
  },

  // 添加产品内容
  addProductContent: {
    url: base + '/product/add/contents/',
    method: 'POST'
  },

  // 添加产品内容
  removeProductContent: {
    url: base + '/product/remove/contents/',
    method: 'POST'
  },

  // 添加产品标签
  addProductTag: {
    url: base + '/product/tag/:{contentId}',
    method: 'POST'
  },

  // 添加产品标签
  removeProductTag: {
    url: base + '/product/:{productId}/tag/:{contentId}/remove',
    method: 'POST'
  },


  getProductRules: {
    url: base + '/product/rules/search',
    method: 'GET'
  },


  // 添加产品规则
  addProductRule: {
    url: base + '/product/add/rules/',
    method: 'POST'
  },

  // 添加产品规则
  removeProductRule: {
    url: base + '/product/remove/rules/',
    method: 'POST'
  },


  getProductPricings: {
    url: base + '/product/pricing-strategies/search',
    method: 'GET'
  },


  // 添加产品价格策略
  addProductPricing: {
    url: base + '/product/add/pricing-strategies/',
    method: 'POST'
  },

  // 添加产品价格策略
  removeProductPricing: {
    url: base + '/product/remove/pricing-strategies/',
    method: 'POST'
  },

  // 获
  getProductWithDiningRules:{
    url: base + '/product/dining-rules/:{product_name}',
    method: 'GET'
  },

  getProductWithDiningRulesById:{
    url: base + '/product/dining-rules-by-id/:{product_id}',
    method: 'GET'
  },

  getProductsByName: {
    url: base + '/product/search/name',
      method: 'GET'
  },

  // 获取活跃产品列表
  getActiveProducts: {
    url: base + '/product/active/',
    method: 'GET'
  }
}
