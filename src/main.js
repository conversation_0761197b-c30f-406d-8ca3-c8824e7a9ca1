import Vue from 'vue'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import Element<PERSON> from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/css/iconfont.css'

import '@/styles/index.less' // global css

import App from './App'
import store from './store'
import router from './router'
import * as filters from './filters' // global filters
import './directive' // global directive

import '@/icons' // icon
import '@/permission' // permission control

Vue.use(ElementUI, {
  size: 'small' // small medium
})
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})
Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})
