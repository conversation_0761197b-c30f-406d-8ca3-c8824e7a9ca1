"""modify product mealtype and add virtual product

Revision ID: 2a28bd5327b2
Revises: fe9201ee715a
Create Date: 2025-09-08 22:03:16.656169

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '2a28bd5327b2'
down_revision: Union[str, None] = 'fe9201ee715a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # 添加VIRTUAL到ProductType枚举
    op.execute("ALTER TABLE products MODIFY COLUMN type ENUM('PRODUCT', 'DIRECT_SALE', 'RESERVATION', 'VIRTUAL') NOT NULL COMMENT '产品类型'")


    op.create_table('virtual_products',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('object_id', sa.Integer(), nullable=False, comment='对象ID'),
    sa.Column('object_type', sa.Enum('COUPON_BATCH', name='ordertype'), nullable=False, comment='对象类型'),
    sa.ForeignKeyConstraint(['id'], ['products.id'], name=op.f('fk_virtual_products_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_virtual_products'))
    )
    op.alter_column('products', 'meal_type',
               existing_type=mysql.ENUM('BUSINESS', 'BUFFET', 'DINE_IN', collation='utf8mb4_unicode_ci'),
               type_=sa.Enum('BUSINESS', 'BUFFET', 'COUPON', name='mealtype'),
               existing_comment='餐食类型：商务餐/自助餐/现场点餐',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('products', 'meal_type',
               existing_type=sa.Enum('BUSINESS', 'BUFFET', 'COUPON', name='mealtype'),
               type_=mysql.ENUM('BUSINESS', 'BUFFET', 'DINE_IN', collation='utf8mb4_unicode_ci'),
               existing_comment='餐食类型：商务餐/自助餐/现场点餐',
               existing_nullable=True)
    op.drop_table('virtual_products')

    # 移除VIRTUAL从ProductType枚举（回退到原来的值）
    op.execute("ALTER TABLE products MODIFY COLUMN type ENUM('PRODUCT', 'DIRECT_SALE', 'RESERVATION') NOT NULL COMMENT '产品类型'")
    # ### end Alembic commands ###
