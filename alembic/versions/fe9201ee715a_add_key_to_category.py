"""add key to category

Revision ID: fe9201ee715a
Revises: 3cd396b6d907
Create Date: 2025-09-07 22:36:39.743742

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fe9201ee715a'
down_revision: Union[str, None] = '3cd396b6d907'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_categories', sa.Column('key', sa.String(length=50), nullable=True, comment='分类键'))
    # ### end Alembic commands ###
    
    # 数据迁移
    op.execute("update product_categories set parent_id = 1 where id <> 1")
    op.execute("update product_categories set name = '商务餐', `key` = 'business', status = 'ACTIVE' where id = 1")


def downgrade() -> None:
    """Downgrade schema."""
    # 数据回滚
    op.execute("update product_categories set parent_id = null where id <> 1")
    op.execute("update product_categories set name = '所有菜品', status = 'INACTIVE' where id = 1")
    
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product_categories', 'key')
    # ### end Alembic commands ###
