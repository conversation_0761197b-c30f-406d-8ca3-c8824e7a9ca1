# 项目基本信息
PROJECT_NAME=乙禾素食管理系统
API_V1_STR=/api/v1
CALLBACK_STR=/callback

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# 数据库配置
SQLALCHEMY_DATABASE_URI="mysql+pymysql://root:123456@127.0.0.1/yh_vegan_dev_stage"

# CORS 配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# 文件上传配置
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=["jpg","jpeg","png","gif","pdf","doc","docx"]

# 微信小程序配置
WECHAT_APPID=wxc58a034f610866c5
WECHAT_SECRET=d16ceeb0717dd9a0d2349411edbf6e04
WECHAT_TOKEN=0GOXA6Pg7JOUfi
WECHAT_OFFICIAL_DOMAIN="http://reqforwardmapping.yiheship.com"

# 设置为你的服务器域名
BASE_URL=http://127.0.0.1:8000

# 微信支付配置
# 商户号
WXPAY_MCHID=1715245681
# 商户证书序列号
WXPAY_SERIAL_NO=29C4F222BD6C7BC7BA3378D208F45781CF6F6DCC
# API V3密钥
WXPAY_API_V3_KEY=TBXzlcaQmwYSLw72HCR512VcbFQuAccG
# 商户私钥路径
WXPAY_PRIVATE_KEY_PATH=certs/wechat_yh/apiclient_key.pem

# Redis配置
REDIS_URL=redis://:123321@localhost:6379/0
# 验证码有效期（秒）
SMS_TIMEOUT=300

# sms 腾讯短信的配置信息
SMS_SECRET_ID = 'AKIDiMn0zeGh1CD2f8QZWHqCOP7ZqvnU1LTU'
SMS_SECRET_KEY = 'tZ2IK8j7JI7kAOPEqFRQDnAKH2av41lK'
SMS_SMSSDK_APPID = "1400844963"
SMS_SIGN_NAME = "深圳一玄科技"
SMS_TEMPLATE_ID = "1886886"

ENABLE_STATIC_FILES = True


# 小程序前端配置
RULE_ITEMS_MEAL_TYPE = {"BREAKFAST": 0, "LUNCH": 1, "AFTERNOON_TEA": 0, "DINNER": 1, "NIGHT_SNACK": 0}


EMAIL_SMTP_SERVER = smtp.163.com
EMAIL_SMTP_PORT = 25
EMAIL_USERNAME = "<EMAIL>"
EMAIL_PASSWORD = LEcu2iTzNT97hMAE
EMAIL_USE_TLS = True
EMAIL_SENDER_NAME = 乙禾素
EMAIL_SENDER_EMAIL = "<EMAIL>"

# 个人固定支付金额配置（单位：元）
PERSONAL_PAYMENT_FIXED_AMOUNT = 3.00

# 企业自助晚餐开放企业列表
OPEN_ENTERPRISE_LIST = ["广州乙禾素饮食文化有限公司","广州乙禾航运风险咨询有限公司"]

# 钉钉配置
DINGTALK_WEBHOOK_URL = https://oapi.dingtalk.com/robot/send?access_token=a6a9414ff79a1396a5890388c9aadf6411cc88343f70b4d9151002d585bc1506
DINGTALK_SECRET = 
DINGTALK_AT_MOBILES = 
DINGTALK_AT_USER_IDS = 
DINGTALK_IS_AT_ALL = True

ENABLE_NOTIFICATION = False

