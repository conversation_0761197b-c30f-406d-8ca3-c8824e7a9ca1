{"name": "invoice-admin", "version": "1.3.0", "description": "A vue admin about shop", "author": "htz", "scripts": {"dev": "vue-cli-service serve", "build:uat": "vue-cli-service build --mode uat", "build:prod": "vue-cli-service build", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"axios": "0.18.1", "core-js": "3.6.5", "decimal.js": "^10.4.3", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "js-cookie": "2.2.0", "js-sha256": "^0.9.0", "less": "^3.9.0", "less-loader": "^5.0.0", "moment": "^2.30.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "number-precision": "^1.6.0", "path-to-regexp": "2.4.0", "qrcode": "^1.5.4", "sortablejs": "^1.15.0", "vue": "2.6.10", "vue-json-viewer": "^2.2.22", "vue-router": "3.0.6", "vuex": "3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10", "webpack": "^4.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}