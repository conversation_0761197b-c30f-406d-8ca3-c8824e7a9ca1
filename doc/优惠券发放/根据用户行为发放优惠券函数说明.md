# 根据用户事件发放优惠券函数说明

## 一、函数名

distribute_coupons_by_user_event

## 二、参数

user_evnet:UserEvent=None

## 三、计算过程

### 1. 根据传入的用户行为事件user_evnet，通过“用户行为函数（user_behavior_func）”计算，获取所有符合发放条件的“优惠券发放规则（coupon_distribution_rules）”


### 2. 根据上一步所得到的优惠券发放规则，通过“用户特征函数（user_feature_func）”（目前暂不实现，仅作为stub函数提供）计算，进一步过滤出符合发放条件的优惠券发放规则

### 3. 循环迭代通过上一步获得的所有的优惠券发放规则，进一步检查改规则对应的发放批次中的发放以及获取要求，并完成对该批次优惠券的发放

- 循环迭代所有得到优惠券发放规则
- 获取该规则对应的发放内容（distribution_content），包括优惠券批次id（coupon_batch_id）以及发放的数量（quantity）
- 根据优惠券批次id，获取对应的优惠券批次对象
- 根据优惠券批次对象中所设置的发放获取约束规则做进一步进行检查（检查规则在第五部分进一步描述）
- 根根据检查结果，向该用户发放优惠券，即生成对应的优惠券使用记录

### 4. 最后返回发放成功的优惠券列表

## 四、用户行为函数说明

- 用户行为函数存在于“优惠券发放规则”对象的user_behavior_func，用于检查当前用户行为是否符合当前优惠券发放条件
- 可以有0到多个
- 通过用户行为函数进行可适用优惠券发放规则计算时，根据用户行为事件中的action，检查user_behavior_func中是否有对应的键存在，如果不存在则检查失败，返回false
- 如果存在，则根据该键值下对应的func_name，在/app/service/user_behavior_func.py中加载对应的函数进行计算
- /app/service/user_behavior_func.py中的的函数传入参数皆为（user_event,params）,user_event为传入的用户行为事件对象，params为user_behavior_func中对应的params


## 五、优惠券批次中的发放获取约束检查规则说明

- 发放渠道检查：当前用户行为是否包含在发放渠道当中distribution_channels，用户行为与发放渠道包含一一对应关系，关系如下：
用户行为"viewed"对应"view_activity", 
用户行为"shared"对应"share_activity", 
用户行为"paid"对应"purchase", 
用户行为"registered"对应"new_user"
- 发放数量检查：检查周期时间内，该批次发放的总数量不超过发放数量限制，发放周期（distribution_cycle），发放数量限制（distribution_quantity）当前领取的数量+该批次在本周期内已经发放的数量> distribution_quantity 则只允许发放distribution_quantity 与本周期内已经发放的数量的差值部分。
- 获取时间检查：当前时间是否在获取时间范围内（receive_start_time、receive_end_time）
- 获取数量检查：检查周期时间内，该用户领取的总数量是否超过周期领取数量限制，领取周期（receive_cycle），领取数量限制（receive_quantity）当前领取的数量+在本周期内该用户已领取的数量> receive_quantity 则只允许领取receive_quantity 与本周期内该用户已领取的数量的差值部分。


