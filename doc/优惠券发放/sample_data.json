优惠券发放规则数据样例：
{
    "name": "用户分享页面获得优惠",
    "description": "用户分享页面获得优惠",
    "type": "user_behavior",
    "user_feature_func": {},
    "user_behavior_func": {
        "shared": {
            "name": "用户分享活动页面",
            "params": {
                "content_ids": [
                    2
                ]
            },
            "func_name": "handle_shared"
        }
    },
    "distribution_content": [
        {
            "coupon_batch_id": 16,
            "quantity": 1
        }
    ],
    "status": 1,
    "execution_time": "",
    "id": 3,
    "created_at": "2025-09-07 10:47:24",
    "updated_at": "2025-09-07 10:47:24"
}


优惠券批次数据样例；
{   
    "id": 16,
    "name": "自助餐8折促销",
    "description": "自助餐8折促销",
    "batch_number": 1,
    "quantity": 99,
    "start_time": "2025-09-01T15:20:37",
    "end_time": "2026-09-01T15:20:37",
    "valid_duration": 240,
    "coupon_id": 14,
    "status": 1,
    "distribution_channels": [
        "view_activity",
        "share_activity"
    ],
    "distribution_quantity": 100,
    "distribution_cycle": "per_day",
    "receive_quantity": 1,
    "receive_cycle": "per_day",
    "receive_start_time": "2025-09-01T15:20:37",
    "receive_end_time": "2026-09-03T15:20:37",
    "created_at": "2025-09-01T15:22:18",
    "updated_at": "2025-09-01T15:38:24"
}
