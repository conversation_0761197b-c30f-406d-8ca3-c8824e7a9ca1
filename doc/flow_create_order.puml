@startuml create_order_flow
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 12

title create_order 函数流程图

start

:接收参数: session, user_id, products, rule_data, source, selected_coupons, only_pricing;

:验证用户是否存在;
if (用户存在?) then (是)
else (否)
  :抛出异常: 用户ID不存在;
  stop
endif

:初始化变量;
note right
- order_items = []
- total_amount = 0.0
- total_payable_amount = 0.0
- temp_reservation_requests = []
- add_price = 3.0 (如果source == "admin_onsite")
end note

:开始遍历产品列表;

while (还有产品?) is (是)
  :获取当前产品信息;
  note right
  - product_id
  - quantity
  - reservation_requests (可选)
  end note
  
  :验证产品有效性;
  if (产品存在?) then (是)
  else (否)
    :抛出异常: 产品ID不存在;
    stop
  endif
  
  if (产品状态为激活?) then (是)
  else (否)
    :抛出异常: 产品无效;
    stop
  endif
  
  :检查库存;
  if (库存充足?) then (是)
  else (否)
    :抛出异常: 库存不足;
    stop
  endif
  
  :计算产品价格;
  note right
  - price = product.price
  - subtotal = price * quantity
  end note
  
  :调用产品级别计价服务;
  :pricing_service.product_pricing();
  
  :创建订单项;
  note right
  - product_id, quantity
  - price, subtotal
  - final_price, payable_amount
  - pricing_remark
  end note
  
  :添加到订单项列表;
  :累加总金额;
  
  :检查是否有预订请求;
  if (有预订请求?) then (是)
    if (产品类型支持预订?) then (是)
      :存储预订请求信息到临时列表;
    else (否)
      :抛出异常: 产品不支持预订;
      stop
    endif
  endif
  
endwhile (否)

:调用订单级别计价服务;
:pricing_service.order_pricing();

if (订单级别优惠为0?) then (是)
  :使用订单项应付金额之和;
endif

:检查是否为商务餐预订订单;
if (rule_data存在?) then (是)
  :商务餐凑整处理;
  note right
  - 计算目标金额
  - 添加凑整产品 (ID: 100或200)
  - 调整订单金额
  end note
endif

:生成订单号;
:OrderService.generate_order_no();

:创建订单对象;
note right
- user_id, order_no
- status: PENDING
- payment_status: UNPAID
- total_amount, payable_amount
- items: order_items
end note

:保存订单到数据库;
:order_dao.create();

:获取保存后的订单项;

:处理预订请求;
if (商务餐预订?) then (是)
  :创建商务餐预订请求;
  note right
  - 联系人信息
  - 用餐时间
  - 人数等
  end note
else (否)
  :处理自助餐预订请求;
  while (还有预订请求?) is (是)
    :创建预订请求;
    :reservation_request_dao.create();
  endwhile (否)
endif

:返回创建的订单;
stop

@enduml

