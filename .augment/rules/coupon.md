---
type: "manual"
---

## coupon 

coupon表示优惠券，其中包含了使用的条件规则（condition_开头属性）和优惠计算应用的约束规则（apply_开头属性）
以及使用的限制约束(usage_开头属性)，同时通过type包含三个子类（分别是折扣券：discount_coupon,现金券：cash_coupon,满减券：full_reduction_coupon）

### coupon的使用的条件规则

- coupon.condition_scope：包含产品与订单两个类型，表示需要在订单还是产品上满足条件
- coupon.condition_objects：包含了产能id与数量的对应组合，表示在订单中需要存在包含了与之对应的产品id和数量，该优惠券才可使用
- coupon.condition_amount：表示订单总金额需要大于该条件金额，该金额优惠券才可使用。

### coupon的使用的限制约束

- coupon.usage_cycle：表示使用周期约束，包含每订单、每日、每周、每月、每年，除每订单外，周期时间内使用该类优惠券的不能超过限定的次数
- coupon.usage_quantity：表示该类优惠券周期内可使用的次数

### coupon的计算应用的约束

- coupon.apply_scope：表示优惠的计算，是作用于整张订单，还是指定的产品
- coupon.apply_objects：表示指定作用的产品对象和数量，举例：apply_objects=[{"1": 3}],则表示作用于产品id为1的3件产品，
假设产品1价格为10元，当单中包含5件产品1，那原总金额=10*5=50元，当使用5折优惠券时，应付金额为=10*3*0.5+10*2=35元

### coupon的使用互斥

- coupon.mutual_exclusive_rules：为一优惠券表id列表，如[1,3,5],表示该优惠券与该列表中的优惠券不能同时使用

### coupon优惠券的子类

- cash_coupon：现金券，对作用对象范围（订单或者指定的产品范围）直接扣减对应金额，amount为扣减金额值，例如amount=20,订单金额=100时，优惠后最终应付金额=100-20=80
- discount_coupons：折扣券，对作用对象范围（订单或者指定的产品范围）的应付金额乘以特定比例，discount_rate比例值， 不能大于1，1表示原价，但优惠金额不能大于max_discount，小于min_amount，但当这两个值为0时则忽略直接以discount_rate来计算。例如：当discount_rate=0.5，max_discount=20，订单金额=100时，优惠后应付金额为80，当订单金额=30时，优惠后应付金额为15
- full_reduction_coupon：满减券，full_amount表示满足金额，作用对象当金额大于满足金额，reduction_amount表示对作用对象扣减对金额值，例如：full_amount=100，reduction_amount=20，当订单金额=130时，则最终应付金额=130-20=110


## coupon_batches

coupon_batches表示优惠券的发放批次，用以定义描述和管理对优惠券的发放行为，包含了对应的优惠券id（coupon_id），发券的数量coupon_batches。quantity，发放的约束规则（distribution_开头的属性），获取的约束规则（receive_开头的属性）

### coupon_batches对优惠券有效期的定义

对优惠券有效期的定义coupon_batches.start_time,coupon_batches.end_time,优惠券只有在有效期内才允许使用。coupon_batches.valid_duration,则表示从领取时开始多少小时内有效

### coupon_batches对发放的约束规则

- distribution_channels: 表示允许可发放的渠道，一个字符串数组
- distribution_cycle: 优惠券发放的周期，表示在周期内只允许发放指定数量的优惠券，如该批券的数量为1000，而每周只允许发放100张
- distribution_quantity:周期发放的优惠券数量

### coupon_batches对领取的约束规则

- receive_cycle: 指客户可用领取优惠券的周期，表示客户在指定周期内，可用领取的优惠券数量，如每周只能领取1张
- receive_quantity:指客户周期可领取的优惠券数量
- receive_start_time:指客户可领取优惠券的开始时间
- receive_end_time:指客户可领取优惠券的开始时间

## coupon_usage_records

coupon_usage_records用以表示和描述用户领取和使用优惠券的情况，

- coupon_id表示领取的优惠券，
- coupon_batch_id表示所领取的批次
- discount_amount表示使用后，优惠了多少金额，如订单为100元，使用了一张8折券，则优惠金额=100-（100*0.8）=20
- distribution_channel 表示该优惠是从哪一个渠道获取的
