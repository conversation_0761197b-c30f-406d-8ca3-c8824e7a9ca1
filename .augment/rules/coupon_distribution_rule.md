---
type: "manual"
---

# 按规则发放优惠券函数

## 参数

- 事件对象：Optional[UserEvent]
- 用户ids：List[int]
- 规则对象：rule_object

## 返回值

- 发放成功列表：List[Dict[str, Any]]
- 发放失败列表：List[Dict[str, Any]]
- 完全发放成功用户数：int
- 部分发放成功用户数：int
- 发放失败用户数：int

## 函数逻辑

1. 检查是否包含用户事件对象，如果包含则获取该用户行为参数UserEvent.action,并根据行为参数获取优惠券发放规则中包含该行为的规则列表（UserEvent.action in CouponDistributionRule.user_behavior_func），否则获取所有用户优惠券发放规则

2. 迭代每一个优惠券发放规则，按一下要求检查是否符合发放条件，如果符合，则将发放内容添加到发放内容列表中

- 通过发放规则中的用户特征CouponDistributionRule.user_feature_func检查传入的用户user_ids是否符合要求，该部分目前暂不做实现，值保留一个stub函数
- 再获取需要分发的优惠券批次列表与数量组合CouponDistributionRule.distribution_content 数据样例为（[{"coupon_batch_id":1, "quantity":2}，{"coupon_batch_id":2, "quantity":3}]），逐一检查优惠券批次是否符合发放条件，如检查优惠券是否已经发放完毕，检查用户是否已经 reach 到发放上限等，用户是否超过了周期领取限制。
- 符合发放条件的，为用户创建对应的优惠券批次与数量的优惠券使用记录，且将优惠券使用记录的分发渠道distribution_channel按照与用户行为的对应关系（下文）填入，并将对应的用户和发放内容添加到发放成功列表中，否则将用户与发放内容从待发放失败的优惠券批次与记录放到失败列表中。

1. 返回发放成功列表、发放失败列表、完全发放成功用户数、部分发放成功用户数、发放失败用户数

## 分发渠道与用户行为对应的关系

| 用户行为 | 分发渠道 |
|----------|----------|
| REGISTERED     | NEW_USER     |
| VIEWED     | VIEW_ACTIVITY     |
| SHARED     | SHARE_ACTIVITY     |

## 要求

1. 尽量减少数据库查询

