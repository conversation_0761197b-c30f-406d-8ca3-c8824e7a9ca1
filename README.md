# yh-vegan-admin-web 素食管理系统
## 素食管理系统-前端

> 本项目使用 vue-admin-template 作为基础模板来开发，这是一个极简的 vue admin 管理后台。它只包含了 Element UI & axios & iconfont & permission control & lint，这些搭建后台必要的东西。

- [vue-element-admin](https://github.com/PanJiaChen/vue-element-admin)

## Build Setup

```bash
# 克隆项目

# 进入项目目录
cd yh-vegan-admin-web

# 安装依赖
npm install

# taobao镜像
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 [http://localhost:8084](http://localhost:8084)

## 发布

```bash
npm run build:prod
```

## 命名规范

classNama -> xxx-xxx
view 文件 => xxx-xxx
components 文件 大驼峰 => AxxxBxxx
自定义 data 字段名 小驼峰 => axxxBxxx
components 在 view 接收参数 => xxx-xxx
components 的 props 小驼峰 => axxxBxxx




OK = {'code': 200, 'msg': '成功'}
PARAM_ERR = {'code': 202, 'msg': '参数错误, 缺失必填参数'}
USER_ERR = {'code': 401, 'msg': '用户未登陆'}
USER_NOT_ESIST = {'code': 402, 'msg': '用户不存在'}
USER_TYPE_ERR = {'code': 403, 'msg': '用户未登陆'}
PERMISSION_ERR = {'code': 405, 'msg': '用户权限错误'}
PHONE_ERR = {'code': 406, 'msg': '手机号不存在'}
PHONE_REPEAT = {'code': 407, 'msg': '手机号重复'}
COMPANY_ERR = {'code': 408, 'msg': '客户公司不存在'}
COMPANY_REPEAT = {'code': 409, 'msg': '客户公司重复'}
COMPANY_BIND_ERR = {'code': 410, 'msg': '客户公司绑定客户错误'}
BUYER_ERR = {'code': 412, 'msg': '购买方不存在'}
BUYER_REPEAT = {'code': 413, 'msg': '购买方重复'}
PROJECT_ERR = {'code': 416, 'msg': '销售项目不存在'}
INVOICE_ERR = {'code': 418, 'msg': '发票任务错误'}
VERIFICATION_CODE_ERR = {'code': 421, 'msg': '验证码发送失败'}
VERIFICATION_CODE_EXPIRED = {'code': 422, 'msg': '验证码已过期'}
ADDR_ERR = {'code': 425, 'msg': '查询的地址不存在'}
EMAIL_ERR = {'code': 426, 'msg': '查询的邮箱不存在'}
DATA_ERR = {'code': 432, 'msg': '数据错误'}
