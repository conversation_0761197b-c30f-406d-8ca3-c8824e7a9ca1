# 权限控制系统使用说明

## 概述

该项目已完善了基于用户权限的菜单显示和路由控制系统，支持角色权限和权限码双重控制。

## 权限系统架构

### 1. 权限级别
- **超级管理员 (super_admin)**: 拥有所有权限
- **管理员 (admin)**: 拥有大部分管理权限
- **业务员 (salesman)**: 拥有业务相关权限
- **普通用户 (user)**: 基础权限

### 2. 权限控制方式
- **角色权限**: 通过 `meta.role` 字段控制
- **权限码**: 通过 `meta.permission` 字段控制
- **双重验证**: 同时满足角色权限和权限码权限

## 配置说明

### 1. 路由权限配置

在 `src/router/index.js` 中为路由添加权限控制：

```javascript
{
  path: '/user',
  component: Layout,
  meta: {
    title: '用户管理',
    icon: 'peoples',
    role: ['admin', 'super_admin'],        // 角色权限
    permission: ['user:manage']             // 权限码
  },
  children: [
    {
      path: 'personal-user',
      meta: {
        title: '个人用户',
        role: ['admin', 'super_admin'],
        permission: ['user:personal:read'],
        noCache: true
      }
    }
  ]
}
```

### 2. 用户权限数据结构

用户登录后返回的权限信息应包含：

```javascript
{
  roles: ['admin'],                        // 用户角色数组
  permissions: [                           // 用户权限码数组
    'user:manage',
    'user:personal:read',
    'user:personal:create',
    'user:personal:update',
    'user:personal:delete'
  ]
}
```

## 使用方法

### 1. 模板中使用指令控制

使用 `v-permission` 指令控制元素显示：

```vue
<template>
  <!-- 基于权限码控制 -->
  <el-button v-permission="['user:personal:create']" @click="handleAdd">
    新增用户
  </el-button>
  
  <!-- 基于角色控制 -->
  <el-button v-permission="['admin', 'super_admin']" @click="handleAdmin">
    管理员操作
  </el-button>
  
  <!-- 表格操作按钮权限控制 -->
  <el-table-column label="操作">
    <template slot-scope="scope">
      <el-button 
        v-permission="['user:personal:update']"
        @click="handleEdit(scope.row)"
      >
        编辑
      </el-button>
      <el-button 
        v-permission="['user:personal:delete']"
        @click="handleDelete(scope.row)"
      >
        删除
      </el-button>
    </template>
  </el-table-column>
</template>
```

### 2. 组件中使用方法检查

在组件的 `methods` 中使用权限检查方法：

```vue
<script>
import { checkPermission, checkRole } from "@/utils/permission";

export default {
  methods: {
    // 权限检查方法
    hasPermission(permission) {
      return checkPermission(permission);
    },

    hasRole(role) {
      return checkRole(role);
    },

    // 在方法中使用权限检查
    handleAction() {
      if (!this.hasPermission(['user:personal:update'])) {
        this.$message.error('您没有权限执行此操作');
        return;
      }
      // 执行操作...
    }
  }
}
</script>
```

### 3. 条件渲染权限控制

```vue
<template>
  <!-- 使用计算属性控制显示 -->
  <div v-if="canManageUsers">
    <h3>用户管理区域</h3>
    <!-- 用户管理相关内容 -->
  </div>
</template>

<script>
export default {
  computed: {
    canManageUsers() {
      return this.hasPermission(['user:manage']) || this.hasRole(['admin', 'super_admin']);
    }
  }
}
</script>
```

## 权限码规范

建议使用以下权限码命名规范：

- `模块:子模块:操作` 格式
- 例如：
  - `user:personal:read` - 个人用户查看
  - `user:personal:create` - 个人用户创建
  - `user:personal:update` - 个人用户更新
  - `user:personal:delete` - 个人用户删除
  - `user:enterprise:relate` - 用户企业关联
  - `system:manage` - 系统管理
  - `admin:manage` - 管理员管理

## 菜单权限控制

系统会根据用户权限自动过滤路由和生成菜单：

1. **路由过滤**: 只有有权限的路由才会被添加到 Vue Router
2. **菜单生成**: 侧边栏菜单只显示用户有权限访问的页面
3. **导航守卫**: 自动检查用户是否有权限访问指定页面

## 注意事项

1. **权限验证**: 角色权限和权限码权限需要同时满足
2. **超级管理员**: `super_admin` 角色拥有所有权限，跳过权限检查
3. **指令使用**: `v-permission` 指令会直接移除没有权限的DOM元素
4. **权限更新**: 用户权限变更后需要重新登录生效
5. **接口权限**: 前端权限控制仅用于UI显示，接口层面也需要进行权限验证

## 示例权限配置

```javascript
// 不同角色的权限码示例
const rolePermissions = {
  'super_admin': ['*'],  // 所有权限
  'admin': [
    'user:manage',
    'user:personal:read',
    'user:personal:create',
    'user:personal:update',
    'user:enterprise:read',
    'user:enterprise:create',
    'user:enterprise:update',
    'system:manage',
    'role:manage'
  ],
  'salesman': [
    'order:read',
    'coupon:manage',
    'statistic:read'
  ],
  'user': [
    'profile:read',
    'profile:update'
  ]
}
``` 