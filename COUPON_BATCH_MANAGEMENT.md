# 优惠券批次管理功能说明

## 功能概述

本次更新为优惠券系统添加了完整的批次管理功能，实现了优惠券的创建、按批次分发管理，以及手动将优惠券发放给指定用户的功能。

## 新增功能

### 1. 优惠券批次管理

#### 1.1 批次列表页面 (`/coupon/batch-list`)
- **功能**: 显示所有优惠券批次的列表
- **特性**:
  - 支持按批次名称、关联优惠券、状态进行筛选
  - 支持分页显示
  - 支持批次的启用/禁用操作
  - 支持批次的编辑和删除

#### 1.2 批次详情/编辑页面
- **功能**: 创建和编辑优惠券批次
- **包含字段**:
  - 基本信息：批次名称、批次号、关联优惠券、描述
  - 数量设置：批次数量、有效时长
  - 时间设置：生效时间、结束时间
  - 发放设置：发放渠道、周期发放数量、发放周期
  - 领取设置：领取开始/结束时间、周期可领取数量、领取周期

### 2. 优惠券列表页面增强

#### 2.1 新增按钮
- **批次管理**: 跳转到批次管理页面
- **查看批次**: 查看特定优惠券的所有批次

### 3. 优惠券发放功能增强

#### 3.1 批次选择
- 在发放优惠券时，现在需要选择具体的批次
- 批次选择会根据选择的优惠券动态加载
- 显示批次的剩余数量信息

## 后端API接口

### 批次管理接口

1. **搜索批次列表**
   - `POST /coupon/batches/search`
   - 支持按关键词、优惠券ID、状态筛选

2. **获取批次详情**
   - `GET /coupon/batches/view/{batch_id}`

3. **创建批次**
   - `POST /coupon/batches/create`

4. **更新批次**
   - `PUT /coupon/batches/update/{batch_id}`

5. **删除批次**
   - `DELETE /coupon/batches/delete/{batch_id}`

6. **更新批次状态**
   - `POST /coupon/batches/status/{batch_id}`

7. **根据优惠券ID获取批次列表**
   - `GET /coupon/batches/coupon/{coupon_id}`

## 数据模型

### CouponBatch (优惠券批次)

```python
class CouponBatch:
    id: int                           # 批次ID
    name: str                         # 批次名称
    description: str                  # 批次描述
    batch_number: int                 # 批次号
    quantity: int                     # 数量
    start_time: datetime              # 生效时间
    end_time: datetime                # 结束时间
    valid_duration: int               # 有效时长(小时)
    coupon_id: int                    # 关联优惠券ID
    status: Status                    # 状态
    
    # 发放相关字段
    distribution_channels: List       # 发放渠道
    cycle_distribution_quantity: int  # 周期发放数量
    distribution_cycle: CouponUsageCycle  # 发放周期
    
    # 领取相关字段
    cycle_receive_quantity: int       # 周期可领取数量
    receive_cycle: CouponUsageCycle   # 领取周期
    receive_start_time: datetime      # 领取开始时间
    receive_end_time: datetime        # 领取结束时间
```

## 使用流程

### 1. 创建优惠券批次
1. 进入优惠券列表页面
2. 点击"批次管理"按钮
3. 在批次管理页面点击"新增批次"
4. 填写批次信息并保存

### 2. 手动发放优惠券
1. 进入优惠券使用记录页面
2. 点击"发放优惠券"按钮
3. 选择优惠券和对应的批次
4. 选择要发放的用户
5. 确认发放

### 3. 查看特定优惠券的批次
1. 在优惠券列表页面
2. 点击某个优惠券的"查看批次"按钮
3. 自动跳转到批次管理页面并筛选该优惠券的批次

## 技术实现

### 前端
- Vue.js 2.x
- Element UI
- 新增页面：
  - `src/views/coupon/coupon-batch-list.vue`
  - `src/views/coupon/coupon-batch-detail.vue`
- 修改页面：
  - `src/views/coupon/coupon-list.vue`
  - `src/views/coupon/coupon-usage/coupon-distribute.vue`

### 后端
- FastAPI
- SQLAlchemy
- 新增API接口在 `app/api/v1/coupon.py`
- 使用现有的DAO层 `app/dao/coupon.py`

### 路由配置
- 新增路由：`/coupon/batch-list`
- 权限：`coupon:batch:read`

## 注意事项

1. **权限控制**: 批次管理功能需要相应的权限配置
2. **数据一致性**: 发放优惠券时会检查批次的有效性和剩余数量
3. **时间验证**: 批次的时间设置需要符合逻辑（开始时间 < 结束时间）
4. **级联删除**: 删除优惠券时需要考虑关联的批次数据

## 后续优化建议

1. **批量操作**: 支持批量启用/禁用批次
2. **统计报表**: 添加批次使用情况的统计图表
3. **自动发放**: 根据发放渠道自动发放优惠券
4. **库存预警**: 当批次数量不足时发出预警
5. **导入导出**: 支持批次数据的导入导出功能
