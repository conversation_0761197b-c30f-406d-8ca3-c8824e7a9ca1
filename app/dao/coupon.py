from typing import List, Optional, Union

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models import User
from app.models.coupon import (
    Coupon, CouponType, CouponUsageRecord, DiscountCoupon, CashCoupon, FullReductionCoupon,
    CouponBatch, CouponScope, CouponUsageStatus, PaymentChannel, DistributionChannel,
    CouponDistributionRule, DistributionRuleType
)
from app.models.enum import Status
from app.schemas.coupon import (
    CouponCreate, CouponUpdate, CouponUsageRecordSearch, CouponUsageRecordCreate,
    DiscountCouponCreate, CashCouponCreate, FullReductionCouponCreate
)


class CouponDAO(DAO):
    """优惠券数据访问对象"""

    def __init__(self):
        super().__init__(Coupon)

    def _serialize_enum_fields(self, data: dict):
        """序列化枚举字段"""
        # 处理支付渠道枚举列表
        if 'payment_channels' in data:
            payment_channels = data['payment_channels']
            if payment_channels:
                data['payment_channels'] = [
                    channel.value if hasattr(channel, 'value') else str(channel)
                    for channel in payment_channels
                ]

        # 处理其他枚举字段
        enum_fields = ['condition_scope', 'apply_scope', 'usage_cycle', 'status', 'type']
        for field in enum_fields:
            if field in data and hasattr(data[field], 'value'):
                data[field] = data[field].value

    def create(self, session: Session, coupon: Union[
        CouponCreate, DiscountCouponCreate, CashCouponCreate, FullReductionCouponCreate]) -> Coupon:
        """创建优惠券
        
        Args:
            session: 数据库会话
            coupon: 优惠券创建请求，可以是基础优惠券或特定类型优惠券
            
        Returns:
            创建的优惠券实例
        """
        coupon_data = coupon.model_dump()
        coupon_type = coupon_data.get('type')

        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)

        # 根据优惠券类型选择对应的模型类
        model_class = {
            CouponType.DISCOUNT: DiscountCoupon,
            CouponType.CASH: CashCoupon,
            CouponType.FULL_REDUCTION: FullReductionCoupon,
            CouponType.COUPON: Coupon
        }.get(coupon_type, Coupon)

        # 创建优惠券实例
        instance = model_class(**coupon_data)
        session.add(instance)
        session.commit()
        session.refresh(instance)
        return instance

    def get(self, session: Session, coupon_id: int) -> Optional[Coupon]:
        """根据ID获取优惠券"""
        return super().get(session, coupon_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Coupon]:
        """获取优惠券列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, coupon_id: int, coupon: CouponUpdate) -> Optional[Coupon]:
        """更新优惠券"""
        coupon_data = coupon.model_dump(exclude_unset=True)

        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)

        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除优惠券"""
        return super().delete(session, coupon_id)

    def search(self,
               session: Session,
               keyword: Optional[str] = None,
               status: Optional[Status] = None,
               coupon_type: Optional[CouponType] = None,
               skip: int = 0,
               limit: int = 10) -> dict:
        """搜索优惠券
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词（匹配名称和描述）
            status: 优惠券状态
            coupon_type: 优惠券类型
            skip: 分页起始位置
            limit: 每页数量
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的优惠券列表）的字典
        """
        query = session.query(self.model)

        if keyword:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )

        if status:
            query = query.filter(self.model.status == status)

        if coupon_type:
            query = query.filter(self.model.type == coupon_type)

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update_status(self, session: Session, coupon_id: int, status: Status) -> Optional[Coupon]:
        """更新优惠券状态
        
        Args:
            session: 数据库会话
            coupon_id: 优惠券ID
            status: 新的状态
            
        Returns:
            更新后的优惠券对象，如果优惠券不存在则返回 None
        """
        return super().update(session, coupon_id, status=status)

    def search_by_name(self, session: Session, name: str) -> List[dict]:
        """
        根据优惠券名称进行模糊搜索
        
        Args:
            session: 数据库会话
            name: 优惠券名称关键词
            
        Returns:
            List[dict]: 包含优惠券名称和ID的列表
        """
        search_name = f"%{name}%"
        coupons = session.query(self.model).filter(
            self.model.name.like(search_name),
            self.model.status == Status.ACTIVE
        ).all()
        
        return [{"name": coupon.name, "id": coupon.id} for coupon in coupons]

    def get_by_scope(self, session: Session, scope: CouponScope) -> List[Coupon]:
        """根据作用范围获取优惠券"""
        return session.query(self.model).filter(self.model.scope == scope).all()

    def get_by_payment_channel(self, session: Session, payment_channel: PaymentChannel) -> List[Coupon]:
        """根据支付渠道获取优惠券"""
        return session.query(self.model).filter(
            self.model.payment_channels.contains([payment_channel.value])
        ).all()

    def get_active_by_type_and_scope(self, session: Session, coupon_type: CouponType, scope: CouponScope) -> List[Coupon]:
        """根据类型和作用范围获取活跃优惠券"""
        return session.query(self.model).filter(
            self.model.type == coupon_type,
            self.model.scope == scope,
            self.model.status == Status.ACTIVE
        ).all()

    def get_mutual_exclusive_coupons(self, session: Session, coupon_id: int) -> List[Coupon]:
        """获取与指定优惠券互斥的优惠券列表"""
        return session.query(self.model).filter(
            self.model.mutual_exclusive_rules.contains([coupon_id])
        ).all()


class DiscountCouponDAO(DAO):
    """折扣券数据访问对象"""

    def __init__(self):
        super().__init__(DiscountCoupon)

    def _serialize_enum_fields(self, data: dict):
        """序列化枚举字段"""
        # 处理支付渠道枚举列表
        if 'payment_channels' in data:
            payment_channels = data['payment_channels']
            if payment_channels:
                data['payment_channels'] = [
                    channel.value if hasattr(channel, 'value') else str(channel)
                    for channel in payment_channels
                ]

        # 处理其他枚举字段
        enum_fields = ['condition_scope', 'apply_scope', 'usage_cycle', 'status', 'type']
        for field in enum_fields:
            if field in data and hasattr(data[field], 'value'):
                data[field] = data[field].value

    def create(self, session: Session, coupon: DiscountCouponCreate) -> DiscountCoupon:
        """创建折扣券"""
        coupon_data = coupon.model_dump()
        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)
        return super().create(session, **coupon_data)

    def get(self, session: Session, coupon_id: int) -> Optional[DiscountCoupon]:
        """根据ID获取折扣券"""
        return super().get(session, coupon_id)

    def update(self, session: Session, coupon_id: int, coupon: DiscountCouponCreate) -> Optional[DiscountCoupon]:
        """更新折扣券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        if 'type' in coupon_data:
            # 不允许修改type
            del coupon_data['type']

        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)

        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除折扣券"""
        return super().delete(session, coupon_id)


class CashCouponDAO(DAO):
    """现金券数据访问对象"""

    def __init__(self):
        super().__init__(CashCoupon)

    def _serialize_enum_fields(self, data: dict):
        """序列化枚举字段"""
        # 处理支付渠道枚举列表
        if 'payment_channels' in data:
            payment_channels = data['payment_channels']
            if payment_channels:
                data['payment_channels'] = [
                    channel.value if hasattr(channel, 'value') else str(channel)
                    for channel in payment_channels
                ]

        # 处理其他枚举字段
        enum_fields = ['condition_scope', 'apply_scope', 'usage_cycle', 'status', 'type']
        for field in enum_fields:
            if field in data and hasattr(data[field], 'value'):
                data[field] = data[field].value

    def create(self, session: Session, coupon: CashCouponCreate) -> CashCoupon:
        """创建现金券"""
        coupon_data = coupon.model_dump()
        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)
        return super().create(session, **coupon_data)

    def get(self, session: Session, coupon_id: int) -> Optional[CashCoupon]:
        """根据ID获取现金券"""
        return super().get(session, coupon_id)

    def update(self, session: Session, coupon_id: int, coupon: CashCouponCreate) -> Optional[CashCoupon]:
        """更新现金券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        if 'type' in coupon_data:
            # 不允许修改type
            del coupon_data['type']

        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)

        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除现金券"""
        return super().delete(session, coupon_id)


class FullReductionCouponDAO(DAO):
    """满减券数据访问对象"""

    def __init__(self):
        super().__init__(FullReductionCoupon)

    def _serialize_enum_fields(self, data: dict):
        """序列化枚举字段"""
        # 处理支付渠道枚举列表
        if 'payment_channels' in data:
            payment_channels = data['payment_channels']
            if payment_channels:
                data['payment_channels'] = [
                    channel.value if hasattr(channel, 'value') else str(channel)
                    for channel in payment_channels
                ]

        # 处理其他枚举字段
        enum_fields = ['condition_scope', 'apply_scope', 'usage_cycle', 'status', 'type']
        for field in enum_fields:
            if field in data and hasattr(data[field], 'value'):
                data[field] = data[field].value

    def create(self, session: Session, coupon: FullReductionCouponCreate) -> FullReductionCoupon:
        """创建满减券"""
        coupon_data = coupon.model_dump()
        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)
        return super().create(session, **coupon_data)

    def get(self, session: Session, coupon_id: int) -> Optional[FullReductionCoupon]:
        """根据ID获取满减券"""
        return super().get(session, coupon_id)

    def update(self, session: Session, coupon_id: int, coupon: FullReductionCouponCreate) -> Optional[
        FullReductionCoupon]:
        """更新满减券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        if 'type' in coupon_data:
            # 不允许修改type
            del coupon_data['type']

        # 处理枚举字段的序列化
        self._serialize_enum_fields(coupon_data)

        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除满减券"""
        return super().delete(session, coupon_id)


class CouponUsageRecordDAO(DAO):
    """优惠券使用记录数据访问对象"""

    def __init__(self):
        super().__init__(CouponUsageRecord)

    def create(self, session: Session, record: CouponUsageRecordCreate) -> CouponUsageRecord:
        """创建使用记录"""
        record_data = record.model_dump()
        return super().create(session, **record_data)

    def batch_create(self, session: Session, records: List[CouponUsageRecordCreate]) -> dict:
        """批量创建使用记录
        
        Args:
            session: 数据库会话
            records: 优惠券使用记录列表
            
        Returns:
            dict: 包含成功创建数量、失败数量和错误信息的字典
        """
        success_count = 0
        failed_count = 0
        errors = []
        created_records = []
        
        for i, record in enumerate(records):
            try:
                record_data = record.model_dump()
                db_record = self.model(**record_data)
                session.add(db_record)
                session.flush()  # 刷新以获取ID，但不提交
                created_records.append(db_record)
                success_count += 1
            except Exception as e:
                failed_count += 1
                errors.append({
                    "index": i,
                    "record": record.model_dump(),
                    "error": str(e)
                })
        
        try:
            session.commit()  # 统一提交所有成功的记录
            # 刷新所有创建的记录以获取完整信息
            for record in created_records:
                session.refresh(record)
            
            # 将数据库对象转换为可序列化的字典
            serialized_records = []
            for record in created_records:
                serialized_records.append({
                    "id": record.id,
                    "coupon_id": record.coupon_id,
                    "coupon_batch_id": record.coupon_batch_id,
                    "user_id": record.user_id,
                    "order_id": record.order_id,
                    "used_at": record.used_at.strftime("%Y-%m-%d %H:%M:%S") if record.used_at else None,
                    "created_at": record.created_at.strftime("%Y-%m-%d %H:%M:%S") if record.created_at else None,
                    "updated_at": record.updated_at.strftime("%Y-%m-%d %H:%M:%S") if record.updated_at else None,
                    "status": record.status.value if record.status else None
                })
                
        except Exception as e:
            session.rollback()
            return {
                "success_count": 0,
                "failed_count": len(records),
                "total_count": len(records),
                "errors": [{"error": f"批量提交失败: {str(e)}"}],
                "created_records": []
            }
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_count": len(records),
            "errors": errors,
            "created_records": serialized_records
        }

    def get(self, session: Session, record_id: int) -> Optional[CouponUsageRecord]:
        """根据ID获取使用记录"""
        return super().get(session, record_id)

    def get_by_order(self, session: Session, order_id: int) -> List[CouponUsageRecord]:
        """获取订单的优惠券使用记录"""
        return session.query(self.model).filter(self.model.order_id == order_id).all()

    def get_by_user(self, session: Session, user_id: int) -> List[CouponUsageRecord]:
        """获取用户的优惠券使用记录"""
        return session.query(self.model).filter(self.model.user_id == user_id).all()

    def get_by_coupon_batch(self, session: Session, coupon_batch_id: int) -> List[CouponUsageRecord]:
        """获取优惠券批次的使用记录"""
        return session.query(self.model).filter(self.model.coupon_batch_id == coupon_batch_id).all()

    def get_by_user_and_batch(self, session: Session, user_id: int, coupon_batch_id: int) -> List[CouponUsageRecord]:
        """获取指定用户在指定批次的使用记录"""
        return session.query(self.model).filter(
            self.model.user_id == user_id,
            self.model.coupon_batch_id == coupon_batch_id
        ).all()

    def get_usage_count_by_batch(self, session: Session, coupon_batch_id: int) -> int:
        """获取指定批次的使用次数统计"""
        return session.query(self.model).filter(
            self.model.coupon_batch_id == coupon_batch_id,
            self.model.status == CouponUsageStatus.USED
        ).count()

    def get_usage_count_by_user_and_coupon(self, session: Session, user_id: int, coupon_id: int) -> int:
        """获取指定用户对指定优惠券的使用次数"""
        return session.query(self.model).filter(
            self.model.user_id == user_id,
            self.model.coupon_id == coupon_id,
            self.model.status == CouponUsageStatus.USED
        ).count()

    def search_records(
            self,
            session: Session,
            search_params: CouponUsageRecordSearch,
            skip: int = 0,
            limit: int = 100
    ) -> dict:
        """搜索优惠券使用记录
        
        Args:
            session: 数据库会话
            search_params: 搜索参数
            skip: 分页起始位置
            limit: 每页数量
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的记录列表）的字典
        """
        query = session.query(self.model).join(
            Coupon, Coupon.id == self.model.coupon_id
        ).join(
            User, User.id == self.model.user_id
        )

        if search_params.coupon_name:
            query = query.filter(Coupon.name.ilike(f"%{search_params.coupon_name}%"))

        if search_params.username:
            query = query.filter(User.username.ilike(f"%{search_params.username}%"))

        if search_params.order_id:
            query = query.filter(self.model.order_id == search_params.order_id)

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }


class CouponBatchDAO(DAO):
    """优惠券批次数据访问对象"""

    def __init__(self):
        super().__init__(CouponBatch)

    def create(self, session: Session, batch_data: dict) -> CouponBatch:
        """创建优惠券批次"""
        return super().create(session, **batch_data)

    def update(self, session: Session, batch_id: int, **kwargs) -> Optional[CouponBatch]:
        """更新优惠券批次"""
        from datetime import datetime
        from app.models.coupon import CouponUsageCycle, Status

        # 处理枚举字段
        if 'distribution_cycle' in kwargs and isinstance(kwargs['distribution_cycle'], str):
            kwargs['distribution_cycle'] = CouponUsageCycle(kwargs['distribution_cycle'])
        if 'receive_cycle' in kwargs and isinstance(kwargs['receive_cycle'], str):
            kwargs['receive_cycle'] = CouponUsageCycle(kwargs['receive_cycle'])
        if 'status' in kwargs and isinstance(kwargs['status'], int):
            kwargs['status'] = Status.ACTIVE if kwargs['status'] == 1 else Status.INACTIVE

        # 处理时间字段
        time_fields = ['start_time', 'end_time', 'receive_start_time', 'receive_end_time']
        for field in time_fields:
            if field in kwargs and isinstance(kwargs[field], str):
                try:
                    kwargs[field] = datetime.fromisoformat(kwargs[field].replace('Z', '+00:00'))
                except ValueError:
                    # 如果解析失败，保持原值
                    pass

        return super().update(session, batch_id, **kwargs)

    def get(self, session: Session, batch_id: int) -> Optional[CouponBatch]:
        """根据ID获取优惠券批次"""
        return super().get(session, batch_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[CouponBatch]:
        """获取优惠券批次列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_coupon_id(self, session: Session, coupon_id: int) -> List[CouponBatch]:
        """根据优惠券ID获取批次列表"""
        return session.query(self.model).filter(self.model.coupon_id == coupon_id).all()

    def search(self, session: Session, keyword: str = None, coupon_id: int = None,
               status: int = None, skip: int = 0, limit: int = 100) -> dict:
        """搜索优惠券批次"""
        query = session.query(self.model)

        # 按关键词搜索（批次名称）
        if keyword:
            query = query.filter(self.model.name.contains(keyword))

        # 按优惠券ID筛选
        if coupon_id:
            query = query.filter(self.model.coupon_id == coupon_id)

        # 按状态筛选
        if status is not None:
            if status == 1:
                query = query.filter(self.model.status == Status.ACTIVE)
            else:
                query = query.filter(self.model.status == Status.INACTIVE)

        # 获取总数
        total = query.count()

        # 分页查询
        items = query.offset(skip).limit(limit).all()

        # 转换为字典格式 - 只返回基本字段避免序列化问题
        batch_list = []
        for batch in items:
            try:
                batch_dict = {
                    "id": batch.id,
                    "name": batch.name or "",
                    "description": batch.description or "",
                    "batch_number": batch.batch_number or 0,
                    "quantity": batch.quantity or 0,
                    "coupon_id": batch.coupon_id,
                    "status": 1 if batch.status and batch.status.value == 1 else 0,
                }
                batch_list.append(batch_dict)
            except Exception as e:
                print(f"Error serializing batch {batch.id}: {e}")
                # 跳过有问题的记录
                continue

        return {
            "total": total,
            "list": batch_list
        }

    def update_status(self, session: Session, batch_id: int, status_value: int) -> Optional[CouponBatch]:
        """更新优惠券批次状态"""
        batch = self.get(session, batch_id)
        if batch:
            if status_value == 1:
                batch.status = Status.ACTIVE
            else:
                batch.status = Status.INACTIVE
            session.commit()
            session.refresh(batch)
            return batch
        return None

    def get_active_batches(self, session: Session) -> List[CouponBatch]:
        """获取所有活跃的批次"""
        return session.query(self.model).filter(self.model.status == Status.ACTIVE).all()

    def get_by_distribution_channel(self, session: Session, distribution_channel: DistributionChannel) -> List[CouponBatch]:
        """根据发放渠道获取批次列表"""
        return session.query(self.model).filter(
            self.model.distribution_channels.contains([distribution_channel.value])
        ).all()

    def get_available_for_user(self, session: Session, user_id: int, distribution_channel: DistributionChannel = None) -> List[CouponBatch]:
        """获取用户可领取的批次列表"""
        from datetime import datetime
        
        query = session.query(self.model).filter(
            self.model.status == Status.ACTIVE,
            self.model.receive_start_time <= datetime.now(),
            self.model.receive_end_time >= datetime.now(),
            self.model.quantity > 0
        )
        
        if distribution_channel:
            query = query.filter(
                self.model.distribution_channels.contains([distribution_channel.value])
            )
        
        return query.all()

    def update_quantity(self, session: Session, batch_id: int, quantity_change: int) -> Optional[CouponBatch]:
        """更新批次数量（增加或减少）"""
        batch = self.get(session, batch_id)
        if batch:
            new_quantity = batch.quantity + quantity_change
            if new_quantity >= 0:
                return super().update(session, batch_id, quantity=new_quantity)
        return None

    def search(self, session: Session, 
               keyword: Optional[str] = None,
               coupon_id: Optional[int] = None,
               status: Optional[Status] = None,
               distribution_channel: Optional[DistributionChannel] = None,
               skip: int = 0,
               limit: int = 100) -> dict:
        """搜索优惠券批次"""
        query = session.query(self.model)

        if keyword:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )

        if coupon_id:
            query = query.filter(self.model.coupon_id == coupon_id)

        if status:
            query = query.filter(self.model.status == status)

        if distribution_channel:
            query = query.filter(
                self.model.distribution_channels.contains([distribution_channel.value])
            )

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update_status(self, session: Session, batch_id: int, status: Status) -> Optional[CouponBatch]:
        """更新批次状态"""
        return super().update(session, batch_id, status=status)

    def delete(self, session: Session, batch_id: int) -> bool:
        """删除批次"""
        return super().delete(session, batch_id)


class CouponDistributionRuleDAO(DAO):
    """优惠券发放规则数据访问对象"""

    def __init__(self):
        super().__init__(CouponDistributionRule)

    def _serialize_enum_fields(self, data: dict):
        """序列化枚举字段"""
        enum_fields = ['type', 'status']
        for field in enum_fields:
            if field in data and hasattr(data[field], 'value'):
                data[field] = data[field].value

    def create(self, session: Session, rule_data: dict) -> CouponDistributionRule:
        """创建发放规则"""
        # 处理枚举字段的序列化
        self._serialize_enum_fields(rule_data)
        return super().create(session, **rule_data)

    def get(self, session: Session, rule_id: int) -> Optional[CouponDistributionRule]:
        """根据ID获取发放规则"""
        return super().get(session, rule_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[CouponDistributionRule]:
        """获取发放规则列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, rule_id: int, **kwargs) -> Optional[CouponDistributionRule]:
        """更新发放规则"""
        # 处理枚举字段的序列化
        self._serialize_enum_fields(kwargs)
        return super().update(session, rule_id, **kwargs)

    def delete(self, session: Session, rule_id: int) -> bool:
        """删除发放规则"""
        return super().delete(session, rule_id)

    def get_active_rules(self, session: Session) -> List[CouponDistributionRule]:
        """获取所有活跃的发放规则"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE
        ).all()

    def get_by_type(self, session: Session, rule_type: DistributionRuleType) -> List[CouponDistributionRule]:
        """根据规则类型获取发放规则"""
        return session.query(self.model).filter(
            self.model.type == rule_type,
            self.model.status == Status.ACTIVE
        ).all()

    def search(self, session: Session,
               keyword: Optional[str] = None,
               rule_type: Optional[DistributionRuleType] = None,
               status: Optional[Status] = None,
               skip: int = 0,
               limit: int = 100) -> dict:
        """搜索发放规则"""
        query = session.query(self.model)

        if keyword:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )

        if rule_type:
            query = query.filter(self.model.type == rule_type)

        if status:
            query = query.filter(self.model.status == status)

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update_status(self, session: Session, rule_id: int, status: Status) -> Optional[CouponDistributionRule]:
        """更新规则状态"""
        return super().update(session, rule_id, status=status)


# 创建 DAO 实例
coupon_dao = CouponDAO()
discount_coupon_dao = DiscountCouponDAO()
cash_coupon_dao = CashCouponDAO()
full_reduction_coupon_dao = FullReductionCouponDAO()
coupon_usage_record_dao = CouponUsageRecordDAO()
coupon_batch_dao = CouponBatchDAO()
coupon_distribution_rule_dao = CouponDistributionRuleDAO()
