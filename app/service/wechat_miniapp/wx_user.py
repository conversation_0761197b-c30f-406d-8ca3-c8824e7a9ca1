from typing import Optional, Dict, Any
import base64
from datetime import datetime, timedelta
import secrets
from Crypto.Cipher import AES
import json
import requests
from app.core.config import settings
from app.models.account import PointsAccount
from app.dao.user import user_dao, personal_user_dao, enterprise_user_relation_dao, enterprise_dao
from app.schemas.user import PersonalUserUpdateWX, PersonalUserMsgStatusUpdate, PersonalUser
from sqlalchemy.orm import Session

from app.service.wechat_miniapp.wx_account import AccountService


class WeChatUserService:
    @staticmethod
    def get_session_info(code: str) -> Optional[Dict[str, Any]]:
        """获取微信session信息"""
        try:
            url = f'{settings.WECHAT_OFFICIAL_DOMAIN}/sns/jscode2session'
            params = {
                'appid': settings.WECHAT_APPID,
                'secret': settings.WECHAT_SECRET,
                'js_code': code,
                'grant_type': 'authorization_code'
            }
            response = requests.get(url, params=params)
            return response.json()
        except Exception:
            return None

    @staticmethod
    def verify_token(session: Session, token: str) -> Optional[PersonalUser]:
        """验证token
        :rtype: object
        """
        try:

            user = personal_user_dao.get_by_token(session, token)
            if not user:
                return None

            # token 已过期
            if user.token_expiry and user.token_expiry < datetime.now():
                # return None
                return user  # 暂时关闭，因为token过期后，用户需要重新登录，小程序会自动登录免去过期设置

            # token 即将过期（比如剩余有效期小于3天），自动延长
            if user.token_expiry and user.token_expiry < datetime.now() + timedelta(days=3):
                # 延长 token 有效期，但保持 token 值不变
                user.token_expiry = datetime.now() + timedelta(days=30)
                session.commit()

        except Exception as e:
            print(f"验证token失败: {str(e)}")
            return None

        return user


    @staticmethod
    def get_user_info(session: Session, wechat_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            personal_object = personal_user_dao.get_by_openid(session, wechat_id)
            if not personal_object:
                return {}

            avatar_url = personal_object.avatar_url
            if avatar_url and not avatar_url.startswith(('http://', 'https://')):
                avatar_url = settings.BASE_URL + avatar_url

            balance = AccountService.get_balance(session, personal_object.id)
            enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(session, personal_object.id)

            enterprise_list = []
            for enterprise_object in enterprise_objects:
                enterprise = enterprise_dao.get(session, enterprise_object.enterprise_id)
                enterprise_data = {
                    'id': enterprise.id,
                    'company_name': enterprise.company_name,
                    'phone': enterprise.phone,
                    'email': enterprise.email,
                    'address': enterprise.address
                }
                enterprise_list.append(enterprise_data)

            user_info = {
                'id': personal_object.id,
                'openid': personal_object.wechat_id,
                'nickName': personal_object.nickname,
                'real_name': personal_object.real_name,
                'avatarUrl': avatar_url,
                'phoneNumber': personal_object.phone,
                'msg_status': personal_object.msg_status,
                'balance': balance,
                'gender': personal_object.gender,
                'country': personal_object.country,
                'province': personal_object.province,
                'city': personal_object.city,
                'enterprise_list': enterprise_list
            }

            return user_info

        except Exception as e:
            print(f"获取用户信息失败: {str(e)}")
            return {}

    @staticmethod
    def update_token(session: Session, wechat_id: str, token: str, token_expiry: datetime) -> bool:
        """更新用户的 token 信息

        Args:
            session: 数据库会话
            wechat_id: 微信 ID
            token: 新的 token
            token_expiry: token 过期时间

        Returns:
            bool: 更新是否成功
        """
        try:
            user = personal_user_dao.get_by_openid(session, wechat_id)
            if user:
                user.token = token
                user.token_expiry = token_expiry
                session.commit()
                return True
            return False
        except Exception:
            session.rollback()
            return False

    @staticmethod
    def update_user_info(session: Session, user_info: PersonalUserUpdateWX) -> bool:
        """更新用户信息
        
        Args:
            session: 数据库会话
            user_info: 用户信息
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not user_info:
                return False
                
            # 将 Pydantic 模型转换为字典
            update_data = user_info.model_dump(exclude_unset=True)
            
            if not update_data:
                return True
                
            result = personal_user_dao.update_by_openid(
                session, 
                user_info.wechat_id, 
                update_data
            )
            
            session.commit()
            return bool(result)
            
        except Exception as e:
            session.rollback()
            raise Exception(f"更新用户信息失败: {str(e)}")

    @staticmethod
    def update_msg_status(session: Session, user_id: int, msg_status: bool) -> bool:
        """更新用户消息订阅状态"""
        try:
            update_data = {"msg_status": msg_status}
            personal_user_dao.update(session, user_id, PersonalUserMsgStatusUpdate(**update_data))
            return True
        except Exception:
            session.rollback()
            return False