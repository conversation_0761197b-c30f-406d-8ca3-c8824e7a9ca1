"""
用户行为函数模块

该模块包含用于检查用户行为是否符合优惠券发放条件的各种函数。
每个函数接收 (user_event, params) 作为参数，返回布尔值表示是否符合条件。
"""

from typing import Dict, Any, List
from app.events.models import UserEvent, UserEventAction
from app.utils.logger import logger


def handle_viewed(user_event: UserEvent, params: Dict[str, Any]) -> bool:
    """
    处理用户查看行为
    
    Args:
        user_event: 用户事件对象
        params: 参数字典，可能包含 content_ids 等
        
    Returns:
        bool: 是否符合发放条件
    """
    try:
        # 检查事件类型是否为查看
        if user_event.action != UserEventAction.VIEWED:
            return False
            
        # 如果指定了内容ID，检查两个数组是否一致
        if "content_ids" in params:
            content_ids = params.get("content_ids", [])
            if content_ids:
                # 从additional_data中获取内容ID
                event_content_ids = user_event.additional_data.get("content_ids") if user_event.additional_data else None
                # 检查两个数组是否一致（排序后比较，忽略顺序差异）
                if event_content_ids is None or sorted(event_content_ids) != sorted(content_ids):
                    return False
        
        logger.debug(f"用户 {user_event.user_id} 查看行为符合条件")
        return True
        
    except Exception as e:
        logger.error(f"处理用户查看行为函数时出错: {str(e)}")
        return False


def handle_shared(user_event: UserEvent, params: Dict[str, Any]) -> bool:
    """
    处理用户分享行为
    
    Args:
        user_event: 用户事件对象
        params: 参数字典，可能包含 content_ids 等
        
    Returns:
        bool: 是否符合发放条件
    """
    try:
        # 检查事件类型是否为分享
        if user_event.action != UserEventAction.SHARED:
            return False
            
        # 如果指定了内容ID，检查两个数组是否一致
        if "content_ids" in params:
            content_ids = params.get("content_ids", [])
            if content_ids:
                # 从additional_data中获取内容ID
                event_content_ids = user_event.additional_data.get("content_ids") if user_event.additional_data else None
                # 检查两个数组是否一致（排序后比较，忽略顺序差异）
                if event_content_ids is None or sorted(event_content_ids) != sorted(content_ids):
                    return False
        
        logger.debug(f"用户 {user_event.user_id} 分享行为符合条件")
        return True
        
    except Exception as e:
        logger.error(f"处理用户分享行为函数时出错: {str(e)}")
        return False


def handle_paid(user_event: UserEvent, params: Dict[str, Any]) -> bool:
    """
    处理用户支付行为
    
    Args:
        user_event: 用户事件对象
        params: 参数字典，可能包含最小金额等条件
        
    Returns:
        bool: 是否符合发放条件
    """
    try:
        # 检查事件类型是否为支付
        if user_event.action != UserEventAction.PAID:
            return False
            
        # 如果指定了最小金额，检查支付金额是否满足
        if "min_amount" in params:
            min_amount = params.get("min_amount", 0)
            event_amount = user_event.additional_data.get("amount", 0) if user_event.additional_data else 0
            if event_amount < min_amount:
                return False
        
        logger.debug(f"用户 {user_event.user_id} 支付行为符合条件")
        return True
        
    except Exception as e:
        logger.error(f"处理用户支付行为函数时出错: {str(e)}")
        return False


def handle_registered(user_event: UserEvent, params: Dict[str, Any]) -> bool:
    """
    处理用户注册行为
    
    Args:
        user_event: 用户事件对象
        params: 参数字典
        
    Returns:
        bool: 是否符合发放条件
    """
    try:
        # 检查事件类型是否为注册
        if user_event.action != UserEventAction.REGISTERED:
            return False
        
        logger.debug(f"用户 {user_event.user_id} 注册行为符合条件")
        return True
        
    except Exception as e:
        logger.error(f"处理用户注册行为函数时出错: {str(e)}")
        return False


def handle_ordered(user_event: UserEvent, params: Dict[str, Any]) -> bool:
    """
    处理用户下单行为
    
    Args:
        user_event: 用户事件对象
        params: 参数字典，可能包含订单类型、最小金额等条件
        
    Returns:
        bool: 是否符合发放条件
    """
    try:
        # 检查事件类型是否为下单
        if user_event.action != UserEventAction.ORDERED:
            return False
            
        # 如果指定了最小金额，检查订单金额是否满足
        if "min_amount" in params:
            min_amount = params.get("min_amount", 0)
            event_amount = user_event.additional_data.get("amount", 0) if user_event.additional_data else 0
            if event_amount < min_amount:
                return False
                
        # 如果指定了订单类型，检查是否匹配
        if "order_types" in params:
            order_types = params.get("order_types", [])
            if order_types:
                event_order_type = user_event.additional_data.get("order_type") if user_event.additional_data else None
                if event_order_type not in order_types:
                    return False
        
        logger.debug(f"用户 {user_event.user_id} 下单行为符合条件")
        return True
        
    except Exception as e:
        logger.error(f"处理用户下单行为函数时出错: {str(e)}")
        return False


# 用户行为函数注册表
USER_BEHAVIOR_FUNCTIONS = {
    "handle_viewed": handle_viewed,
    "handle_shared": handle_shared,
    "handle_paid": handle_paid,
    "handle_registered": handle_registered,
    "handle_ordered": handle_ordered,
}


def get_user_behavior_function(func_name: str):
    """
    获取用户行为函数
    
    Args:
        func_name: 函数名称
        
    Returns:
        函数对象或None
    """
    return USER_BEHAVIOR_FUNCTIONS.get(func_name)

