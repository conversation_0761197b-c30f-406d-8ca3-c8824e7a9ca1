from typing import List, Dict, Any, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.dao.coupon import coupon_usage_record_dao
from app.schemas.coupon import ProductQuantityItem
from app.schemas.order import OrderItemBase, CouponDiscountDetail
from app.service.coupon import CouponService
from app.models.coupon import CouponUsageStatus
from app.utils.logger import logger


class CouponCalculator:
    """优惠券计算器
    
    提供独立的优惠券验证和计算功能，用于订单创建过程中的优惠券应用
    """

    @staticmethod
    def validate_and_calculate_coupons(
        session: Session,
        user_id: int,
        order_items: List[OrderItemBase],
        coupon_usage_record_ids: List[int]
    ) -> Tuple[List[OrderItemBase], float, List[CouponDiscountDetail]]:
        """验证并计算优惠券优惠
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            order_items: 已经计算过pricing的订单项列表
            coupon_usage_record_ids: 优惠券使用记录ID列表
            
        Returns:
            Tuple[List[OrderItemBase], float, List[CouponDiscountDetail]]: 
            (更新后的订单项列表, 总优惠金额, 优惠券优惠详情列表)
        """
        if not coupon_usage_record_ids:
            return order_items, 0.0, []

        try:
            # 1. 将OrderItemBase转换为ProductQuantityItem格式
            products = CouponCalculator._convert_order_items_to_products(order_items)
            
            # 2. 调用CouponService进行优惠券计价
            pricing_result = CouponService.coupon_pricing(
                session, user_id, products, coupon_usage_record_ids
            )
            
            # 3. 提取计价结果
            order_pricing = pricing_result["pricing_result"]["order"]
            discount_info = pricing_result["pricing_result"]["discount"]
            
            # 4. 更新订单项的应付金额
            updated_order_items = CouponCalculator._update_order_items_with_pricing(
                order_items, order_pricing["order_items"]
            )
            
            # 5. 转换优惠券详情格式
            coupon_discounts = CouponCalculator._convert_coupon_discounts(
                discount_info["coupons"]
            )
            
            total_discount = discount_info["total_discount"]
            
            logger.info(f"优惠券计算完成，用户{user_id}，总优惠金额: {total_discount}")
            
            return updated_order_items, total_discount, coupon_discounts
            
        except Exception as e:
            logger.error(f"优惠券计算失败: {str(e)}")
            raise ValueError(f"优惠券计算失败: {str(e)}")

    @staticmethod
    def _convert_order_items_to_products(order_items: List[OrderItemBase]) -> List[ProductQuantityItem]:
        """将OrderItemBase转换为ProductQuantityItem格式"""
        products = []
        for item in order_items:
            products.append(ProductQuantityItem(
                product_id=item.product_id,
                quantity=item.quantity
            ))
        return products

    @staticmethod
    def _update_order_items_with_pricing(
        original_items: List[OrderItemBase], 
        pricing_items: List[Dict[str, Any]]
    ) -> List[OrderItemBase]:
        """使用优惠券计价结果更新订单项"""
        # 创建产品ID到计价结果的映射
        pricing_map = {}
        for pricing_item in pricing_items:
            product_id = pricing_item["product_id"]
            if product_id not in pricing_map:
                pricing_map[product_id] = []
            pricing_map[product_id].append(pricing_item)
        
        updated_items = []
        for item in original_items:
            # 复制原始订单项
            updated_item = OrderItemBase(
                product_id=item.product_id,
                quantity=item.quantity,
                price=item.price,
                subtotal=item.subtotal,
                final_price=item.final_price,
                payable_amount=item.payable_amount,
                pricing_remark=item.pricing_remark
            )
            
            # 如果有对应的计价结果，更新应付金额
            if item.product_id in pricing_map:
                pricing_items_for_product = pricing_map[item.product_id]
                # 找到数量匹配的计价项
                for pricing_item in pricing_items_for_product:
                    if pricing_item["quantity"] == item.quantity:
                        updated_item.payable_amount = pricing_item["payable_amount"]
                        break
            
            updated_items.append(updated_item)
        
        return updated_items

    @staticmethod
    def _convert_coupon_discounts(coupon_discounts: List[Dict[str, Any]]) -> List[CouponDiscountDetail]:
        """转换优惠券优惠详情格式"""
        result = []
        for discount in coupon_discounts:
            result.append(CouponDiscountDetail(
                coupon_usage_record_id=discount["coupon_usage_record_id"],
                coupon_id=discount["coupon_id"],
                coupon_name=discount["coupon_name"],
                quantity=discount["quantity"],
                discount_amount=discount["discount_amount"]
            ))
        return result

    @staticmethod
    def update_coupon_usage_records(
        session: Session,
        order_id: int,
        coupon_usage_record_ids: List[int],
        coupon_discounts: List[CouponDiscountDetail]
    ) -> None:
        """更新优惠券使用记录
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            coupon_usage_record_ids: 优惠券使用记录ID列表
            coupon_discounts: 优惠券优惠详情列表
        """
        if not coupon_usage_record_ids:
            return
            
        try:
            # 创建优惠券记录ID到优惠金额的映射
            discount_map = {}
            for discount in coupon_discounts:
                discount_map[discount.coupon_usage_record_id] = discount.discount_amount
            
            # 更新每个优惠券使用记录
            for record_id in coupon_usage_record_ids:
                record = coupon_usage_record_dao.get(session, record_id)
                if record:
                    # 更新记录状态和相关信息
                    update_data = {
                        "status": CouponUsageStatus.USED,
                        "order_id": order_id,
                        "discount_amount": discount_map.get(record_id, 0.0),
                        "used_at": datetime.now()
                    }
                    coupon_usage_record_dao.update(session, record_id, **update_data)
                    
            logger.info(f"已更新{len(coupon_usage_record_ids)}个优惠券使用记录")
            
        except Exception as e:
            logger.error(f"更新优惠券使用记录失败: {str(e)}")
            raise ValueError(f"更新优惠券使用记录失败: {str(e)}")


# 创建计算器实例
coupon_calculator = CouponCalculator()
