"""
Event Data Models

This module defines Pydantic models for different types of events
that can be published and consumed across the application.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from app.events.base import BaseEvent


class EventType(str, Enum):
    """Enumeration of event types for categorization."""
    USER = "user"
    ORDER = "order"
    RESERVATION = "reservation"
    PAYMENT = "payment"
    NOTIFICATION = "notification"
    SYSTEM = "system"


class UserEventAction(str, Enum):
    """User-related event actions."""
    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    LOGIN = "login"
    LOGOUT = "logout"
    PASSWORD_CHANGED = "password_changed"
    VIEWED = "viewed"
    SHARED = "shared"
    REGISTERED = "registered"
    ORDERED = "ordered"
    PAID = "paid"
    USED = "used"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    VERIFIED = "verified"


class UserEvent(BaseEvent):
    """
    Event model for user-related events.
    
    This event is published when user-related actions occur,
    such as user creation, updates, login, etc.
    """
    event_type: EventType = EventType.USER
    action: UserEventAction
    user_id: int
    user_type: Optional[str] = None
    username: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class OrderEventAction(str, Enum):
    """Order-related event actions."""
    CREATED = "created"
    UPDATED = "updated"
    CANCELLED = "cancelled"
    PAID = "paid"
    REFUNDED = "refunded"
    COMPLETED = "completed"


class OrderEvent(BaseEvent):
    """
    Event model for order-related events.
    
    This event is published when order status changes or
    order-related actions occur.
    """
    event_type: EventType = EventType.ORDER
    action: OrderEventAction
    order_id: int
    order_no: Optional[str] = None
    user_id: Optional[int] = None
    amount: Optional[float] = None
    status: Optional[str] = None
    payment_status: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class ReservationEventAction(str, Enum):
    """Reservation-related event actions."""
    CREATED = "created"
    UPDATED = "updated"
    CANCELLED = "cancelled"
    CONFIRMED = "confirmed"
    COMPLETED = "completed"
    VERIFIED = "verified"


class ReservationEvent(BaseEvent):
    """
    Event model for reservation-related events.
    
    This event is published when reservation status changes
    or reservation-related actions occur.
    """
    event_type: EventType = EventType.RESERVATION
    action: ReservationEventAction
    reservation_id: int
    user_id: Optional[int] = None
    dining_date: Optional[datetime] = None
    status: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class PaymentEventAction(str, Enum):
    """Payment-related event actions."""
    INITIATED = "initiated"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"


class PaymentEvent(BaseEvent):
    """
    Event model for payment-related events.
    
    This event is published when payment transactions occur.
    """
    event_type: EventType = EventType.PAYMENT
    action: PaymentEventAction
    payment_id: Optional[str] = None
    order_id: Optional[int] = None
    user_id: Optional[int] = None
    amount: Optional[float] = None
    payment_method: Optional[str] = None
    transaction_id: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class NotificationEventAction(str, Enum):
    """Notification-related event actions."""
    SEND_EMAIL = "send_email"
    SEND_SMS = "send_sms"
    SEND_WECHAT = "send_wechat"
    SEND_PUSH = "send_push"


class NotificationEvent(BaseEvent):
    """
    Event model for notification events.
    
    This event is published when notifications need to be sent
    to users through various channels.
    """
    event_type: EventType = EventType.NOTIFICATION
    action: NotificationEventAction
    recipient_id: Optional[int] = None
    recipient_type: Optional[str] = None  # "user", "admin", etc.
    channel: str  # "email", "sms", "wechat", "push"
    template_id: Optional[str] = None
    subject: Optional[str] = None
    content: str
    data: Optional[Dict[str, Any]] = None
    priority: int = 1  # 1=low, 2=normal, 3=high, 4=urgent


class SystemEventAction(str, Enum):
    """System-related event actions."""
    STARTUP = "startup"
    SHUTDOWN = "shutdown"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    MAINTENANCE = "maintenance"


class SystemEvent(BaseEvent):
    """
    Event model for system-related events.
    
    This event is published for system-level events such as
    startup, shutdown, errors, and maintenance activities.
    """
    event_type: EventType = EventType.SYSTEM
    action: SystemEventAction
    component: Optional[str] = None
    message: str
    level: str = "info"  # "debug", "info", "warning", "error", "critical"
    additional_data: Optional[Dict[str, Any]] = None
