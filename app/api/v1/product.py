from typing import List, Dict, Optional
import json

from fastapi import APIRouter, Depends, HTTPException, status, Body, Form, File, UploadFile
from sqlalchemy.orm import Session
from starlette.responses import JSONResponse

from app.core.deps import get_db
from app.dao.product import (
    product_dao, direct_sale_product_dao, reservation_product_dao, virtual_product_dao
)
from app.dao.tag import tag_dao
from app.models.product import ProductType
from app.models.enum import Status
from app.schemas.common import CommonResponse, CommonRequest
from app.schemas.content import (
    ProductAndContent
)
from app.schemas.pricing import PricingStrategyResponse
from app.schemas.product import (
    ProductCreate, ProductUpdate, ProductResponse, ProductWithRelations,
    DirectSaleProductCreate, DirectSaleProductUpdate, DirectSaleProductResponse, DirectSaleProductWithRelations,
    ReservationProductCreate, ReservationProductUpdate, ReservationProductResponse, ReservationProductWithRelations,
    VirtualProductCreate, VirtualProductUpdate, VirtualProductResponse, VirtualProductWithRelations,
    ProductAndPricingStrategy, ProductAndCategory, ProductCategoryResponse,
    ProductSearchRequest, ProductListFormattedResponse, ProductStatusUpdateRequest, ProdContentsSearchReq,
    ProdContentsSearchResp, ProdPricingStrategiesSearchResp, ProdPricingStrategiesSearchReq, ProdRulesSearchResp,
    ProdRulesSearchReq, ProductWithDiningRulesResponse, ProductNameSearchResponse, ProductNameSearchItem
)
from app.schemas.rule import ProductAndRule
from app.schemas.rule import RuleResponse
from app.schemas.tag import TagResponse
from app.utils.file import save_uploaded_image

router = APIRouter()


# Product endpoints
@router.post("/", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
def create_product(product: ProductCreate, db: Session = Depends(get_db)):
    """创建产品"""
    return product_dao.create(session=db, product=product)


@router.get("/{product_id}", response_model=ProductWithRelations)
def read_product(product_id: int, db: Session = Depends(get_db)):
    """获取产品详情，包含关联的内容和标签"""
    db_product = product_dao.get(session=db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return db_product


@router.get("/", response_model=List[ProductResponse])
def read_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取产品列表"""
    return product_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/active/", response_model=CommonResponse)
def read_active_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取活跃状态的产品列表"""
    products = product_dao.get_active_products(session=db, skip=skip, limit=limit)

    # 简化返回数据，只返回基础产品信息
    product_list = []
    for product in products:
        product_data = {
            "id": product.id,
            "name": product.name,
            "price": product.price,
            "description": product.description,
            "image": product.image,
            "stock": product.stock,
            "status": product.status.value if product.status else "active",
            "type": product.type.value if product.type else "product",
            "meal_type": product.meal_type.value if product.meal_type else "buffet",
            "listed_at": product.listed_at.strftime("%Y-%m-%d %H:%M:%S") if product.listed_at else None,
            "created_at": product.created_at.strftime("%Y-%m-%d %H:%M:%S") if product.created_at else None,
            "updated_at": product.updated_at.strftime("%Y-%m-%d %H:%M:%S") if product.updated_at else None
        }
        product_list.append(product_data)

    return {
        "code": 200,
        "message": "获取活跃产品列表成功",
        "data": product_list
    }


@router.get("/by-type/{product_type}", response_model=List[ProductResponse])
def read_products_by_type(product_type: ProductType, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """根据产品类型获取产品列表"""
    return product_dao.get_by_type(session=db, product_type=product_type, skip=skip, limit=limit)


@router.get("/by-tags/", response_model=List[ProductResponse])
def read_products_by_tags(tag_ids: List[int], skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """根据标签获取产品列表"""
    return product_dao.get_by_tags(session=db, tag_ids=tag_ids, skip=skip, limit=limit)


# 旧的更新路由已被新的 /update 路由替代
# @router.put("/{product_id}", response_model=ProductResponse)
# def update_product(product_id: int, product: ProductUpdate, db: Session = Depends(get_db)):
#     """更新产品信息"""
#     db_product = product_dao.update(session=db, product_id=product_id, product=product)
#     if db_product is None:
#         raise HTTPException(status_code=404, detail="产品不存在")
#     return db_product


@router.delete("/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_product(product_id: int, db: Session = Depends(get_db)):
    """删除产品"""
    success = product_dao.delete(session=db, product_id=product_id)
    if not success:
        raise HTTPException(status_code=404, detail="产品不存在")
    return {"message": "产品删除成功"}


def get_product_with_content(product_id: int, db: Session = Depends(get_db)):
    """获取产品及其关联的内容"""
    db_product = product_dao.get(session=db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    # 获取产品关联的内容


# DirectSaleProduct endpoints
@router.post("/direct-sale/", response_model=DirectSaleProductResponse, status_code=status.HTTP_201_CREATED)
def create_direct_sale_product(product: DirectSaleProductCreate, db: Session = Depends(get_db)):
    """创建直销产品"""
    return direct_sale_product_dao.create(session=db, product=product)


@router.get("/direct-sale/{product_id}", response_model=DirectSaleProductWithRelations)
def read_direct_sale_product(product_id: int, db: Session = Depends(get_db)):
    """获取直销产品详情，包含关联的内容和标签"""
    db_product = direct_sale_product_dao.get(session=db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="直销产品不存在")
    return db_product


@router.get("/direct-sale/", response_model=List[DirectSaleProductResponse])
def read_direct_sale_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取直销产品列表"""
    return direct_sale_product_dao.get_list(session=db, skip=skip, limit=limit)


@router.put("/direct-sale/{product_id}", response_model=DirectSaleProductResponse)
def update_direct_sale_product(product_id: int, product: DirectSaleProductUpdate, db: Session = Depends(get_db)):
    """更新直销产品信息"""
    db_product = direct_sale_product_dao.update(session=db, product_id=product_id, product=product)
    if db_product is None:
        raise HTTPException(status_code=404, detail="直销产品不存在")
    return db_product


@router.delete("/direct-sale/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_direct_sale_product(product_id: int, db: Session = Depends(get_db)):
    """删除直销产品"""
    # 先检查是否有关联的订单项
    db_product = direct_sale_product_dao.get(session=db, product_id=product_id)
    if not db_product:
        raise HTTPException(status_code=404, detail="直销产品不存在")

    # 检查是否存在关联的订单项
    if db_product.order_items and len(db_product.order_items) > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": "无法删除直销产品，存在关联的订单项，请先删除或修改相关订单",
                "data": None
            }
        )

    success = direct_sale_product_dao.delete(session=db, product_id=product_id)
    return {"message": "直销产品删除成功"}


# ReservationProduct endpoints
@router.post("/reservation/", response_model=ReservationProductResponse, status_code=status.HTTP_201_CREATED)
def create_reservation_product(product: ReservationProductCreate, db: Session = Depends(get_db)):
    """创建预订产品"""
    return reservation_product_dao.create(session=db, product=product)


@router.get("/reservation/{product_id}", response_model=ReservationProductWithRelations)
def read_reservation_product(product_id: int, db: Session = Depends(get_db)):
    """获取预订产品详情，包含关联的内容和标签"""
    db_product = reservation_product_dao.get(session=db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="预订产品不存在")
    return db_product


@router.get("/reservation/", response_model=List[ReservationProductResponse])
def read_reservation_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取预订产品列表"""
    return reservation_product_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/reservation/available/", response_model=List[ReservationProductResponse])
def read_available_reservation_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取可预订的产品列表"""
    return reservation_product_dao.get_available_reservations(session=db, skip=skip, limit=limit)


@router.put("/reservation/{product_id}", response_model=ReservationProductResponse)
def update_reservation_product(product_id: int, product: ReservationProductUpdate, db: Session = Depends(get_db)):
    """更新预订产品信息"""
    db_product = reservation_product_dao.update(session=db, product_id=product_id, product=product)
    if db_product is None:
        raise HTTPException(status_code=404, detail="预订产品不存在")
    return db_product


@router.delete("/reservation/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_reservation_product(product_id: int, db: Session = Depends(get_db)):
    """删除预订产品"""
    # 先检查是否有关联的订单项
    db_product = reservation_product_dao.get(session=db, product_id=product_id)
    if not db_product:
        raise HTTPException(status_code=404, detail="预订产品不存在")

    # 检查是否存在关联的订单项
    if db_product.order_items and len(db_product.order_items) > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": "无法删除预订产品，存在关联的订单项，请先删除或修改相关订单",
                "data": None
            }
        )

    # 检查是否存在关联的预约请求
    if db_product.reservation_requests and len(db_product.reservation_requests) > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": "无法删除预订产品，存在关联的预约请求，请先删除或修改相关预约",
                "data": None
            }
        )

    success = reservation_product_dao.delete(session=db, product_id=product_id)
    return {"message": "预订产品删除成功"}


# VirtualProduct endpoints
@router.post("/virtual/", response_model=VirtualProductResponse, status_code=status.HTTP_201_CREATED)
def create_virtual_product(product: VirtualProductCreate, db: Session = Depends(get_db)):
    """创建虚拟产品"""
    return virtual_product_dao.create(session=db, product=product)


@router.get("/virtual/{product_id}", response_model=VirtualProductWithRelations)
def read_virtual_product(product_id: int, db: Session = Depends(get_db)):
    """获取虚拟产品详情，包含关联的内容和标签"""
    db_product = virtual_product_dao.get(session=db, product_id=product_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="虚拟产品不存在")
    return db_product


@router.get("/virtual/", response_model=List[VirtualProductResponse])
def read_virtual_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取虚拟产品列表"""
    return virtual_product_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/virtual/by-type/{object_type}", response_model=List[VirtualProductResponse])
def read_virtual_products_by_type(object_type: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """根据对象类型获取虚拟产品列表"""
    return virtual_product_dao.get_by_object_type(session=db, object_type=object_type, skip=skip, limit=limit)


@router.get("/virtual/by-object/{object_id}", response_model=VirtualProductResponse)
def read_virtual_product_by_object_id(object_id: int, db: Session = Depends(get_db)):
    """根据对象ID获取虚拟产品"""
    db_product = virtual_product_dao.get_by_object_id(session=db, object_id=object_id)
    if db_product is None:
        raise HTTPException(status_code=404, detail="虚拟产品不存在")
    return db_product


@router.put("/virtual/{product_id}", response_model=VirtualProductResponse)
def update_virtual_product(product_id: int, product: VirtualProductUpdate, db: Session = Depends(get_db)):
    """更新虚拟产品信息"""
    db_product = virtual_product_dao.update(session=db, product_id=product_id, product=product)
    if db_product is None:
        raise HTTPException(status_code=404, detail="虚拟产品不存在")
    return db_product


@router.delete("/virtual/{product_id}")
def delete_virtual_product(product_id: int, db: Session = Depends(get_db)):
    """删除虚拟产品"""
    success = virtual_product_dao.delete(session=db, product_id=product_id)
    return {"message": "虚拟产品删除成功"}


# 获取产品标签端点
@router.get("/{product_id}/tags/", response_model=List[TagResponse])
def read_product_tags(product_id: int, db: Session = Depends(get_db)):
    """获取产品相关的标签列表"""
    product = product_dao.get(session=db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return tag_dao.get_by_product(session=db, product_id=product_id)


# 批量内容与产品绑定解绑端点
@router.post("/add/contents/", status_code=status.HTTP_200_OK, response_model=Dict)
def add_contents_to_product(binding: ProductAndContent, db: Session = Depends(get_db)):
    """批量将内容绑定到产品"""
    result = product_dao.add_contents(
        session=db,
        content_ids=binding.content_ids,
        product_id=binding.product_id
    )

    if not result["success"] and result["failed"]:
        message = "所有内容绑定失败"
    else:
        message = "内容批量绑定到产品操作完成"

    data = {
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }

    return {
        "code": 200,
        "message": message,
        "data": data
    }


@router.post("/remove/contents/", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_contents_from_product(binding: ProductAndContent, db: Session = Depends(get_db)):
    """批量将内容从产品解绑"""
    result = product_dao.remove_contents(
        session=db,
        content_ids=binding.content_ids,
        product_id=binding.product_id
    )

    if not result["success"] and result["failed"]:
        message = "所有内容解绑失败"
    else:
        message = "内容批量从产品解绑操作完成"

    data = {
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }

    return {
        "code": 200,
        "message": message,
        "data": data
    }


# 添加新端点：获取产品绑定的定价策略
@router.get("/{product_id}/pricing-strategies", response_model=List[PricingStrategyResponse])
def get_pricing_strategies_by_product(
        product_id: int,
        skip: int = 0,
        limit: int = 100,
        db: Session = Depends(get_db)
):
    """获取指定产品绑定的所有定价策略"""
    product = product_dao.get(session=db, product_id=product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")

    result = product_dao.get_pricing_strategies_by_product(
        session=db,
        product_id=product_id,
        skip=skip,
        limit=limit
    )
    return result["list"]


# 产品与定价策略绑定解绑端点
@router.post("/add/pricing-strategies/", status_code=status.HTTP_200_OK, response_model=Dict)
def add_pricing_strategies_to_product(binding: ProductAndPricingStrategy, db: Session = Depends(get_db)):
    """批量将定价策略绑定到产品"""
    result = product_dao.add_pricing_strategies(
        session=db,
        pricing_strategy_ids=binding.pricing_strategy_ids,
        product_id=binding.product_id
    )

    if not result["success"] and result["failed"]:
        message = "所有定价策略绑定失败"
    else:
        message = "定价策略批量绑定到产品操作完成"

    data = {
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }

    return {
        "code": 200,
        "message": message,
        "data": data
    }


@router.post("/remove/pricing-strategies/", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_pricing_strategies_from_product(binding: ProductAndPricingStrategy, db: Session = Depends(get_db)):
    """批量将定价策略从产品解绑"""
    result = product_dao.remove_pricing_strategies(
        session=db,
        pricing_strategy_ids=binding.pricing_strategy_ids,
        product_id=binding.product_id
    )

    if not result["success"] and result["failed"]:
        message = "所有定价策略解绑失败"
    else:
        message = "定价策略批量从产品解绑操作完成"

    data = {
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }

    return {
        "code": 200,
        "message": message,
        "data": data
    }


# 添加新端点：获取产品绑定的规则
@router.get("/{product_id}/rules", response_model=List[RuleResponse])
def get_rules_by_product(
        product_id: int,
        skip: int = 0,
        limit: int = 100,
        db: Session = Depends(get_db)
):
    """获取指定产品绑定的所有规则"""
    product = product_dao.get(session=db, product_id=product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")

    result = product_dao.get_rules_by_product(
        session=db,
        product_id=product_id,
        skip=skip,
        limit=limit
    )
    return result["list"]


# 产品与规则绑定解绑端点
@router.post("/add/rules/", status_code=status.HTTP_200_OK, response_model=Dict)
def add_rules_to_product(binding: ProductAndRule, db: Session = Depends(get_db)):
    """批量将规则绑定到产品"""
    result = product_dao.add_rules(
        session=db,
        rule_ids=binding.rule_ids,
        product_id=binding.product_id
    )

    if not result["success"] and result["failed"]:
        message = "所有规则绑定失败"
    else:
        message = "规则批量绑定到产品操作完成"

    data = {
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }

    return {
        "code": 200,
        "message": message,
        "data": data
    }


@router.post("/remove/rules/", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_rules_from_product(binding: ProductAndRule, db: Session = Depends(get_db)):
    """
    将规则从产品解绑

    Args:
        binding: 包含产品ID和规则ID列表的绑定信息
        db: 数据库会话

    Returns:
        解绑结果
    """
    result = product_dao.remove_rules(session=db, rule_ids=binding.rule_ids, product_id=binding.product_id)
    if result["failed"]:
        return {
            "code": 207,  # 部分成功
            "message": f"部分规则解绑失败，成功: {len(result['success'])}，失败: {len(result['failed'])}",
            "data": result
        }
    return {
        "code": 200,
        "message": "规则解绑成功",
        "data": result
    }


@router.post("/add/categories/", status_code=status.HTTP_200_OK, response_model=Dict)
def add_categories_to_product(binding: ProductAndCategory, db: Session = Depends(get_db)):
    """
    将分类绑定到产品

    Args:
        binding: 包含产品ID和分类ID列表的绑定信息
        db: 数据库会话

    Returns:
        绑定结果
    """
    result = product_dao.add_categories(session=db, category_ids=binding.category_ids, product_id=binding.product_id)
    if result["failed"]:
        return {
            "code": 207,  # 部分成功
            "message": f"部分分类绑定失败，成功: {len(result['success'])}，失败: {len(result['failed'])}",
            "data": result
        }
    return {
        "code": 200,
        "message": "分类绑定成功",
        "data": result
    }


@router.post("/remove/categories/", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_categories_from_product(binding: ProductAndCategory, db: Session = Depends(get_db)):
    """
    将分类从产品解绑

    Args:
        binding: 包含产品ID和分类ID列表的绑定信息
        db: 数据库会话

    Returns:
        解绑结果
    """
    result = product_dao.remove_categories(session=db, category_ids=binding.category_ids, product_id=binding.product_id)
    if result["failed"]:
        return {
            "code": 207,  # 部分成功
            "message": f"部分分类解绑失败，成功: {len(result['success'])}，失败: {len(result['failed'])}",
            "data": result
        }
    return {
        "code": 200,
        "message": "分类解绑成功",
        "data": result
    }


@router.post("/update/categories/", status_code=status.HTTP_200_OK, response_model=Dict)
def update_product_categories(binding: ProductAndCategory, db: Session = Depends(get_db)):
    """
    更新产品的分类绑定（先清空再重新绑定）

    Args:
        binding: 包含产品ID和分类ID列表的绑定信息
        db: 数据库会话

    Returns:
        更新结果
    """
    result = product_dao.update_categories(session=db, category_ids=binding.category_ids, product_id=binding.product_id)
    if result["failed"]:
        return {
            "code": 207,  # 部分成功
            "message": f"部分分类更新失败，成功: {len(result['success'])}，失败: {len(result['failed'])}",
            "data": result
        }
    return {
        "code": 200,
        "message": "分类更新成功",
        "data": result
    }


@router.get("/search/", response_model=ProductListFormattedResponse)
def search_products(request: ProductSearchRequest = Depends(), db: Session = Depends(get_db)):
    """
    根据条件搜索产品列表

    可以根据以下条件进行搜索:
    - keyword: 关键词，用于全文搜索
    - name: 产品名称
    - product_type: 产品类型
    - status: 产品状态
    """
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    products = product_dao.search(
        session=db,
        keyword=request.keyword,
        name=request.name,
        product_type=request.product_type,
        status=request.status,
        skip=skip,
        limit=limit
    )
    return {
        "code": 200,
        "message": "搜索产品列表成功",
        "data": products
    }


@router.post("/create", response_model=CommonResponse, status_code=status.HTTP_201_CREATED)
async def create_product(
        name: str = Form(...),
        type: str = Form(...),
        price: float = Form(...),
        stock: int = Form(0),
        product_status: int = Form(1, alias="status"),
        description: Optional[str] = Form(None),
        # 产品分类字段
        category_ids: Optional[str] = Form(None),
        # 直销产品字段
        shipping_fee: Optional[float] = Form(None),
        # 预订产品字段
        reservation_fee: Optional[float] = Form(None),
        max_reservations: Optional[int] = Form(None),
        is_approval_required: Optional[bool] = Form(False),
        # 虚拟产品字段
        object_id: Optional[int] = Form(None),
        object_type: Optional[str] = Form(None),
        # 图片上传
        image_file: Optional[UploadFile] = File(None),
        db: Session = Depends(get_db)
):
    """
    创建产品，支持form-data格式和图片上传

    支持创建不同类型的产品：
    - 直销产品 (direct_sale)
    - 预订产品 (reservation)
    - 虚拟产品 (virtual)
    """
    try:
        # 构建基础产品数据
        product_data = {
            "name": name,
            "price": price,
            "stock": stock,
            "description": description,
            "status": Status.ACTIVE if product_status == 1 else Status.INACTIVE
        }

        # 如果有上传文件，处理文件
        if image_file:
            # 重置文件指针位置
            await image_file.seek(0)
            # 保存图片并获取URL
            image_url, thumbnail_url = save_uploaded_image(image_file, folder_name="products")
            product_data["image"] = image_url

        # 根据产品类型创建对应的产品
        if type == ProductType.DIRECT_SALE.value:
            # 创建直销产品
            if shipping_fee is not None:
                product_data["shipping_fee"] = shipping_fee
            product_model = DirectSaleProductCreate(**product_data)
            db_result = direct_sale_product_dao.create(session=db, product=product_model)
            result = DirectSaleProductResponse.model_validate(db_result)
        elif type == ProductType.RESERVATION.value:
            # 创建预订产品
            if reservation_fee is not None:
                product_data["reservation_fee"] = reservation_fee
            if max_reservations is not None:
                product_data["max_reservations"] = max_reservations
            if is_approval_required is not None:
                product_data["is_approval_required"] = is_approval_required
            product_model = ReservationProductCreate(**product_data)
            db_result = reservation_product_dao.create(session=db, product=product_model)
            result = ReservationProductResponse.model_validate(db_result)
        elif type == ProductType.VIRTUAL.value:
            # 创建虚拟产品
            if object_id is None or object_type is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "code": 400,
                        "message": "虚拟产品必须提供object_id和object_type",
                        "data": None
                    }
                )
            product_data["object_id"] = object_id
            product_data["object_type"] = object_type
            product_model = VirtualProductCreate(**product_data)
            db_result = virtual_product_dao.create(session=db, product=product_model)
            result = VirtualProductResponse.model_validate(db_result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": f"不支持的产品创建类型: {type}",
                    "data": None
                }
            )

        # 处理产品分类绑定
        if category_ids:
            try:
                # 解析分类ID列表
                parsed_category_ids = json.loads(category_ids)
                if isinstance(parsed_category_ids, list) and parsed_category_ids:
                    # 绑定分类
                    category_result = product_dao.add_categories(db, parsed_category_ids, db_result.id)
                    if category_result["failed"]:
                        print(f"警告：部分分类绑定失败，失败的分类ID: {category_result['failed']}")
            except (json.JSONDecodeError, ValueError) as e:
                print(f"警告：分类ID解析失败: {e}")
                # 分类绑定失败不影响产品创建，只记录警告

        return {
            "code": 200,
            "message": f"创建{type}产品成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建产品失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/delete/{product_id}")
def delete_product(product_id: int, db: Session = Depends(get_db)):
    """删除产品"""
    # 先检查是否有关联的订单项
    db_product = product_dao.get(session=db, product_id=product_id)
    if not db_product:
        return JSONResponse({
            "code": 400,
            "message": "产品不存在",
            "data": None
        }, status_code=200)

    # 检查是否存在关联的订单项
    if db_product.order_items and len(db_product.order_items) > 0:
        return JSONResponse({
            "code": 400,
            "message": "无法删除产品，存在关联的订单项，请先删除或修改相关订单",
            "data": None
        }, status_code=200)

    success = product_dao.delete(session=db, product_id=product_id)
    return JSONResponse({"message": "产品删除成功"}, status_code=204)


@router.get("/view/{product_id}", response_model=CommonResponse)
def view_product(product_id: int, db: Session = Depends(get_db)):
    """
    查看产品详情

    根据传入的id，获取产品，再根据对应的类型，获取对应类型的产品详情
    """
    try:
        # 先获取基础产品信息，确定产品类型
        base_product = product_dao.get(session=db, product_id=product_id)
        if base_product is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "产品不存在",
                    "data": None
                }
            )

        # 根据产品类型获取详细信息
        if base_product.type == ProductType.DIRECT_SALE:
            db_result = direct_sale_product_dao.get(session=db, product_id=product_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "直销产品不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = DirectSaleProductResponse.model_validate(db_result)
        elif base_product.type == ProductType.RESERVATION:
            db_result = reservation_product_dao.get(session=db, product_id=product_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "预订产品不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = ReservationProductResponse.model_validate(db_result)
        else:
            # 将SQLAlchemy模型转换为Pydantic模型
            result = ProductResponse.model_validate(base_product)

        # 获取产品绑定的分类信息
        categories = []
        if base_product.categories:
            for category in base_product.categories:
                category_data = ProductCategoryResponse.model_validate(category)
                categories.append(category_data.model_dump())

        # 将结果转换为字典并添加分类信息
        result_data = result.model_dump()
        result_data["categories"] = categories

        return {
            "code": 200,
            "message": f"获取{base_product.type.value}产品详情成功",
            "data": result_data
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取产品详情失败: {str(e)}",
                "data": None
            }
        )


@router.put("/update", response_model=CommonResponse)
async def update_product(
        id: int = Form(..., alias="id"),
        name: Optional[str] = Form(None),
        type: Optional[str] = Form(None),
        price: Optional[float] = Form(None),
        stock: Optional[int] = Form(None),
        product_status: Optional[int] = Form(None, alias="status"),
        description: Optional[str] = Form(None),
        # 产品分类字段
        category_ids: Optional[str] = Form(None),
        # 直销产品字段
        shipping_fee: Optional[float] = Form(None),
        # 预订产品字段
        reservation_fee: Optional[float] = Form(None),
        max_reservations: Optional[int] = Form(None),
        is_approval_required: Optional[bool] = Form(None),
        reservation_deadline: Optional[str] = Form(None),
        cancellation_deadline: Optional[str] = Form(None),
        # 虚拟产品字段
        object_id: Optional[int] = Form(None),
        object_type: Optional[str] = Form(None),
        # 图片上传
        image_file: Optional[UploadFile] = File(None),
        db: Session = Depends(get_db)
):
    """
    更新产品信息，支持form-data格式和图片上传

    根据传入的id，判断对应的产品类型，找到对应的产品，进行更新
    """
    try:
        # 先获取基础产品信息，确定产品类型
        base_product = product_dao.get(session=db, product_id=id)
        if base_product is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "产品不存在",
                    "data": None
                }
            )

        # 构建更新数据字典
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if price is not None:
            update_data["price"] = price
        if stock is not None:
            update_data["stock"] = stock
        if description is not None:
            update_data["description"] = description
        if product_status is not None:
            update_data["status"] = Status.ACTIVE if product_status == 1 else Status.INACTIVE

        # 如果有上传文件，处理文件
        if image_file:
            # 重置文件指针位置
            await image_file.seek(0)
            # 保存图片并获取URL
            image_url, thumbnail_url = save_uploaded_image(image_file, folder_name="products")
            update_data["image"] = image_url

        # 根据产品类型更新信息
        if base_product.type == ProductType.DIRECT_SALE:
            if shipping_fee is not None:
                update_data["shipping_fee"] = shipping_fee
            product_data = DirectSaleProductUpdate(**update_data)
            db_result = direct_sale_product_dao.update(session=db, product_id=id, product=product_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "直销产品不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = DirectSaleProductResponse.model_validate(db_result)
        elif base_product.type == ProductType.RESERVATION:
            if reservation_fee is not None:
                update_data["reservation_fee"] = reservation_fee
            if max_reservations is not None:
                update_data["max_reservations"] = max_reservations
            if is_approval_required is not None:
                update_data["is_approval_required"] = is_approval_required
            # 处理时间字段
            if reservation_deadline is not None:
                from datetime import datetime
                try:
                    update_data["reservation_deadline"] = datetime.strptime(reservation_deadline, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail={
                            "code": 400,
                            "message": "预约截止时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式",
                            "data": None
                        }
                    )
            if cancellation_deadline is not None:
                from datetime import datetime
                try:
                    update_data["cancellation_deadline"] = datetime.strptime(cancellation_deadline, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail={
                            "code": 400,
                            "message": "取消截止时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式",
                            "data": None
                        }
                    )
            product_data = ReservationProductUpdate(**update_data)
            db_result = reservation_product_dao.update(session=db, product_id=id, product=product_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "预订产品不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = ReservationProductResponse.model_validate(db_result)
        elif base_product.type == ProductType.VIRTUAL:
            if object_id is not None:
                update_data["object_id"] = object_id
            if object_type is not None:
                update_data["object_type"] = object_type
            product_data = VirtualProductUpdate(**update_data)
            db_result = virtual_product_dao.update(session=db, product_id=id, product=product_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "虚拟产品不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = VirtualProductResponse.model_validate(db_result)
        else:
            product_data = ProductUpdate(**update_data)
            db_result = product_dao.update(session=db, product_id=id, product=product_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "产品不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = ProductResponse.model_validate(db_result)

        # 处理产品分类绑定
        if category_ids is not None:  # 注意这里是 is not None，因为空字符串也要处理（表示清空分类）
            try:
                # 解析分类ID列表
                if category_ids.strip():  # 如果不是空字符串
                    parsed_category_ids = json.loads(category_ids)
                    if not isinstance(parsed_category_ids, list):
                        parsed_category_ids = []
                else:
                    parsed_category_ids = []  # 空字符串表示清空分类
                
                # 更新分类绑定（清空后重新绑定）
                category_result = product_dao.update_categories(db, parsed_category_ids, id)
                if category_result["failed"]:
                    print(f"警告：部分分类绑定失败，失败的分类ID: {category_result['failed']}")
            except (json.JSONDecodeError, ValueError) as e:
                print(f"警告：分类ID解析失败: {e}")
                # 分类绑定失败不影响产品更新，只记录警告

        return {
            "code": 200,
            "message": f"更新{base_product.type.value}产品成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新产品失败: {str(e)}",
                "data": None
            }
        )


@router.post("/status/{product_id}", response_model=Dict)
def update_product_status(product_id: int, request: ProductStatusUpdateRequest, db: Session = Depends(get_db)):
    """修改产品状态"""
    try:
        result = product_dao.update(
            session=db,
            product_id=product_id,
            product=ProductUpdate(status=request.status)
        )
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "产品不存在",
                    "data": None
                }
            )

        response_model = ProductResponse.model_validate(result)
        return {
            "code": 200,
            "message": "产品状态更新成功",
            "data": response_model.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"产品状态更新失败: {str(e)}",
                "data": None
            }
        )


@router.get("/contents/search", response_model=ProdContentsSearchResp)
def get_product_contents_by_search(request: ProdContentsSearchReq = Depends(), db: Session = Depends(get_db)):
    """获取指定产品绑定的所有定价策略"""
    product = product_dao.get(session=db, product_id=request.product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    contents = product_dao.get_product_contents_by_search(
        session=db,
        product_id=request.product_id,
        skip=skip,
        limit=limit
    )
    return {
        "code": 200,
        "message": "搜索规则列表成功",
        "data": contents
    }


@router.get("/pricing-strategies/search", response_model=ProdPricingStrategiesSearchResp)
def get_pricing_strategies_by_product(request: ProdPricingStrategiesSearchReq = Depends(), db: Session = Depends(get_db)):
    """获取指定产品绑定的所有定价策略"""
    product = product_dao.get(session=db, product_id=request.product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")

    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    strategies = product_dao.get_pricing_strategies_by_product(
        session=db,
        product_id=request.product_id,
        skip=skip,
        limit=limit
    )
    return {
        "code": 200,
        "message": "搜索规则列表成功",
        "data": strategies
    }


@router.get("/rules/search", response_model=ProdRulesSearchResp)
def get_rules_by_product(request: ProdRulesSearchReq = Depends(), db: Session = Depends(get_db)):
    """获取指定产品绑定的所有规则"""
    product = product_dao.get(session=db, product_id=request.product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")

    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    rules = product_dao.get_rules_by_product(
        session=db,
        product_id=request.product_id,
        skip=skip,
        limit=limit
    )
    return {
        "code": 200,
        "message": "搜索规则列表成功",
        "data": rules
    }


@router.get("/dining-rules/{product_name}", response_model=ProductWithDiningRulesResponse)
def get_product_with_dining_rules(product_name: str, db: Session = Depends(get_db)):
    """
    根据产品名称关键词获取匹配的产品及其关联的餐厅预订规则
    
    Args:
        product_name: 产品名称关键词，支持模糊匹配
        db: 数据库会话
        
    Returns:
        包含匹配到的产品及其关联的餐厅预订规则的响应
    """
    result = product_dao.get_product_with_dining_rules(session=db, product_name=product_name)
    
    if result["total"] == 0:
        return {
            "code": 404,
            "message": f"未找到名称包含 '{product_name}' 的产品",
            "data": {"total": 0, "products": []}
        }
        
    return {
        "code": 200, 
        "message": f"获取产品及餐厅预订规则成功，共找到 {result['total']} 个产品",
        "data": result
    }


@router.get("/dining-rules-by-id/{product_id}", response_model=ProductWithDiningRulesResponse)
def get_product_with_dining_rules_by_id(product_id: int, db: Session = Depends(get_db)):
    """
    根据产品ID获取产品及其关联的餐厅预订规则
    
    Args:
        product_id: 产品ID
        db: 数据库会话
        
    Returns:
        包含指定产品及其关联的餐厅预订规则的响应
    """
    result = product_dao.get_product_with_dining_rules_by_id(session=db, product_id=product_id)
    
    if result["total"] == 0:
        return {
            "code": 404,
            "message": f"未找到ID为 {product_id} 的产品",
            "data": {"total": 0, "products": []}
        }
        
    return {
        "code": 200, 
        "message": "获取产品及餐厅预订规则成功",
        "data": result
    }


@router.get("/search/name", response_model=ProductNameSearchResponse)
def search_products_by_name(
    name: str,
    db: Session = Depends(get_db)
):
    """
    根据产品名称进行模糊搜索
    """
    products = product_dao.search_by_name(db, name)
    return {
        "code": 200,
        "message": "搜索成功",
        "data": products
    } 