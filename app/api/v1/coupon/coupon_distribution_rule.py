from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.coupon import coupon_distribution_rule_dao
from app.models.coupon import DistributionRuleType
from app.schemas.common import CommonResponse
from app.schemas.coupon import (
    CouponDistributionRuleCreate, CouponDistributionRuleUpdate, CouponDistributionRuleResponse,
    CouponDistributionRuleSearch, CouponDistributionRuleListResponse, CouponDistributionRuleListData,
    CouponDistributionRuleStatusUpdateRequest
)
from app.models.enum import Status

router = APIRouter()


def _validate_function_json(func_json: dict, func_type: str) -> None:
    """
    验证函数JSON对象的结构
    
    Args:
        func_json: 函数JSON对象
        func_type: 函数类型，用于错误提示
    """
    if not isinstance(func_json, dict):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{func_type}必须是JSON对象格式"
        )
    
    for key, func_def in func_json.items():
        if not isinstance(func_def, dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{func_type}中的'{key}'必须是对象格式"
            )
        
        # 验证必填字段
        required_fields = ["name", "func_name", "params"]
        for field in required_fields:
            if field not in func_def:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"{func_type}中的'{key}'缺少必填字段'{field}'"
                )
        
        # 验证字段类型
        if not isinstance(func_def["name"], str):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{func_type}中的'{key}'.name必须是字符串"
            )
        
        if not isinstance(func_def["func_name"], str):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{func_type}中的'{key}'.func_name必须是字符串"
            )
        
        if not isinstance(func_def["params"], dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{func_type}中的'{key}'.params必须是对象格式"
            )


@router.post("/distribution-rules/", response_model=CommonResponse)
def create_distribution_rule(
    rule: CouponDistributionRuleCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    创建优惠券发放规则
    """
    try:
        # 验证规则类型和必填字段
        if rule.type == DistributionRuleType.USER_FEATURE:
            if not rule.user_feature_func:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户特征类型规则必须指定用户特征函数"
                )
            _validate_function_json(rule.user_feature_func, "用户特征函数")
        
        if rule.type == DistributionRuleType.USER_BEHAVIOR:
            if not rule.user_behavior_func:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户行为类型规则必须指定用户行为函数"
                )
            _validate_function_json(rule.user_behavior_func, "用户行为函数")
        
        if rule.type == DistributionRuleType.USER_FEATURE_AND_BEHAVIOR:
            if not rule.user_feature_func or not rule.user_behavior_func:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户特征与行为类型规则必须同时指定用户特征函数和用户行为函数"
                )
            _validate_function_json(rule.user_feature_func, "用户特征函数")
            _validate_function_json(rule.user_behavior_func, "用户行为函数")
        
        # 转换为字典格式
        rule_data = rule.model_dump()
        
        # 处理发放内容
        if rule_data.get('distribution_content'):
            rule_data['distribution_content'] = [item.model_dump() for item in rule.distribution_content]
        
        db_rule = coupon_distribution_rule_dao.create(db, rule_data)
        
        return CommonResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleResponse.model_validate(db_rule)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建发放规则失败: {str(e)}"
        )


@router.get("/distribution-rules/{rule_id}", response_model=CommonResponse)
def get_distribution_rule(
    rule_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """
    获取优惠券发放规则详情
    """
    db_rule = coupon_distribution_rule_dao.get(db, rule_id)
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="发放规则不存在"
        )
    
    return CommonResponse(
        code=200,
        message="success",
        data=CouponDistributionRuleResponse.model_validate(db_rule)
    )


@router.put("/distribution-rules/{rule_id}", response_model=CommonResponse)
def update_distribution_rule(
    rule_id: int,
    rule: CouponDistributionRuleUpdate,
    db: Session = Depends(get_db)
) -> Any:
    """
    更新优惠券发放规则
    """
    try:
        # 检查规则是否存在
        db_rule = coupon_distribution_rule_dao.get(db, rule_id)
        if not db_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="发放规则不存在"
            )
        
        # 转换为字典格式
        rule_data = rule.model_dump(exclude_unset=True)
        
        # 获取最终的规则类型和函数值（合并更新数据和现有数据）
        final_type = rule_data.get('type', db_rule.type)
        final_user_feature_func = rule_data.get('user_feature_func', db_rule.user_feature_func)
        final_user_behavior_func = rule_data.get('user_behavior_func', db_rule.user_behavior_func)
        
        # 验证规则类型和必填字段（与create_distribution_rule保持一致）
        if final_type == DistributionRuleType.USER_FEATURE:
            if not final_user_feature_func:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户特征类型规则必须指定用户特征函数"
                )
            _validate_function_json(final_user_feature_func, "用户特征函数")
        
        if final_type == DistributionRuleType.USER_BEHAVIOR:
            if not final_user_behavior_func:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户行为类型规则必须指定用户行为函数"
                )
            _validate_function_json(final_user_behavior_func, "用户行为函数")
        
        if final_type == DistributionRuleType.USER_FEATURE_AND_BEHAVIOR:
            if not final_user_feature_func or not final_user_behavior_func:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户特征与行为类型规则必须同时指定用户特征函数和用户行为函数"
                )
            _validate_function_json(final_user_feature_func, "用户特征函数")
            _validate_function_json(final_user_behavior_func, "用户行为函数")
        
        # 处理发放内容
        if 'distribution_content' in rule_data and rule_data['distribution_content']:
            rule_data['distribution_content'] = [item.model_dump() for item in rule.distribution_content]
        
        updated_rule = coupon_distribution_rule_dao.update(db, rule_id, **rule_data)
        
        return CommonResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleResponse.model_validate(updated_rule)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新发放规则失败: {str(e)}"
        )


@router.delete("/distribution-rules/{rule_id}", response_model=CommonResponse)
def delete_distribution_rule(
    rule_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """
    删除优惠券发放规则
    """
    try:
        # 检查规则是否存在
        db_rule = coupon_distribution_rule_dao.get(db, rule_id)
        if not db_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="发放规则不存在"
            )
        
        success = coupon_distribution_rule_dao.delete(db, rule_id)
        
        return CommonResponse(
            code=200,
            message="success",
            data=success
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除发放规则失败: {str(e)}"
        )


@router.get("/distribution-rules/", response_model=CouponDistributionRuleListResponse)
def list_distribution_rules(
    keyword: str = Query(None, description="关键词搜索"),
    rule_type: DistributionRuleType = Query(None, description="规则类型"),
    rule_status: Status = Query(None, description="规则状态"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="每页数量"),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取优惠券发放规则列表
    """
    try:
        search_params = CouponDistributionRuleSearch(
            keyword=keyword,
            type=rule_type,
            status=rule_status
        )
        
        result = coupon_distribution_rule_dao.search(
            db, 
            keyword=search_params.keyword,
            rule_type=search_params.type,
            status=search_params.status,
            skip=skip, 
            limit=limit
        )
        
        # 转换为响应模型
        rule_responses = [
            CouponDistributionRuleResponse.model_validate(rule) 
            for rule in result["list"]
        ]
        
        return CouponDistributionRuleListResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleListData(
                total=result["total"],
                list=rule_responses
            )
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取发放规则列表失败: {str(e)}"
        )


@router.patch("/distribution-rules/{rule_id}/status", response_model=CommonResponse)
def update_distribution_rule_status(
    rule_id: int,
    status_request: CouponDistributionRuleStatusUpdateRequest,
    db: Session = Depends(get_db)
) -> Any:
    """
    更新优惠券发放规则状态
    """
    try:
        # 检查规则是否存在
        db_rule = coupon_distribution_rule_dao.get(db, rule_id)
        if not db_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="发放规则不存在"
            )
        
        updated_rule = coupon_distribution_rule_dao.update_status(db, rule_id, status_request.status)
        
        return CommonResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleResponse.model_validate(updated_rule)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新发放规则状态失败: {str(e)}"
        )
