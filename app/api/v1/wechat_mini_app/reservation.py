# -*- coding: utf-8 -*-
# 预约模块


from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


def parse_booking_date(date_str: str) -> datetime.date:
    """
    解析前端传递的日期字符串，兼容多种格式
    支持的格式：
    - "MM-DD" (如 "07-23") - 所有现有页面使用的格式
    - "YYYY-MM-DD" (如 "2025-07-23") - 完整日期格式
    """
    try:
        # 尝试解析完整日期格式 "YYYY-MM-DD"
        if len(date_str) == 10 and date_str.count('-') == 2:
            return datetime.strptime(date_str, "%Y-%m-%d").date()

        # 解析简短日期格式 "MM-DD" （当前所有页面使用的格式）
        elif len(date_str) == 5 and date_str.count('-') == 1:
            current_year = datetime.now().year
            return datetime.strptime(f"{current_year}-{date_str}", "%Y-%m-%d").date()

        else:
            raise ValueError(f"不支持的日期格式: {date_str}")

    except Exception as e:
        logger.error(f"日期解析失败: {date_str}, 错误: {str(e)}")
        # 如果解析失败，返回今天的日期作为fallback
        return datetime.now().date()


@router.get("/reserve/config")
async def get_reserve_config(
        type: str,  # 预约类型
        source: Optional[str] = None,  # 添加source参数，默认为None
        meal_type: Optional[str] = None,  # 添加meal_type参数，默认为None
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取预约配置
    Args:
        type: 预约类型
        source: 请求来源（topic或admin）
        meal_type: 餐类
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含预约配置信息的响应
    """
    logger.info(f"开始获取预约配置")
    logger.info(f"请求来源: {source}")  # 记录请求来源

    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    logger.info(f"用户token验证成功，用户ID: {user.id}")
    logger.info(f"预约类型: {type}")
    user_id = user.id

    qr_type = "dynamic"
    logger.info(f"二维码类型: {qr_type}")

    # 传递source参数给service层
    return reservation_service.get_reservation_info(db, type, user_id, source=source, meal_type=meal_type, qr_type=qr_type)


@router.post("/reserve/submit")
async def submit_reserve(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """提交预订信息
    Args:
        task_info: 预订信息，包含：
            - bookings: 预订信息列表
            - coupon_id: 优惠券ID字符串，格式如"14,17"
            - coupon_discount: 预期优惠金额
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含预订结果信息的响应
    """
    try:
        logger.info(f"开始提交预订信息，接收到的参数: {task_info}")

        # 验证用户token
        logger.info(f"开始验证用户token: {token[:10] if token else 'None'}")
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )
        logger.info(f"用户token验证成功，用户ID: {user.id}")

        # 提取优惠券参数
        coupon_id_str = task_info.get("coupon_id", "")
        coupon_discount = task_info.get("coupon_discount", 0)

        # 解析优惠券ID列表
        coupon_usage_record_ids = []
        if coupon_id_str and coupon_id_str.strip():
            try:
                coupon_usage_record_ids = [int(id.strip()) for id in coupon_id_str.split(",") if id.strip()]
                logger.info(f"解析优惠券ID列表: {coupon_usage_record_ids}")
            except ValueError as e:
                logger.error(f"优惠券ID格式错误: {coupon_id_str}, 错误: {str(e)}")
                return {
                    "message": "优惠券ID格式错误",
                    "status": 400,
                    "reservation_result": None
                }

        logger.info(f"优惠券参数 - coupon_usage_record_ids: {coupon_usage_record_ids}, coupon_discount: {coupon_discount}")

        # 验证预订订单对应的规则库存是否满足
        # 获取已预约人数（状态为待支付、已支付定金、已支付全款和已核销的预约）不计入已取消的预约
        valid_statuses = [
            # ReservationStatus.PENDING,
            ReservationStatus.PAID_DEPOSIT,
            ReservationStatus.PAID_FULL,
            ReservationStatus.VERIFIED
        ]
        for booking in task_info["bookings"]:
            # 获取总可预约人数
            rule_item = rule_item_dao.get(db, booking["rule_item_id"])
            total_capacity = rule_item.quantity

            # 累加每个预约请求关联的订单项的quantity值，而不是计算预约请求数量
            reserved_count = 0
            reservation_requests = db.query(reservation_request_dao.model).filter(
                reservation_request_dao.model.rule_item_id == booking["rule_item_id"],
                reservation_request_dao.model.status.in_(valid_statuses)
            ).all()

            logger.info(f"预订请求数量: {len(reservation_requests)}")

            for req in reservation_requests:
                try:
                    # 获取预订订单的就餐日期
                    reservation_period = req.reservation_period  # 2505091000_2505091200
                    reservation_period_start, reservation_period_end, reservation_time = revervation_service.extract_start_end_times(
                        reservation_period)

                    # 将 reservation_period_start 转换为日期对象进行比较
                    # reservation_period_start 的格式是 "2025-07-23 11:30"
                    reservation_date = datetime.strptime(reservation_period_start, "%Y-%m-%d %H:%M").date()

                    # 使用兼容的日期解析函数处理前端传递的日期
                    booking_date = parse_booking_date(booking["date"])

                    # 比较日期
                    if booking_date == reservation_date:
                        # 日期匹配，继续处理
                        reserved_count += req.order_item.quantity
                    else:
                        continue  # 日期不匹配，跳过当前预约

                except Exception as e:
                    logger.error(f"处理预订请求时出错: {str(e)}, reservation_period: {req.reservation_period}")
                    continue  # 如果某个预订请求处理出错，跳过并继续处理其他的

            # 计算剩余可预约人数
            remaining_capacity = total_capacity - reserved_count

            if booking["quantity"] > remaining_capacity:
                logger.error(f"日期：{booking['date']} 时段: {booking['timeSlot']} 对应的库存不足, 请重新选择")
                return {
                    "message": f"日期：{booking['date']} 时段: {booking['timeSlot']} 对应的库存不足, 请重新选择",
                    "status": 500,
                    "reservation_result": None
                }

        # 验证预订信息
        logger.info(f"开始验证预订信息，接收到的参数: {task_info}")

        # 从请求中获取 source 参数，如果没有则默认为 None
        source = task_info.get("source", None)

        reservation_result = reservation_service.submit_reservation(
            db, task_info["bookings"], user.id, source,
            coupon_usage_record_ids, coupon_discount
        )
        logger.info(f"预订信息验证成功，预订结果: {reservation_result}")

        if reservation_result:
            return {
                "message": "提交成功",
                "status": 200,
                "reservation_result": reservation_result
            }
        else:
            return {
                "message": "提交失败",
                "status": 500,
                "reservation_result": None
            }
    except Exception as e:
        logger.error(f"提交预订信息失败: {str(e)}")
        return {
            "message": f"提交预订信息失败: {str(e)}",
            "status": 500,
            "reservation_result": None
        }


@router.get("/reserve/list")
async def get_reserve_list(
        date: Optional[str] = None,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取预约记录
    Args:
        date: 日期过滤参数
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含预约记录信息的响应
    """
    logger.info(f"开始验证token: {token[:10]}..." if token else "token为空")

    if not token:
        logger.warning("请求中未提供token")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)

    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    logger.info(f"token验证成功，用户ID: {user.id}")
    user_info = WeChatUserService.get_user_info(db, user.wechat_id)
    logger.debug(f"获取到的用户信息: {user_info}")

    # 强化日期参数验证和清理
    clean_date = None
    if date:
        # 去除首尾空格
        date = date.strip()

        # 过滤掉常见地无效值
        invalid_values = ['undefined', 'null', 'none', '', 'false', 'true']
        if date.lower() not in invalid_values:
            try:
                # 验证日期格式是否正确
                datetime.strptime(date, "%Y-%m-%d")
                clean_date = date
                logger.info(f"有效日期参数: {clean_date}")
            except ValueError as e:
                logger.warning(f"无效的日期格式: {date}, 错误: {str(e)}")
                # 可以选择抛出异常或者忽略无效日期
                # 这里选择忽略无效日期，按照查询全部来处理
                clean_date = None
        else:
            logger.warning(f"过滤掉无效日期值: {date}")

    # 获取用户订单列表
    logger.info(f"开始获取用户订单，用户ID: {user_info}, 清理后的日期参数: {clean_date}")
    order_list = reservation_service.get_reservation_list(db, user_info["id"], clean_date)

    logger.info(f"获取到订单数量: {len(order_list)}")

    return {
        "message": "获取成功",
        "status": 200,
        "orders": order_list,
        "count": len(order_list)
    }


@router.post("/reserve/cancel")  # 弃用
async def cancel_reserve(
        task_info: Dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """取消预约
    Args:
        task_info: 预约订单
        token: 用户token
        db: 数据库会话
    """
    try:
        logger.info(f"开始取消预约，接收到的参数: {task_info}")

        # 验证用户token
        logger.info(f"开始验证用户token: {token[:10] if token else 'None'}")
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )
        user_id = user.id

        # 验证预约ID
        if not task_info["order"]:
            logger.error("预约ID为空")
            raise HTTPException(
                status_code=400,
                detail={"message": "预约ID不能为空", "code": 400}
            )

        order = task_info["order"]
        order_object = order_dao.get(db, order["order_id"])
        if not order_object:
            logger.error("订单不存在")
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "code": 404}
            )
        order_id = order_object.id
        order_no = order_object.order_no

        # 验证预约是否存在
        reservation = reservation_request_dao.get(db, order["reservation_request_id"])
        if not reservation:
            logger.error("预约不存在")
            raise HTTPException(
                status_code=404,
                detail={"message": "预约不存在", "code": 404}
            )

        # 只运行订单就餐前一天22点前可以取消
        reservation_start_time = datetime.strptime('20' + reservation.reservation_period.split("_")[0], "%Y%m%d%H%M")
        current_time = datetime.now()
        # 计算预约日期前一天的22点
        limit_date = reservation_start_time.replace(hour=22, minute=0, second=0) - timedelta(days=1)
        # 判断当前时间是否已超过取消订单的限制时间
        if current_time > limit_date:
            logger.error(f"取消失败，已超过取消时间限制，预约时间: {reservation_start_time}，取消截止时间: {limit_date}")
            return {
                "message": "取消失败，已超过可取消时间，只能在就餐前一天22点前取消预约",
                "status": 500
            }

        # 获取微信支付记录
        payment_record = wx_payment_dao.get_payment_by_order_no(db, order_no)

        reservation_order_status = reservation.status
        order_payment_method = order_object.payment_method
        reservation_request_id = reservation.id
        logger.info(f"预约订单状态: {reservation_order_status}")
        logger.info(f"预约订单支付方式: {order_payment_method}")

        # 取消预约
        update_reservation_request = reservation_request_dao.cancel_reservation(db, reservation.id)
        if update_reservation_request:
            logger.info(f"预约取消成功，预约ID: {reservation.id}")
        else:
            logger.error("取消失败，预约取消失败")
            return {
                "message": "取消失败，预约取消失败",
                "status": 500
            }

        # 更新为：取消订单(第一步标记为取消， 后面根据支付方式，再更新为取消并退款， 同时把支付状态也更新)
        update_order = order_dao.cancel_update_status(db, order_id, OrderStatus.CANCELLED)
        order = order_dao.get(db, order_id)
        if update_order:
            logger.info(f"订单取消成功，订单ID: {order_id}")
        else:
            logger.error("取消失败，订单取消失败")
            return {
                "message": "取消失败，订单取消失败",
                "status": 500
            }

        # 退款
        order_state = None
        if reservation_order_status == ReservationStatus.PAID_FULL:
            # 退款
            if order_payment_method == PaymentMethod.WECHAT_PAY:
                # 调用微信退款接口
                cancel_reservation_request_state = reservation_service.cancel_reservation(db, reservation_request_id)
                if cancel_reservation_request_state:
                    # 调用微信退款接口
                    if not payment_record:
                        logger.error("取消失败，支付记录不存在")
                        return {
                            "message": "取消失败，支付记录不存在",
                            "status": 500
                        }
                    else:
                        logger.info("支付记录存在，进行原路退款")
                        wechat_service.create_refund(payment_record.transaction_id, payment_record.order_no,
                                                     payment_record.total_amount, payment_record.payer_total,
                                                     "订单「" + payment_record.order_no + "」预约取消退款")
                    logger.info("退款成功")
                else:
                    logger.error("退款失败")
                    return {
                        "message": "取消失败，退款失败",
                        "status": 500
                    }
            elif order_payment_method == PaymentMethod.ACCOUNT_BALANCE:
                # 调用账户余额退款接口
                accounts = account_dao.get_by_user_id(db, user_id)

                if not accounts:
                    raise ValueError("用户账户不存在")

                regular_account = None
                for account in accounts:
                    if account.type == AccountType.REGULAR:
                        regular_account = account
                        break

                if not regular_account:
                    raise ValueError("用户普通账户不存在")

                # 退还金额到账户余额
                regular_account.balance += order.actual_amount_paid

                # 创建退款交易记录
                refund_transaction = AccountTransactionCreate(
                    account_id=regular_account.id,
                    order_id=order_id,
                    transaction_type=TransactionType.REFUND,
                    amount=order.actual_amount_paid,  # 注意这里是正数，因为是退款
                    description=f"订单退款：{order_id}",
                    transaction_time=get_current_time()
                )
                account_transaction_dao.create(db, refund_transaction)

                # 提交事务
                db.commit()
            elif order_payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                # 调用企业账户退款接口
                account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
                if not account_transactions:
                    raise ValueError("企业账户交易记录不存在")
                account_id = account_transactions[0].account_id
                logger.info(f"企业账户ID: {account_id}")

                accounts = account_dao.get_by_id(db, account_id)
                logger.info(f"企业账户: {accounts}")

                if not accounts:
                    raise ValueError("用户账户不存在")

                regular_account = None
                for account in accounts:
                    if account.type == AccountType.REGULAR:
                        regular_account = account
                        logger.info(f"企业账户: {regular_account}")
                        break

                if not regular_account:
                    raise ValueError("用户普通账户不存在")

                # 退还金额到账户余额
                regular_account.balance += order.actual_amount_paid
                logger.info(f"退还金额到账户余额: {regular_account.balance}")

                # 创建退款交易记录
                refund_transaction = AccountTransactionCreate(
                    account_id=regular_account.id,
                    order_id=order_id,
                    transaction_type=TransactionType.REFUND,
                    amount=order.actual_amount_paid,  # 注意这里是正数，因为是退款
                    description=f"订单退款：{order_id}",
                    transaction_time=get_current_time()
                )
                account_transaction_dao.create(db, refund_transaction)
                logger.info(f"退还金额到账户余额: {regular_account.balance}")

                # 提交事务
                db.commit()

            else:
                logger.error("退款失败，不支持的支付方式")
                return {
                    "message": "取消失败，退款失败，不支持的支付方式",
                    "status": 500
                }

            order_state = OrderStatus.REFUNDED

        # 更新订单状态，把"取消订单"改为"取消并退款
        if order_state:
            # 更新订单状态, 同时更新支付状态
            update_order = order_dao.cancel_order(db, order_id)
            if update_order:
                logger.info(f"订单退款成功")
            else:
                logger.error("订单退款失败")
                return {
                    "message": "取消失败，订单退款失败",
                    "status": 500
                }

        return {
            "message": "取消成功",
            "status": 200
        }
    except Exception as e:
        logger.error(f"取消预约失败: {str(e)}")
        return {
            "message": f"取消预约失败: {str(e)}",
            "status": 500
        }
