# -*- coding: utf-8 -*-
# 订单模块

from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


@router.post("/order/pay/create")
async def create_order_pay(task_info: dict, event_bus: EventBusDep, token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    创建订单支付
    """
    try:
        logger.info(f"开始创建订单支付，接收到的参数: {task_info}")

        # 验证用户token
        logger.info(f"开始验证用户token: {token[:10] if token else 'None'}")
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证订单ID
        if not task_info["order_no"]:
            logger.error("订单号为空")
            raise HTTPException(
                status_code=400,
                detail={"message": "订单号不能为空", "code": 400}
            )

        # 验证充值金额
        if not task_info["amount"]:
            logger.error("充值金额为空")
            raise HTTPException(
                status_code=400,
                detail={"message": "充值金额不能为空", "code": 400}
            )

        logger.info(f"充值金额验证通过: {task_info['amount']}")

        # 获取订单
        logger.info(f"开始获取订单，订单号: {task_info['order_no']}")
        order = order_dao.get_by_order_no(db, task_info["order_no"])

        # 检查订单是否存在
        if not order:
            logger.error("订单不存在")
            raise HTTPException(
                status_code=500,
                detail={"message": "订单不存在", "code": 500}
            )
        logger.info(f"订单存在，订单号: {order.order_no}")

        # 立即保存订单ID和订单号，避免后续访问失效的实例
        order_id = order.id
        order_no = order.order_no
        order_user_id = order.user_id
        order_payable_amount = order.payable_amount
        order_status = order.status
        order_payment_status = order.payment_status

        # 更改订单类型
        order_dao.update_order_type(db, order_id, OrderType.RESERVATION)

        # 更新订单支付方式
        # 使用微信支付流程
        if task_info["paymentMethod"] == "wxpay":
            order_dao.update_payment_method(db, order_id, PaymentMethod.WECHAT_PAY)

            # 微信支付则在小程序端支付完后，回调数据进行更新，不在此次更新。
            logger.info(f"订单支付方式更新成功: 微信支付")

            # 并没有实现支付，而是只是生成支付参数，为微信支付做准备
            # 生成充值订单支付参数
            logger.info("开始生成微信支付参数")
            try:
                # task_info["amount"] = 0.01 # 测试用
                class_wechat_service = WechatService()
                pay_params = class_wechat_service.create_jsapi_payment(
                    user.wechat_id,
                    order_no,
                    task_info["amount"],
                    "订单支付"
                )
                logger.info(f"微信支付参数生成成功: {pay_params}")
            except Exception as e:
                logger.error(f"生成微信支付参数失败: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail={"message": f"生成支付参数失败: {str(e)}", "code": 500}
                )

            return {
                "message": "创建成功",
                "status": 200,
                "payParams": pay_params
            }
        # 使用个人账户余额支付流程
        elif task_info["paymentMethod"] == "balance":
            order_dao.update_payment_method(db, order_id, PaymentMethod.ACCOUNT_BALANCE)
            # 进行余额扣款
            logger.info(f"余额扣款成功，扣款金额: {task_info['amount']}")
            payment_info = {
                "payment_method": PaymentMethod.ACCOUNT_BALANCE
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            # 个人账户余额支付成功处理
            if paid_order:
                logger.info(f"余额支付成功，支付金额: {task_info['amount']}")

                # 更新产品库存 - 使用原始订单ID
                order_service.update_product_stock(db, order_id)
                logger.info(f"产品库存更新成功，订单ID: {order_id}")

                # TODO：个人支付成功，虚拟产品交付
                # 如果是虚拟产品，执行交付

                # 发布订单支付成功事件(余额支付)事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.PAID,
                        order_id=order_id,
                        order_no=order_no,
                        user_id=order_user_id,
                        amount=order_payable_amount,  # 转换为元
                        status=str(order_status),
                        payment_status=str(order_payment_status),
                        source="miniapp_order_payment_by_balance",
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"订单支付成功事件(余额支付)已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"发布订单支付成功事件(余额支付)失败: {e}")

                return {
                    "message": "余额支付成功",
                    "status": 200
                }
            else:
                logger.error("余额支付失败")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "余额支付失败", "code": 500}
                )
        # 使用企业账户余额支付流程
        elif task_info["paymentMethod"] == "enterprise":
            if not task_info["enterprise_id"]:
                logger.error("企业ID为空")
                return {
                    "message": "找不到关联企业",
                    "status": 400
                }

            # TODO：一般产品支付跳过该环节
            # 检查当日是否存在同一类型的支付
            # 一般产品购买需要跳过该环节

            # 标记订单消费日期
            order_item_list = order_item_dao.get_by_order(db, order_id)
            # 按餐食类型检查是否存在重复订单
            conflict_info = []
            if order_item_list:
                for tmp_order_item in order_item_list:
                    logger.info(f"订单项: {tmp_order_item}")
                    tmp_reservation_request = reservation_request_dao.get_by_order_item_id_and_user_id(db,
                                                                                                       tmp_order_item.id,
                                                                                                       user.id)
                    reservation_period = tmp_reservation_request.reservation_period
                    reservation_period_start = reservation_period.split("_")[0]
                    reservation_period_start = '20' + reservation_period_start[0:6]
                    reservation_period_start = datetime.strptime(reservation_period_start, '%Y%m%d')
                    request_date_list = [reservation_period_start.date()]

                    # 获取该预订请求的餐食类型
                    rule_item = rule_item_dao.get(db, tmp_reservation_request.rule_item_id)
                    meal_type = rule_item.meal_type if rule_item else None

                    logger.info(
                        f"=== 支付检查 ===: 检查日期 {reservation_period_start.date()} 的餐食类型 {meal_type.value if meal_type else '未知'}")

                    # 查询该日期该餐食类型是否已经存在企业账户支付的订单
                    has_enterprise_order, existing_reservation_period_start = order_dao.check_enterprise_order_in_day_by_meal_type(
                        db,
                        user.id,
                        request_date_list,
                        meal_type
                    )

                    if has_enterprise_order:
                        conflict_date = existing_reservation_period_start.strftime('%Y-%m-%d')
                        meal_type_name = meal_type.value if meal_type else '未知'
                        conflict_info.append(f"{conflict_date} 的 {meal_type_name}")
                        logger.error(f"用户当日该餐食类型({meal_type_name})已使用企业账户支付，不允许重复支付")

            if conflict_info:
                return {
                    "message": f"存在重复订单，请修改消费日期: {', '.join(conflict_info)} 的订单",
                    "status": 400
                }
            else:
                logger.info(f"用户当日各餐食类型均未使用企业账户支付，允许支付")

            order_dao.update_payment_method(db, order_id, PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE)
            logger.info(f"企业支付成功，支付金额: {task_info['amount']}")
            payment_info = {
                "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
                "enterprise_id": task_info["enterprise_id"]
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)

            # 支付成功
            if paid_order:
                logger.info(f"企业支付成功，支付金额: {task_info['amount']}")
                paid_order_id = paid_order.id
                paid_order_no = paid_order.order_no
                paid_order_user_id = paid_order.user_id
                paid_order_payable_amount = paid_order.payable_amount
                paid_order_status = paid_order.status
                paid_order_payment_status = paid_order.payment_status

                # 更新产品库存 - 使用原始订单ID
                order_service.update_product_stock(db, order_id)
                logger.info(f"产品库存更新成功，订单ID: {order_id}")

                # TODO：企业支付成功，虚拟产品交付
                # 如果是虚拟产品，执行交付

                # 发布订单支付成功事件(企业支付)事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.PAID,
                        order_id=paid_order_id,
                        order_no=paid_order_no,
                        user_id=paid_order_user_id,
                        amount=paid_order_payable_amount,  # 转换为元
                        status=str(paid_order_status),
                        payment_status=str(paid_order_payment_status),
                        source="miniapp_order_payment_by_enterprise",
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"订单支付成功事件(企业支付)已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"发布订单支付成功事件(企业支付)失败: {e}")

                return {
                    "message": "企业支付成功",
                    "status": 200
                }
            else:
                logger.error("企业支付失败")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "企业支付失败", "code": 500}
                )
        # 商务餐企业支付流程
        elif task_info["paymentMethod"] == "biz_enterprise":
            # 商务餐企业支付
            if not task_info["enterprise_id"]:
                logger.error("企业ID为空")
                return {
                    "message": "找不到关联企业",
                    "status": 400
                }
            # 判断该用户是否是该企业的管理员
            enterprise = enterprise_dao.get(db, task_info["enterprise_id"])
            if not enterprise:
                logger.error("企业不存在")
                return {
                    "message": "企业不存在",
                    "status": 400
                }

            # 获取用户与企业的关系
            enterprise_relation = enterprise_user_relation_dao.get_by_personal_user_id(db, user.id)
            is_admin = False
            for relation in enterprise_relation:
                if relation.enterprise_id == task_info["enterprise_id"] and relation.is_admin:
                    is_admin = True
                    break

            if not is_admin:
                logger.error("用户不是企业管理员")
                return {
                    "message": "用户不是企业管理员",
                    "status": 400
                }

            order_dao.update_payment_method(db, order_id, PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE)
            logger.info(f"商务餐企业支付成功，支付金额: {task_info['amount']}")
            payment_info = {
                "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
                "enterprise_id": task_info["enterprise_id"],
                "type": "biz_enterprise"
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            if paid_order:
                logger.info(f"企业支付成功，支付金额: {task_info['amount']}")
                paid_order_id = paid_order.id
                paid_order_no = paid_order.order_no
                paid_order_user_id = paid_order.user_id
                paid_order_payable_amount = paid_order.payable_amount
                paid_order_status = paid_order.status
                paid_order_payment_status = paid_order.payment_status

                # 更新产品库存 - 使用原始订单ID
                order_service.update_product_stock(db, order_id)
                logger.info(f"产品库存更新成功，订单ID: {order_id}")

                # TODO：企业支付成功，虚拟产品交付
                # 如果是虚拟产品，执行交付

                # 发布订单支付成功事件(企业支付)事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.PAID,
                        order_id=paid_order_id,
                        order_no=paid_order_no,
                        user_id=paid_order_user_id,
                        amount=paid_order_payable_amount,  # 转换为元
                        status=str(paid_order_status),
                        payment_status=str(paid_order_payment_status),
                        source="miniapp_biz_order_payment_by_enterprise",
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"商务餐订单支付成功事件(企业支付)已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"发布商务餐订单支付成功事件(企业支付)失败: {e}")

                return {
                    "message": "企业支付成功",
                    "status": 200
                }
            else:
                logger.error("企业支付失败")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "企业支付失败", "code": 500}
                )

        else:
            logger.error("支付方式不支持")
            return {
                "message": "支付方式不支持",
                "status": 400
            }

    except Exception as e:
        logger.error(f"创建订单支付失败: {str(e)}")
        return {
            "message": f"创建订单支付失败",
            "status": 500
        }


@router.post("/order/item/cancel")
async def cancel_order_item(task_info: dict, event_bus: EventBusDep, token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    取消订单中的单个子项

    Args:
        task_info: 包含order_item_id的请求数据
        event_bus: 形参
        token: 用户token
        db: 数据库会话
    """
    try:
        logger.info(f"[订单子项取消] 开始处理，接收到的参数: {task_info}")

        # 验证用户
        logger.info(f"[订单子项取消] 开始验证用户token: {token[:10] if token else 'None'}")
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.error(f"[订单子项取消] 用户token验证失败")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )
        user_id = user.id
        logger.info(f"[订单子项取消] 用户token验证成功，用户ID: {user_id}")

        # 获取订单项ID
        if "order_item_id" not in task_info or "order_no" not in task_info or "order_id" not in task_info:
            logger.error(f"[订单子项取消] 订单项ID为空")
            return {
                "message": "订单项ID不能为空",
                "status": 400
            }

        order_item_id = task_info["order_item_id"]
        order_no = task_info["order_no"]
        order_id = task_info["order_id"]

        # 开始事务
        try:
            # 获取订单项
            order_item = order_item_dao.get(db, order_item_id)
            if not order_item:
                logger.error(f"[订单子项取消] 订单项不存在，订单项ID: {order_item_id}")
                return {
                    "message": "订单项不存在",
                    "status": 404
                }

            # 获取主订单
            order = order_dao.get(db, order_item.order_id)
            if not order:
                logger.error(f"[订单子项取消] 订单不存在，订单ID: {order_item.order_id}")
                return {
                    "message": "订单不存在",
                    "status": 404
                }

            # 判断是否为商务餐订单
            is_business_dining = False
            for reservation_request in order.reservation_requests:
                if hasattr(reservation_request,
                           'type') and reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    is_business_dining = True
                    break

            logger.info(f"[订单子项取消] 订单类型: {'商务餐' if is_business_dining else '自助餐'}")

            # 根据订单类型确定退款金额
            if is_business_dining:
                # 商务餐订单 - 退还整个订单金额
                refund_amount = order.actual_amount_paid
                logger.info(f"[订单子项取消] 商务餐订单，退还整个订单金额: {refund_amount}")
            else:
                # 自助餐订单 - 只退还当前订单项金额
                # 优先使用payable_amount或final_price
                if hasattr(order_item, 'payable_amount') and order_item.payable_amount is not None:
                    refund_amount = order_item.payable_amount
                elif hasattr(order_item, 'final_price') and order_item.final_price is not None:
                    refund_amount = order_item.final_price
                else:
                    refund_amount = order_item.price * order_item.quantity

                logger.info(f"[订单子项取消] 自助餐订单，退还订单项金额: {refund_amount}")

            order_item_status = order_item.status
            order_user_id = order.user_id
            order_payment_method = order.payment_method
            order_actual_amount_paid = order.actual_amount_paid

            # 验证订单支付状态
            if order_item_status != OrderStatus.PAID:
                logger.error(f"[订单子项取消] 订单未支付，无法取消，订单支付状态: {order_item_status}")
                return {
                    "message": "请勿重复申请退款",
                    "status": 400
                }

            # 验证权限
            if order_user_id != user_id:
                logger.error(f"[订单子项取消] 权限验证失败，无权操作此订单")
                return {
                    "message": "无权操作此订单",
                    "status": 403
                }

            # 获取与订单项关联的预约请求
            reservation = reservation_request_dao.get_by_order_item_id_and_user_id(db, order_item_id, user_id)
            if not reservation:
                logger.error(f"[订单子项取消] 预约信息不存在")
                return {
                    "message": "预约信息不存在",
                    "status": 404
                }

            # 获取关联的 rule_item
            rule_item = reservation.rule_item
            if not rule_item:
                logger.error(f"[订单子项取消] 预约规则项不存在")
                return {
                    "message": "预约规则项不存在",
                    "status": 404
                }
            else:
                logger.info(f"[订单子项取消] 预约规则项: {rule_item}")
                cancellation_deadline = rule_item.cancellation_deadline
                logger.info(f"[订单子项取消] 取消截止时间: {cancellation_deadline}")

            # 验证取消时间限制
            reservation_start_time = datetime.strptime('20' + reservation.reservation_period.split("_")[0],
                                                       "%Y%m%d%H%M")
            current_time = datetime.now()

            # 判断当前时间是否已超过取消时间限制
            if cancellation_deadline is not None:
                # 计算取消截止时间
                deadline_date = reservation_start_time
                hours = cancellation_deadline // 60
                minutes = cancellation_deadline % 60
                logger.info(f"取消截止时间 - 小时: {hours}, 分钟: {minutes}")

                cancellation_deadline_time = deadline_date - timedelta(hours=hours, minutes=minutes)
                logger.info(f"就餐开始时间: {deadline_date}")
                logger.info(f"取消截止时间: {cancellation_deadline_time}")

                if current_time > cancellation_deadline_time:
                    logger.info(f"已超过取消截止时间，无法取消预订")
                    return {
                        "message": "已超过取消截止时间，无法取消预订",
                        "status": 500
                    }
            else:
                limit_date = reservation_start_time.replace(hour=22, minute=0, second=0) - timedelta(days=1)
                if current_time > limit_date:
                    logger.info(f"已超过默认取消截止时间（就餐前一天22点），无法取消预订")
                    return {
                        "message": "取消失败，就餐前一天22点后不可取消预约",
                        "status": 500
                    }

            # 验证订单实付金额
            if order_actual_amount_paid < refund_amount:
                logger.error(f"[订单子项取消] 总订单实付金额不足，拒绝退款")
                return {
                    "message": "退款失败，总订单实付金额不足",
                    "status": 500
                }

            cancel_order_id = order_id
            canceled_order_no = order.order_no
            canceled_order_user_id = order.user_id
            canceled_order_amount = order.payable_amount
            canceled_order_status = str(order.status)
            canceled_order_payment_status = str(order.payment_status)
            canceled_order_item_id = order_item.id
            # 开始事务处理
            try:
                # 1. 更新预约状态
                update_reservation_request = reservation_request_dao.cancel_reservation(db, reservation.id)
                if not update_reservation_request:
                    raise Exception("预约状态更新失败")

                # 2. 处理退款
                out_refund_no = f"refund_{order_no}_{order_item_id}"

                # 重新获取order对象，确保session状态正确
                order = order_dao.get(db, order_id)
                if not order:
                    raise Exception("订单不存在")

                order_is_split_payment = order.is_split_payment
                order_enterprise_paid_amount = order.enterprise_paid_amount
                order_personal_paid_amount = order.personal_paid_amount

                # 检查是否为混合支付（分账支付）
                # 注意：混合支付订单的payment_method仍然是ENTERPRISE_ACCOUNT_BALANCE，
                # 但是通过is_split_payment和两个支付金额字段来识别混合支付
                if order_is_split_payment and order_enterprise_paid_amount > 0 and order_personal_paid_amount > 0:
                    logger.info(
                        f"[订单子项取消] 检测到混合支付订单，企业支付: {order_enterprise_paid_amount}, 个人支付: {order_personal_paid_amount}")

                    # 混合支付退款处理：需要分别退款到企业账户和个人账户/微信
                    success = await handle_split_payment_refund(
                        db, order_id, refund_amount, out_refund_no, is_business_dining, order_item_id
                    )

                    if not success:
                        raise Exception("混合支付退款处理失败")

                elif order_payment_method == PaymentMethod.WECHAT_PAY:
                    # 纯微信支付退款
                    if is_business_dining:
                        # 商务餐订单：需要找到所有关联的加菜记录支付记录分别退款
                        logger.info(f"商务餐订单微信支付退款处理 - 订单号: {order_no}")

                        # 获取所有与该订单相关的支付记录
                        payment_records = wx_payment_dao.get_payments_by_order_id(db, order_id)
                        if not payment_records:
                            raise Exception("找不到关联的支付记录")

                        logger.info(f"找到 {len(payment_records)} 条支付记录")

                        # 计算每个支付记录需要退款的金额
                        total_payment_amount = sum(record.total_amount for record in payment_records)
                        if total_payment_amount <= 0:
                            raise Exception("支付记录总金额为0，无法退款")

                        # 使用分（整数）进行计算，避免浮点数精度问题
                        total_payment_amount_cents = int(total_payment_amount * 100)
                        refund_amount_cents = int(refund_amount * 100)

                        logger.info(f"退款计算 - 总支付金额: {total_payment_amount}元 ({total_payment_amount_cents}分), 退款金额: {refund_amount}元 ({refund_amount_cents}分)")

                        # 按比例分配退款金额
                        refunded_amount_cents = 0
                        for i, payment_record in enumerate(payment_records):
                            # 计算当前支付记录应退款的金额（以分为单位）
                            if i == len(payment_records) - 1:
                                # 最后一个支付记录处理剩余金额，避免精度问题
                                current_refund_amount_cents = refund_amount_cents - refunded_amount_cents
                                logger.info(f"支付记录 {i+1} (最后一条) - 剩余退款金额: {current_refund_amount_cents}分")
                            else:
                                # 按比例计算，但确保精度
                                payment_amount_cents = int(payment_record.total_amount * 100)
                                current_refund_amount_cents = int(refund_amount_cents * payment_amount_cents / total_payment_amount_cents)
                                logger.info(f"支付记录 {i+1} - 按比例计算: {payment_amount_cents}分 / {total_payment_amount_cents}分 * {refund_amount_cents}分 = {current_refund_amount_cents}分")

                            # 转换为元
                            current_refund_amount = round(current_refund_amount_cents / 100, 2)

                            if current_refund_amount <= 0:
                                continue

                            # 确保退款金额不超过支付记录的实际金额
                            if current_refund_amount > payment_record.total_amount:
                                logger.warning(f"支付记录 {i+1} 退款金额 {current_refund_amount} 超过支付金额 {payment_record.total_amount}，调整为支付金额")
                                current_refund_amount = payment_record.total_amount

                            logger.info(f"支付记录 {i+1}: 交易号 {payment_record.transaction_id}, 支付金额 {payment_record.total_amount}, 退款金额 {current_refund_amount}")

                            # 为每个支付记录创建独立的退款单号
                            current_out_refund_no = f"{out_refund_no}_part_{i+1}"

                            try:
                                refund_result = wechat_service.create_refund(
                                    payment_record.transaction_id,
                                    current_out_refund_no,
                                    payment_record.total_amount,
                                    current_refund_amount,
                                    f"商务餐订单「{order_no}」退款 - 第{i+1}部分"
                                )

                                if not refund_result:
                                    logger.error(f"微信退款失败 - 支付记录 {i+1}, 交易号: {payment_record.transaction_id}, 退款金额: {current_refund_amount}")
                                    raise Exception(f"微信退款失败 - 支付记录 {i+1}")

                                logger.info(f"微信退款成功 - 支付记录 {i+1}, 退款金额: {current_refund_amount}, 微信退款单号: {refund_result.get('refund_id', 'unknown')}")
                                # 更新已退款金额（以分为单位）
                                refunded_amount_cents += int(current_refund_amount * 100)

                            except Exception as e:
                                logger.error(f"微信退款处理异常 - 支付记录 {i+1}, 错误: {str(e)}")
                                raise Exception(f"微信退款处理失败 - 支付记录 {i+1}: {str(e)}")

                        total_refunded_amount = round(refunded_amount_cents / 100, 2)
                        logger.info(f"商务餐订单微信支付退款处理完成，总退款金额: {total_refunded_amount}")

                        # 验证退款金额是否正确
                        if abs(total_refunded_amount - refund_amount) > 0.01:
                            logger.warning(f"退款金额不匹配 - 期望: {refund_amount}元, 实际: {total_refunded_amount}元")
                        else:
                            logger.info(f"退款金额验证通过 - 期望: {refund_amount}元, 实际: {total_refunded_amount}元")

                    else:
                        # 自助餐订单：原有的退款逻辑
                        payment_record = wx_payment_dao.get_payment_by_order_no(db, order_no)
                        if not payment_record:
                            raise Exception("支付记录不存在")

                        logger.info(f"支付交易号: {payment_record.transaction_id}")
                        logger.info(f"退款单号: {out_refund_no}")
                        logger.info(f"订单金额: {payment_record.total_amount}")
                        logger.info(f"退款金额: {refund_amount}")

                        try:
                            refund_result = wechat_service.create_refund(
                                payment_record.transaction_id,
                                out_refund_no,
                                payment_record.total_amount,
                                refund_amount,
                                f"订单「{order_no}」部分商品退款"
                            )

                            if not refund_result:
                                # 记录更详细的错误信息
                                logger.error(f"微信退款失败 - 订单号: {order_no}, 退款金额: {refund_amount}, 支付交易号: {payment_record.transaction_id}")
                                raise Exception(f"微信退款失败 - 订单号: {order_no}")

                            logger.info(f"微信退款成功 - 订单号: {order_no}, 退款金额: {refund_amount}, 微信退款单号: {refund_result.get('refund_id', 'unknown')}")

                        except Exception as e:
                            logger.error(f"微信退款处理异常 - 订单号: {order_no}, 错误: {str(e)}")
                            raise Exception(f"微信退款处理失败: {str(e)}")

                elif order_payment_method == PaymentMethod.ACCOUNT_BALANCE:
                    # 纯个人账户余额退款
                    accounts = account_dao.get_by_user_id(db, user.id)
                    regular_account = next((account for account in accounts if account.type == AccountType.REGULAR),
                                           None)

                    if not regular_account:
                        raise Exception("用户账户不存在")

                    # 更新账户余额
                    regular_account.balance += refund_amount

                    # 创建退款交易记录
                    refund_transaction = AccountTransactionCreate(
                        account_id=regular_account.id,
                        order_id=order_id,
                        transaction_type=TransactionType.REFUND,
                        amount=refund_amount,
                        description=f"{'订单退款' if is_business_dining else '订单部分退款'}：{order_id}-{order_item_id}",
                        transaction_time=get_current_time()
                    )
                    account_transaction_dao.create(db, refund_transaction)

                elif order_payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                    # 纯企业账户退款（非混合支付）
                    account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
                    if not account_transactions:
                        raise Exception("企业账户交易记录不存在")

                    account_id = account_transactions[0].account_id
                    accounts = account_dao.get_by_id(db, account_id)
                    regular_account = next((account for account in accounts if account.type == AccountType.REGULAR),
                                           None)

                    if not regular_account:
                        raise Exception("企业账户不存在")

                    # 更新企业账户余额
                    regular_account.balance += refund_amount

                    # 创建退款交易记录
                    refund_transaction = AccountTransactionCreate(
                        account_id=regular_account.id,
                        order_id=order_id,
                        transaction_type=TransactionType.REFUND,
                        amount=refund_amount,
                        description=f"{'订单退款' if is_business_dining else '订单部分退款'}：{order_id}-{order_item_id}",
                        transaction_time=get_current_time()
                    )
                    account_transaction_dao.create(db, refund_transaction)

                # 3. 更新订单项状态和订单状态
                if is_business_dining:
                    # 商务餐订单 - 取消所有订单项并更新订单状态为已退款
                    # 重新获取订单对象，因为之前的可能已经被修改
                    order = order_dao.get(db, order_id)
                    if not order:
                        raise Exception("订单不存在或已被删除")

                    # 获取所有订单项
                    order_items = order_item_dao.get_by_order(db, order_id)
                    for item in order_items:
                        item.status = OrderStatus.REFUNDED

                    # 更新订单状态
                    order.status = OrderStatus.REFUNDED
                    order.payment_status = PaymentStatus.REFUNDED
                else:
                    # 自助餐订单 - 只取消当前订单项
                    # 重新获取订单项，因为之前的可能已经被修改
                    order_item = order_item_dao.get(db, order_item_id)
                    if not order_item:
                        raise Exception("订单项不存在或已被删除")

                order_item.status = OrderStatus.REFUNDED

                # 更新订单总金额和状态（非微信支付）
                if order_payment_method != PaymentMethod.WECHAT_PAY:
                    updated_order = order_dao.get(db, order_id)
                    if updated_order:
                        updated_order.actual_amount_paid -= refund_amount

                        # 检查所有订单项状态
                        order_items = order_item_dao.get_by_order(db, order_id)
                        all_items_refunded = all(item.status == OrderStatus.REFUNDED for item in order_items)

                        # 更新订单状态
                        updated_order.status = OrderStatus.REFUNDED if all_items_refunded else OrderStatus.REFUNDED_PARTIAL

                # 提交事务
                db.commit()
                logger.info(f"[订单子项取消] 事务处理成功")

                # 发布订单取消成功事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.CANCELLED,
                        order_id=cancel_order_id,
                        order_no=canceled_order_no,
                        user_id=canceled_order_user_id,
                        amount=canceled_order_amount,  # 转换为元
                        status=canceled_order_status,
                        payment_status=canceled_order_payment_status,
                        source="miniapp_order_cancel",
                        additional_data={
                            "is_business_dining": is_business_dining,
                            "order_item_id": canceled_order_item_id
                        }
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"订单取消成功事件已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"订单取消成功事件失败: {e}")
                return {
                    "message": "取消成功",
                    "status": 200
                }

            except Exception as e:
                logger.error(f"[订单子项取消] 处理异常: {str(e)}", exc_info=True)
                # 回滚事务
                db.rollback()
                return {
                    "message": f"取消订单项失败: {str(e)}",
                    "status": 500
                }

        except Exception as e:
            logger.error(f"[订单子项取消] 处理异常: {str(e)}", exc_info=True)
            return {
                "message": f"取消订单项失败: {str(e)}",
                "status": 500
            }

    except Exception as e:
        logger.error(f"[订单子项取消] 处理异常: {str(e)}", exc_info=True)
        return {
            "message": f"取消订单项失败: {str(e)}",
            "status": 500
        }
